import { create } from 'zustand';
import { Pokemon, League, PokemonFilters, LoadingState } from '@/types';
import { pokemonService } from '@/services/pokemonService';

interface PokemonState {
  // State
  allPokemon: Pokemon[];
  filteredPokemon: Pokemon[];
  selectedLeague: League;
  searchQuery: string;
  filters: PokemonFilters;
  loadingState: LoadingState<Pokemon[]>;
  
  // Actions
  loadPokemon: () => Promise<void>;
  filterPokemon: (filters: Partial<PokemonFilters>) => void;
  setLeague: (league: League) => void;
  setSearchQuery: (query: string) => void;
  clearFilters: () => void;
  refreshPokemon: () => Promise<void>;
}

const defaultFilters: PokemonFilters = {
  types: undefined,
  leagues: undefined,
  searchQuery: '',
  tags: undefined,
};

export const usePokemonStore = create<PokemonState>((set, get) => ({
  // Initial state
  allPokemon: [],
  filteredPokemon: [],
  selectedLeague: 'great',
  searchQuery: '',
  filters: defaultFilters,
  loadingState: { status: 'idle' },
  
  // Actions
  loadPokemon: async () => {
    const { loadingState } = get();
    
    // Don't load if already loading or loaded
    if (loadingState.status === 'loading' || 
        (loadingState.status === 'success' && loadingState.data.length > 0)) {
      return;
    }
    
    set({ loadingState: { status: 'loading' } });
    
    try {
      const pokemon = await pokemonService.getAllPokemon();
      
      set({
        allPokemon: pokemon,
        filteredPokemon: pokemon,
        loadingState: { status: 'success', data: pokemon },
      });
      
      // Apply current filters
      get().filterPokemon({});
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load Pokemon';
      set({ loadingState: { status: 'error', error: errorMessage } });
    }
  },
  
  filterPokemon: (newFilters) => {
    const { allPokemon, filters, searchQuery } = get();
    
    const updatedFilters = { ...filters, ...newFilters };
    
    let filtered = [...allPokemon];
    
    // Apply search query
    const query = updatedFilters.searchQuery || searchQuery;
    if (query.trim()) {
      const lowercaseQuery = query.toLowerCase();
      filtered = filtered.filter(pokemon =>
        pokemon.speciesName.toLowerCase().includes(lowercaseQuery) ||
        pokemon.speciesId.toLowerCase().includes(lowercaseQuery) ||
        pokemon.types.some(type => type.toLowerCase().includes(lowercaseQuery)) ||
        pokemon.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    }
    
    // Apply type filters
    if (updatedFilters.types && updatedFilters.types.length > 0) {
      filtered = filtered.filter(pokemon =>
        updatedFilters.types!.some(type => pokemon.types.includes(type))
      );
    }
    
    // Apply tag filters
    if (updatedFilters.tags && updatedFilters.tags.length > 0) {
      filtered = filtered.filter(pokemon =>
        updatedFilters.tags!.some(tag => pokemon.tags.includes(tag))
      );
    }
    
    // Apply league filters (this would need CP calculations)
    if (updatedFilters.leagues && updatedFilters.leagues.length > 0) {
      // TODO: Implement league filtering based on CP limits
      // For now, just keep all Pokemon
    }
    
    set({
      filteredPokemon: filtered,
      filters: updatedFilters,
      searchQuery: query,
    });
  },
  
  setLeague: (league) => {
    set({ selectedLeague: league });
    // Reapply filters for the new league
    get().filterPokemon({ leagues: [league] });
  },
  
  setSearchQuery: (query) => {
    set({ searchQuery: query });
    get().filterPokemon({ searchQuery: query });
  },
  
  clearFilters: () => {
    const { allPokemon } = get();
    set({
      filters: defaultFilters,
      searchQuery: '',
      filteredPokemon: allPokemon,
    });
  },
  
  refreshPokemon: async () => {
    set({ 
      allPokemon: [],
      filteredPokemon: [],
      loadingState: { status: 'idle' }
    });
    await get().loadPokemon();
  },
}));
