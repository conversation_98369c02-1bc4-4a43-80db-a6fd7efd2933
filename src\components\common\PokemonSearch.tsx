import { useState, useEffect, useRef } from 'react';
import { Pokemon } from '@/types/pokemon';
import clsx from 'clsx';

interface PokemonSearchProps {
  onSelect: (pokemon: Pokemon) => void;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
}

// Mock Pokemon data for demonstration
const mockPokemon: Pokemon[] = [
  {
    speciesId: 'bulbasaur',
    speciesName: 'Bulbasaur',
    dex: 1,
    types: ['grass', 'poison'],
    baseStats: { attack: 118, defense: 111, stamina: 128 },
    fastMoves: [{ moveId: 'vine_whip', name: '<PERSON>e Whip', type: 'grass', power: 7, energyGain: 6, cooldown: 0.5 }],
    chargedMoves: [{ moveId: 'sludge_bomb', name: 'Sludge Bomb', type: 'poison', power: 80, energy: 50, cooldown: 2.3 }],
    tags: [],
    buddyDistance: 3,
    thirdMoveCost: 10000,
    released: true,
  },
  {
    speciesId: 'charmander',
    speciesName: 'Charmander',
    dex: 4,
    types: ['fire'],
    baseStats: { attack: 116, defense: 93, stamina: 118 },
    fastMoves: [{ moveId: 'ember', name: 'Ember', type: 'fire', power: 10, energyGain: 6, cooldown: 1 }],
    chargedMoves: [{ moveId: 'flamethrower', name: 'Flamethrower', type: 'fire', power: 90, energy: 55, cooldown: 2.2 }],
    tags: [],
    buddyDistance: 3,
    thirdMoveCost: 10000,
    released: true,
  },
  {
    speciesId: 'squirtle',
    speciesName: 'Squirtle',
    dex: 7,
    types: ['water'],
    baseStats: { attack: 94, defense: 121, stamina: 127 },
    fastMoves: [{ moveId: 'water_gun', name: 'Water Gun', type: 'water', power: 5, energyGain: 5, cooldown: 0.5 }],
    chargedMoves: [{ moveId: 'hydro_pump', name: 'Hydro Pump', type: 'water', power: 130, energy: 75, cooldown: 3.3 }],
    tags: [],
    buddyDistance: 3,
    thirdMoveCost: 10000,
    released: true,
  },
];

export function PokemonSearch({
  onSelect,
  placeholder = 'Search for a Pokemon',
  value,
  onChange,
  disabled = false,
}: PokemonSearchProps) {
  const [searchQuery, setSearchQuery] = useState(value || '');
  const [results, setResults] = useState<Pokemon[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Update internal state when external value changes
  useEffect(() => {
    if (value !== undefined) {
      setSearchQuery(value);
    }
  }, [value]);

  // Search for Pokemon when query changes
  useEffect(() => {
    if (!searchQuery) {
      setResults([]);
      return;
    }

    // In a real implementation, this would call an API or search a local database
    const filteredResults = mockPokemon.filter((pokemon) =>
      pokemon.speciesName.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setResults(filteredResults);
    setSelectedIndex(-1);
  }, [searchQuery]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchQuery(newValue);
    if (onChange) {
      onChange(newValue);
    }
    setIsOpen(true);
  };

  const handleSelectPokemon = (pokemon: Pokemon) => {
    onSelect(pokemon);
    setSearchQuery('');
    setIsOpen(false);
    setResults([]);
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex((prev) => (prev < results.length - 1 ? prev + 1 : prev));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleSelectPokemon(results[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        break;
    }
  };

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="text"
        value={searchQuery}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => setIsOpen(true)}
        onBlur={() => setTimeout(() => setIsOpen(false), 200)}
        placeholder={placeholder}
        disabled={disabled}
        className={clsx(
          'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-colors',
          disabled
            ? 'bg-gray-100 text-gray-400 border-gray-200'
            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
        )}
      />

      {isOpen && results.length > 0 && (
        <div
          ref={resultsRef}
          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          {results.map((pokemon, index) => (
            <div
              key={pokemon.speciesId}
              className={clsx(
                'flex items-center p-2 cursor-pointer hover:bg-gray-100',
                selectedIndex === index && 'bg-blue-50'
              )}
              onClick={() => handleSelectPokemon(pokemon)}
            >
              <img
                src={`/images/pokemon/${pokemon.speciesId}.png`}
                alt={pokemon.speciesName}
                className="w-8 h-8 mr-2"
                onError={(e) => {
                  e.currentTarget.src = '/images/pokemon/0.png';
                }}
              />
              <div>
                <div className="font-medium">{pokemon.speciesName}</div>
                <div className="text-xs text-gray-500">
                  {pokemon.types.map((type) => type.charAt(0).toUpperCase() + type.slice(1)).join(', ')}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}