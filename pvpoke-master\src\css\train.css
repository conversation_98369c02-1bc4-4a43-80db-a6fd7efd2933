/* MIXINS */
.multi .quick-fill-select option.multi-battle {
  display: none !important;
}
.multi .cup-select, .multi .format-select {
  display: none;
}

.train .poke {
  display: block;
  width: 100%;
}
.train .poke:first-of-type {
  float: left;
}
.train .poke:last-of-type {
  float: right;
}
.train .poke.ai-options {
  float: right;
}
.train .poke h3:first-of-type {
  margin: 0;
}
.train .poke .options {
  display: none;
}
.train .poke .custom-options,
.train .poke .poke-stats {
  display: block;
}
.train .poke .section-title,
.train .poke .rank {
  text-align: left;
}
.train .poke p {
  text-align: left;
  font-size: 12px;
}
.train .featured-team-section {
  margin-top: 10px;
  display: none;
}
.train .custom-team-section {
  display: none;
}
.train .custom-team-section .team-import {
  width: 95%;
  display: block;
}
.train .custom-team-section .custom-team-validation {
  border-radius: 8px;
  padding: 8px;
  font-size: 12px;
  text-align: left;
  display: none;
  margin-top: 5px;
}
.train .custom-team-section .custom-team-validation.true {
  border: 1px solid #0eb084;
  color: #0eb084;
}
.train .custom-team-section .custom-team-validation.false {
  border: 1px solid #ff5115;
  color: #ff5115;
}

.editor .poke.multi {
  float: none;
}
.editor .poke.multi .poke-stats {
  background: none;
}
.editor .multi-selector {
  display: none;
  border-top: 1px solid;
  padding-top: 20px;
  margin-top: 20px;
}
.editor .multi-selector[mode=new] .button.add-team {
  display: block;
}
.editor .multi-selector[mode=new] .button.save-changes {
  display: none;
}
.editor .multi-selector[mode=edit] .button.add-team {
  display: none;
}
.editor .multi-selector[mode=edit] .button.save-changes {
  display: block;
}
.editor .multi-selector .poke h3:nth-of-type(2) {
  display: none;
}
.editor .multi-selector .poke .quick-fill-select {
  display: none;
}
.editor .multi-selector .poke .quick-fill-buttons {
  display: none;
}
.editor .multi-selector .pokebox {
  display: none !important;
}
.editor .button.new-team,
.editor .button.add-team,
.editor .button.save-changes {
  margin: 10px 0;
}
.editor .table-container {
  max-height: 800px;
}

.training-editor-import h3 {
  margin-top: 0;
}
.training-editor-import textarea.import {
  width: 100%;
  height: 100px;
}
.training-editor-import .copy {
  display: inline-block;
  cursor: pointer;
  font-weight: bold;
  background: #004c66;
  color: #eee;
  padding: 5px;
  border-radius: 8px;
  margin: 5px 0;
}
.training-editor-import .team-fill-select {
  max-width: 200px;
  margin-bottom: 10px;
}
.training-editor-import .team-fill-buttons button {
  margin-right: 10px;
}

.featured-team-description {
  display: none;
  text-align: center;
}
.featured-team-description img {
  max-width: 75px;
  margin: 0 auto;
}
.featured-team-description a h3 {
  color: #003748;
  margin-top: 0;
  font-size: 16px;
  font-weight: bold !important;
  text-decoration: underline;
}
.featured-team-description h5 {
  font-size: 14px;
  text-align: left;
  margin: 10px 0 5px 0;
}
.featured-team-description .featured-team-preview {
  padding: 5px;
  border: 1px solid;
  border-radius: 12px;
}
.featured-team-description .featured-team-preview .preview-poke {
  display: flex;
  margin-bottom: 5px;
}
.featured-team-description .featured-team-preview .preview-poke:last-of-type {
  margin-bottom: 0;
}
.featured-team-description .featured-team-preview .preview-poke .sprite-container {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  padding: 0;
  margin-right: 10px;
}
.featured-team-description .featured-team-preview .preview-poke h6, .featured-team-description .featured-team-preview .preview-poke p {
  margin: 0;
}
.featured-team-description .featured-team-preview .preview-poke h6 {
  text-align: left;
  font-size: 14px;
}
.featured-team-description .featured-team-preview .preview-poke p {
  font-size: 12px;
}

.ai-options .poke.multi {
  float: none;
  display: none;
}
.ai-options .poke.multi .poke-stats {
  background: none;
  padding: 0;
}

.poke.ai-options a.inline-link {
  font-size: 12px;
  font-weight: bold;
  text-decoration: underline;
  margin: 0;
  display: inline;
}
.poke.ai-options a.inline-link.train-editor-link {
  display: block;
  text-align: left;
  margin-top: 10px;
}

.autotap-toggle {
  font-size: 14px;
  text-align: left;
}

a.random {
  margin-top: 5px;
}

.section.battle {
  text-align: center;
}

.battle-active {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -moz-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -khtml-user-select: none;
  /* Konqueror HTML */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
     supported by Chrome and Opera */
}
.battle-active .battle-window {
  display: inline-block;
}
.battle-active header {
  display: none;
}
.battle-active #main {
  padding-top: 0;
}

.battle-window {
  display: none;
  background: rgba(255, 255, 255, 0.75);
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  box-shadow: 0px 5px 15px;
  cursor: pointer;
  touch-action: manipulation;
}
.battle-window .img-block {
  visibility: hidden;
  max-width: 100%;
}
.battle-window .top {
  padding-top: 5px;
  text-align: center;
  position: absolute;
  width: 100%;
  top: 0;
  z-index: 10;
}
.battle-window .top .timer {
  display: none;
  color: #eee;
  width: 60px;
  height: 60px;
  border-radius: 60px;
  background: rgba(0, 0, 0, 0.75);
  margin: 0 auto;
}
.battle-window .top .timer .text {
  display: table-cell;
  vertical-align: middle;
  font-size: 28px;
}
.battle-window .top .shields {
  background-size: contain;
  background-image: url("../img/shield.png");
  background-repeat: no-repeat;
  padding-left: 24px;
  font-size: 14px;
  height: 15px;
  margin-top: 2px;
}
.battle-window .top .shields.left {
  left: 6%;
}
.battle-window .top .shields.right {
  right: 6%;
}
.battle-window .top .shields.no-shields {
  color: #ff0000;
}
.battle-window .top .team-indicator {
  position: absolute;
  background: rgba(255, 255, 255, 0.75);
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  top: 25px;
  padding: 3px;
  max-width: 35%;
  justify-content: space-between;
}
.battle-window .top .team-indicator.left {
  left: 2.5%;
}
.battle-window .top .team-indicator.left .cmp, .battle-window .top .team-indicator.left .types {
  left: 0;
}
.battle-window .top .team-indicator.left .type {
  margin-left: 5px;
}
.battle-window .top .team-indicator.right {
  right: 2.5%;
}
.battle-window .top .team-indicator.right .cp {
  text-align: left;
}
.battle-window .top .team-indicator.right .name {
  text-align: right;
}
.battle-window .top .team-indicator.right .cmp, .battle-window .top .team-indicator.right .types {
  right: 0;
}
.battle-window .top .team-indicator.right .type {
  margin-right: 5px;
}
.battle-window .top .team-indicator .name, .battle-window .top .team-indicator .cp, .battle-window .top .team-indicator .balls {
  width: 50%;
  font-size: 12px;
  font-weight: bold;
  text-align: left;
}
.battle-window .top .team-indicator .cp {
  text-align: right;
}
.battle-window .top .team-indicator .balls {
  display: flex;
}
.battle-window .top .team-indicator .ball {
  width: 14px;
  height: 14px;
  border-radius: 40px;
  background: #ff0000;
  margin: 3px 3px;
}
.battle-window .top .team-indicator .ball.fainted {
  background: #000;
}
.battle-window .top .team-indicator .cmp {
  position: absolute;
  top: 110%;
  font-weight: bold;
  font-size: 12px;
  padding: 5px;
  background: rgba(0, 0, 0, 0.25);
  color: #eee;
  border-radius: 12px;
}
.battle-window .top .team-indicator .types {
  position: absolute;
  display: flex;
  top: -20px;
}
.battle-window .top .team-indicator .types .type {
  width: 18px;
  height: 18px;
  border-radius: 18px;
  font-weight: bold;
  color: #fff;
  font-size: 12px;
  line-height: 20px;
  text-transform: capitalize;
}
.battle-window .top .team-indicator .types .type.none {
  display: none;
}
.battle-window .top .team-indicator .featured-team-description {
  position: absolute;
  top: 190%;
  right: 0;
}
.battle-window .top .team-indicator .featured-team-description img {
  max-width: 45px;
}
.battle-window .scene {
  position: absolute;
  width: 150%;
  height: 150%;
  left: -25%;
  top: -25%;
  -webkit-transition: all 0.5s ease 0s;
  -moz-transition: all 0.5s ease 0s;
  -o-transition: all 0.5s ease 0s;
  transition: all 0.5s ease 0s;
}
.battle-window .scene .background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../img/train/pvp-bg.jpg");
  background-repeat: no-repeat;
  background-position: center bottom;
}
.battle-window .scene .pokemon-container {
  position: absolute;
  width: 37.5%;
}
.battle-window .scene .pokemon-container.self {
  bottom: 30%;
  left: 20%;
  z-index: 10;
}
.battle-window .scene .pokemon-container.self .pokemon {
  -moz-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
  transform: scaleX(-1);
}
.battle-window .scene .pokemon-container.self .pokemon .main-sprite, .battle-window .scene .pokemon-container.self .pokemon .secondary-sprite {
  z-index: 10;
}
.battle-window .scene .pokemon-container.self .pokemon .shadow {
  z-index: 20;
}
.battle-window .scene .pokemon-container.self .fast-move-circle-container {
  top: -10px;
  left: -70px;
}
.battle-window .scene .pokemon-container.opponent {
  bottom: 52%;
  right: 25%;
}
.battle-window .scene .pokemon-container.opponent .shield-sprite {
  z-index: 20;
}
.battle-window .scene .pokemon-container.opponent .fast-move-circle-container {
  top: 25px;
  left: -60px;
  z-index: 25;
}
.battle-window .scene .pokemon-container h3 {
  display: inline-block;
  padding: 5px;
  margin: 0 0 5px 0;
  line-height: 18px;
  border-radius: 12px;
  text-align: center;
}
.battle-window .scene .pokemon-container .messages {
  text-align: center;
}
.battle-window .scene .pokemon-container .pokemon {
  -webkit-transition: all 1s ease 0s;
  -moz-transition: all 1s ease 0s;
  -o-transition: all 1s ease 0s;
  transition: all 1s ease 0s;
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto;
}
.battle-window .scene .pokemon-container .pokemon .fast-move-circle-container {
  position: absolute;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.battle-window .scene .pokemon-container .pokemon .fast-move-circle {
  width: 100px;
  height: 100px;
  border-radius: 300px;
  opacity: 0;
}
.battle-window .scene .pokemon-container .pokemon .shadow {
  width: 151px;
  height: 62px;
  background: url("../img/train/sprite-shadow.png");
  background-size: 100%;
  background-repeat: no-repeat;
  position: absolute;
  z-index: 5;
  top: 65px;
  left: -25px;
  opacity: 0.45;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container {
  position: absolute;
  top: 0;
  left: 0;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container.active .shield-sprite {
  opacity: 0.85;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container .shield-sprite {
  width: 65px;
  height: 55px;
  background: url("../img/train/shield.png");
  background-size: cover;
  background-repeat: no-repeat;
  position: absolute;
  opacity: 0;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container .shield-sprite:nth-of-type(1) {
  -webkit-transition: all 0.75s ease 0s;
  -moz-transition: all 0.75s ease 0s;
  -o-transition: all 0.75s ease 0s;
  transition: all 0.75s ease 0s;
  top: -15px;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container .shield-sprite:nth-of-type(2) {
  -webkit-transition: all 0.75s ease 0.05s;
  -moz-transition: all 0.75s ease 0.05s;
  -o-transition: all 0.75s ease 0.05s;
  transition: all 0.75s ease 0.05s;
  top: 15px;
  left: 52px;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container .shield-sprite:nth-of-type(3) {
  -webkit-transition: all 0.75s ease 0.15s;
  -moz-transition: all 0.75s ease 0.15s;
  -o-transition: all 0.75s ease 0.15s;
  transition: all 0.75s ease 0.15s;
  top: 45px;
}
.battle-window .scene .pokemon-container .pokemon .shield-sprite-container .shield-sprite:nth-of-type(4) {
  -webkit-transition: all 0.75s ease 0.2s;
  -moz-transition: all 0.75s ease 0.2s;
  -o-transition: all 0.75s ease 0.2s;
  transition: all 0.75s ease 0.2s;
  top: 15px;
  left: -52px;
}
.battle-window .scene .pokemon-container .hp.bar-back {
  outline: 2px solid #ccc;
  border: none;
  height: 15px;
  width: 90%;
  margin: 0 auto 20px auto;
  -webkit-transition: all 0.4s ease 0s;
  -moz-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}
.battle-window .scene .pokemon-container .hp.bar-back.super-effective, .battle-window .scene .pokemon-container .hp.bar-back.effective, .battle-window .scene .pokemon-container .hp.bar-back.not-very-effective {
  -webkit-transition: all 0.05s ease 0s;
  -moz-transition: all 0.05s ease 0s;
  -o-transition: all 0.05s ease 0s;
  transition: all 0.05s ease 0s;
}
.battle-window .scene .pokemon-container .hp.bar-back.super-effective {
  outline: 4px solid #ea0404;
}
.battle-window .scene .pokemon-container .hp.bar-back.effective {
  outline: 2px solid #f57a23;
}
.battle-window .scene .pokemon-container .hp.bar-back.not-very-effective {
  outline: 2px solid #2172ea;
}
.battle-window .scene .pokemon-container .hp.bar-back .bar {
  position: relative;
  top: -11px;
  background-color: #31e8b7;
  -webkit-transition: width 0.2s ease 0s;
  -moz-transition: width 0.2s ease 0s;
  -o-transition: width 0.2s ease 0s;
  transition: width 0.2s ease 0s;
}
.battle-window .scene .pokemon-container .hp.bar-back .bar.damage {
  background: #ff5115;
  position: relative;
  top: 0px;
  -webkit-transition: width 0.2s ease 0.5s;
  -moz-transition: width 0.2s ease 0.5s;
  -o-transition: width 0.2s ease 0.5s;
  transition: width 0.2s ease 0.5s;
}
.battle-window .scene .pokemon-container .hp.bar-back .bar[color=yellow] {
  background: #eabd49;
}
.battle-window .scene .pokemon-container .hp.bar-back .bar[color=red] {
  background: #e11414;
}
.battle-window .controls {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40%;
  background: rgba(0, 0, 0, 0.85);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.85) 99%) !important;
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.85) 99%) !important;
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.85) 99%) !important;
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.battle-window .controls .move-bars {
  position: absolute;
  bottom: 25%;
  width: 100%;
  z-index: 15;
}
.battle-window .controls .move-bars .move-bar {
  display: block;
  width: 70px;
  height: 70px;
  -webkit-transition: all 0.2s ease 0s;
  -moz-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
}
.battle-window .controls .move-bars .move-bar .bar {
  -webkit-transition: top 0.2s ease-out 0s;
  -moz-transition: top 0.2s ease-out 0s;
  -o-transition: top 0.2s ease-out 0s;
  transition: top 0.2s ease-out 0s;
  bottom: 0;
  background: url(../img/train/type-gradients.jpg);
  background-repeat: no-repeat;
}
.battle-window .controls .move-bars .move-bar .bar-back {
  border-radius: 40px;
}
.battle-window .controls .move-bars .move-bar .label {
  font-size: 24px;
  padding-top: 18px;
  pointer-events: none;
}
.battle-window .controls .move-bars .move-bar.active {
  border: 3px solid #000;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.5);
  -moz-transform: scale(1.25, 1.25);
  -webkit-transform: scale(1.25, 1.25);
  -o-transform: scale(1.25, 1.25);
  -ms-transform: scale(1.25, 1.25);
  transform: scale(1.25, 1.25);
}
.battle-window .controls .move-bars .move-bar.active:active {
  -moz-transform: scale(1.15, 1.15);
  -webkit-transform: scale(1.15, 1.15);
  -o-transform: scale(1.15, 1.15);
  -ms-transform: scale(1.15, 1.15);
  transform: scale(1.15, 1.15);
}
.battle-window .controls .move-labels {
  position: absolute;
  bottom: 62%;
  width: 100%;
  display: flex;
  justify-content: space-around;
  z-index: 15;
}
.battle-window .controls .move-labels .label {
  -webkit-transition: all 0.2s ease 0s;
  -moz-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  width: 50%;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.75);
  text-transform: uppercase;
  font-size: 12px;
  padding: 1px 0;
}
.battle-window .controls .move-labels .label.active {
  color: white;
  text-shadow: 3px 3px 5px #000;
  font-size: 14px;
  padding: 0;
}
.battle-window .controls .switch-btn {
  width: 40px;
  height: 35px;
  border-radius: 40px;
  background: rgba(0, 0, 0, 0.5);
  border: 2px solid #000;
  text-align: center;
  padding-top: 5px;
  font-size: 24px;
  font-weight: bold;
  position: absolute;
  z-index: 25;
  bottom: 5%;
  right: 5%;
  -webkit-text-fill-color: #eee;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #000;
}
.battle-window .controls .switch-btn.active {
  background: #eee;
}
.battle-window .controls .auto-tap-container {
  position: absolute;
  width: 100%;
  bottom: 10px;
}
.battle-window .controls .auto-tap-container .auto-tap {
  -webkit-transition: all 0.2s ease 0s;
  -moz-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  display: inline-block;
  background: #888;
  padding: 5px;
  border-radius: 12px;
  font-weight: bold;
}
.battle-window .controls .auto-tap-container .auto-tap.active {
  background: #ee2222;
  color: #eee;
}
.battle-window .controls .button-stack {
  -webkit-transition: all 0.2s ease 0s;
  -moz-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  position: absolute;
  background: rgba(255, 255, 255, 0.85);
  padding: 5px 2px;
  border-radius: 8px;
  left: 2.5%;
  top: -80%;
  z-index: 15;
}
.battle-window .controls .button-stack div {
  width: 16px;
  height: 19px;
  margin: 10px 0;
  background-position: center center;
  background-repeat: no-repeat;
}
.battle-window .controls .button-stack div:first-of-type {
  margin-top: 0;
}
.battle-window .controls .button-stack div:last-of-type {
  margin-bottom: 0;
}
.battle-window .controls .button-stack .pause-btn {
  background-image: url("../img/playback_pause.png");
}
.battle-window .controls .button-stack .pause-btn.active {
  background-image: url("../img/playback_play.png");
}
.battle-window .controls .button-stack .restart-btn {
  background-image: url("../img/playback_replay.png");
}
.battle-window .shield-window {
  width: 100%;
  height: 40%;
  position: absolute;
  left: 0;
  bottom: 0;
  opacity: 0;
  pointer-events: none;
  -webkit-transition: all 0.25s ease 0s;
  -moz-transition: all 0.25s ease 0s;
  -o-transition: all 0.25s ease 0s;
  transition: all 0.25s ease 0s;
}
.battle-window .shield-window.closed {
  pointer-events: none;
  bottom: -50%;
}
.battle-window .shield-window .container .close {
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
}
.battle-window .shield-window .container .shield {
  width: 109px;
  height: 94px;
  margin: 1em auto;
  cursor: pointer;
  background: url("../img/train/shield.png");
  background-size: contain;
}
.battle-window .shield-window, .battle-window .switch-window, .battle-window .charge-window {
  position: absolute;
  height: 50%;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #eee;
  opacity: 0;
  pointer-events: none;
}
.battle-window .shield-window .container,
.battle-window .switch-window .container {
  width: 90%;
  margin: 0 auto;
  height: 100%;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.5);
  color: #eee;
  text-align: center;
  padding: 30px;
  box-sizing: border-box;
}
.battle-window .charge-window {
  -webkit-transition: all 0.25s ease 0.25s;
  -moz-transition: all 0.25s ease 0.25s;
  -o-transition: all 0.25s ease 0.25s;
  transition: all 0.25s ease 0.25s;
}
.battle-window .charge-window .container p {
  padding: 10px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.5);
}
.battle-window .charge-window .container .move-bar {
  display: block;
  width: 120px;
  height: 120px;
  border-radius: 120px;
  border: 6px solid #eee;
}
.battle-window .charge-window .container .move-bar .label {
  font-size: 60px;
  padding-top: 22px;
}
.battle-window .charge-window .container .move-bar .bar:nth-child(3),
.battle-window .charge-window .container .move-bar .bar:nth-child(4) {
  display: none;
}
.battle-window .charge-window .rings {
  position: absolute;
  width: 500px;
  height: 500px;
  left: 50%;
  bottom: -25%;
  margin-left: -250px;
}
.battle-window .charge-window .rings .ring-container {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.battle-window .charge-window .rings .ring-container .ring {
  border: 4px solid #eee;
  border-radius: 500px;
}
.battle-window .charge-window .rings .ring-container:nth-of-type(1) .ring {
  max-width: 90%;
  max-height: 90%;
}
.battle-window .charge-window .rings .ring-container:nth-of-type(2) .ring {
  max-width: 95%;
  max-height: 95%;
}
.battle-window .charge-window .rings .ring-container:nth-of-type(3) .ring {
  max-width: 100%;
  max-height: 100%;
}
.battle-window .charge-window .charge {
  font-weight: bold;
  font-size: 18px;
  padding: 10px;
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.5);
  display: inline-block;
  position: relative;
  top: 170px;
}
.battle-window .switch-sidebar {
  position: absolute;
  text-align: center;
  z-index: 20;
  bottom: 27%;
  right: 0;
  pointer-events: all;
}
.battle-window .switch-sidebar .pokemon {
  position: relative;
}
.battle-window .switch-sidebar .sprite-container {
  padding: 0;
  width: 45px;
  height: 45px;
  text-align: center;
  color: #eee;
}
.battle-window .switch-sidebar .sprite-container .main-sprite,
.battle-window .switch-sidebar .sprite-container .secondary-sprite {
  width: 75%;
  height: 75%;
  left: 6px;
  top: 6px;
}
.battle-window .switch-sidebar .sprite-container .cp {
  position: absolute;
  z-index: 5;
  font-size: 10px;
  text-align: center;
  width: 100%;
}
.battle-window .switch-sidebar .switch-timer {
  color: #eee;
  font-weight: bold;
  position: absolute;
  top: 12px;
  width: 100%;
  text-align: center;
  font-size: 24px;
  display: none;
}
.battle-window .switch-sidebar .name {
  font-size: 11px;
  margin-bottom: 5px;
  width: 60px;
  min-height: 24px;
}
.battle-window .switch-sidebar:not(.active) .sprite-container {
  opacity: 0.25;
}
.battle-window .switch-sidebar:not(.active) .switch-timer {
  display: block;
}
.battle-window .switch-sidebar .container {
  padding: 10px;
}
.battle-window .switch-window {
  height: 30%;
  opacity: 1;
  text-align: center;
  z-index: 20;
  bottom: -30%;
  -webkit-transition: all 0.25s ease 0s;
  -moz-transition: all 0.25s ease 0s;
  -o-transition: all 0.25s ease 0s;
  transition: all 0.25s ease 0s;
}
.battle-window .switch-window.active {
  bottom: 0;
  pointer-events: all;
}
.battle-window .switch-window p {
  margin-top: 0;
}
.battle-window .switch-window .container {
  padding: 15px 30px;
}
.battle-window .switch-window .pokemon {
  position: relative;
}
.battle-window .switch-window .pokemon-container {
  display: flex;
  justify-content: space-around;
}
.battle-window .switch-window .main-sprite,
.battle-window .switch-window .secondary-sprite {
  width: 85%;
  height: 85%;
  left: 4px;
  top: 4px;
}
.battle-window .switch-window .health,
.battle-window .switch-sidebar .health {
  width: 100%;
  height: 2px;
  background-color: #31e8b7;
  position: absolute;
  bottom: 3px;
  left: 5px;
  width: 75%;
}
.battle-window .switch-window .health[color=yellow],
.battle-window .switch-sidebar .health[color=yellow] {
  background: #eabd49;
}
.battle-window .switch-window .health[color=red],
.battle-window .switch-sidebar .health[color=red] {
  background: #e11414;
}
.battle-window .animate-message {
  position: absolute;
  width: 100%;
  text-align: center;
  top: 37%;
  opacity: 0;
  pointer-events: none;
  z-index: 25;
  -webkit-transition: all 0.25s ease 0.25s;
  -moz-transition: all 0.25s ease 0.25s;
  -o-transition: all 0.25s ease 0.25s;
  transition: all 0.25s ease 0.25s;
}
.battle-window .animate-message .text {
  margin: 0 auto;
  display: inline-block;
  background: rgba(0, 0, 0, 0.65);
  color: #eee;
  padding: 10px;
  border-radius: 12px;
  font-size: 18px;
}
.battle-window .countdown {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 20;
  top: 0;
  left: 0;
  display: none;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}
.battle-window .countdown .text {
  font-size: 300px;
  font-weight: bold;
  text-transform: uppercase;
  color: #eee;
}
.battle-window .countdown.animate .text {
  font-size: 72px;
  -webkit-transition: all 0.9s ease 0s;
  -moz-transition: all 0.9s ease 0s;
  -o-transition: all 0.9s ease 0s;
  transition: all 0.9s ease 0s;
}
.battle-window[phase=suspend_charged_shield] .timer {
  display: table;
}
.battle-window[phase=suspend_charged_shield] .shield-window {
  opacity: 1;
  pointer-events: all;
}
.battle-window[phase=suspend_charged_shield] .controls .move-bars,
.battle-window[phase=suspend_charged_shield] .controls .move-labels {
  display: none;
}
.battle-window[phase=suspend_charged_shield] .controls .button-stack,
.battle-window[phase=suspend_charged_shield] .controls .move-bar,
.battle-window[phase=suspend_charged_shield] .controls .switch-btn,
.battle-window[phase=suspend_charged_shield] .switch-sidebar {
  display: none !important;
}
.battle-window[phase=suspend_charged_shield] .team-indicator {
  display: none;
}
.battle-window[phase=suspend_charged_shield] .scene {
  -moz-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  transform: scale(0.8);
}
.battle-window[phase=suspend_charged_no_shields] .animate-message {
  opacity: 1;
}
.battle-window[phase=suspend_charged_no_shields] .controls .move-bars {
  display: none;
}
.battle-window[phase=suspend_charged_no_shields] .controls .button-stack,
.battle-window[phase=suspend_charged_no_shields] .controls .move-bar,
.battle-window[phase=suspend_charged_no_shields] .controls .switch-btn,
.battle-window[phase=suspend_charged_no_shields] .controls .move-labels,
.battle-window[phase=suspend_charged_no_shields] .switch-sidebar {
  display: none !important;
}
.battle-window[phase=suspend_charged_no_shields] .team-indicator {
  display: none;
}
.battle-window[phase=suspend_charged_no_shields] .scene {
  -moz-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  transform: scale(0.8);
}
.battle-window[phase=suspend_charged_attack] .timer {
  display: table;
}
.battle-window[phase=suspend_charged_attack] .animate-message {
  opacity: 1;
}
.battle-window[phase=suspend_charged_attack] .charge-window {
  opacity: 1;
  pointer-events: all;
}
.battle-window[phase=suspend_charged_attack] .controls .move-bars,
.battle-window[phase=suspend_charged_attack] .controls .move-labels {
  display: none;
}
.battle-window[phase=suspend_charged_attack] .controls .button-stack,
.battle-window[phase=suspend_charged_attack] .controls .move-bar,
.battle-window[phase=suspend_charged_attack] .switch-sidebar {
  display: none !important;
}
.battle-window[phase=suspend_charged_attack] .team-indicator {
  display: none;
}
.battle-window[phase=suspend_charged_attack] .scene {
  -moz-transform: scale(1.1);
  -webkit-transform: scale(1.1);
  -o-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}
.battle-window[phase=suspend_switch_self] .timer {
  display: table;
}
.battle-window[phase=suspend_switch_self] .switch-window {
  bottom: 0;
  pointer-events: all;
}
.battle-window[phase=suspend_switch_self] .controls .button-stack,
.battle-window[phase=suspend_switch_self] .controls .move-bar,
.battle-window[phase=suspend_switch_self] .controls .move-labels,
.battle-window[phase=suspend_switch_self] .switch-sidebar {
  display: none !important;
}
.battle-window[phase=suspend_switch] .animate-message {
  opacity: 1;
}
.battle-window[phase=suspend_switch] .controls .button-stack,
.battle-window[phase=suspend_switch] .controls .move-bar,
.battle-window[phase=suspend_switch] .switch-sidebar {
  display: none !important;
}
.battle-window[phase=animating] .animate-message {
  opacity: 1;
}
.battle-window[phase=animating] .team-indicator {
  display: none;
}
.battle-window[phase=animating] .controls .button-stack {
  display: none !important;
}
.battle-window[phase=countdown] .top, .battle-window[phase=countdown] .controls {
  display: none !important;
}
.battle-window[phase=countdown] .countdown {
  display: flex;
}
.battle-window[phase=game_over_screen] {
  display: inline-block;
}
.battle-window[phase=game_over_screen] .end-screen-container {
  bottom: -10px;
}
.battle-window[phase=game_paused] .countdown {
  display: flex;
}
.battle-window[mode=single] .button.next-round {
  display: none;
}

.team-select .pokemon-container .sprite-container {
  opacity: 0.25;
  overflow: hidden;
}
.team-select .pokemon-container .name {
  padding: 5px;
  border-radius: 12px;
  margin-top: 5px;
  max-width: 100px;
  font-weight: bold;
}
.team-select .pokemon-container .name, .team-select .pokemon-container .cp {
  text-align: center;
}
.team-select .pokemon-container .pokemon.active .sprite-container {
  opacity: 1;
}

.team-select {
  display: none;
}
.team-select .pokemon-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.team-select .pokemon-container .pokemon {
  width: 100px;
  margin-bottom: 10px;
}
.team-select .pokemon-container .pokemon .number {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: #0088a6;
  color: #eee;
  font-weight: bold;
  display: none;
}
.team-select .pokemon-container .pokemon.selected .number {
  display: block;
  opacity: 0.9;
  font-size: 40px;
  text-align: center;
  padding-top: 5px;
}
.team-select .self, .team-select .opponent {
  background: rgba(255, 255, 255, 0.75);
  border-radius: 12px;
  padding: 10px 0;
}
.team-select .self h3, .team-select .opponent h3 {
  margin-top: 0;
}
.team-select .self .pokemon, .team-select .opponent .pokemon {
  cursor: pointer;
}
.team-select .lets-go-btn {
  display: none;
}
.team-select a.return-to-setup {
  background: rgba(255, 255, 255, 0.75);
  color: #000;
  border-radius: 8px;
  font-size: 14px;
  padding: 4px 12px;
  margin-bottom: 10px;
  display: inline-block;
}

/* END SCREEN */
.end-screen-container {
  -webkit-transition: all 0.5s ease 0s;
  -moz-transition: all 0.5s ease 0s;
  -o-transition: all 0.5s ease 0s;
  transition: all 0.5s ease 0s;
  position: absolute;
  z-index: 50;
  width: 100%;
  bottom: -150%;
}
.end-screen-container .end-screen {
  width: 90%;
  margin: 0 auto;
  padding-bottom: 20px;
  min-height: 450px;
  box-shadow: #000 0px 0px 15px;
  background: rgba(255, 255, 255, 0.95);
}
.end-screen-container .buttons {
  display: flex;
}
.end-screen-container .buttons .button {
  font-size: 14px;
}
.end-screen-container .tabs {
  display: flex;
}
.end-screen-container .tabs a.tab {
  display: block;
  width: 33%;
  text-align: center;
  text-decoration: none;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 14px;
  padding: 5px;
}
.end-screen-container .tabs a.tab[href=damage] {
  color: #ed4f34;
}
.end-screen-container .tabs a.tab[href=damage].active {
  background: #ed4f34;
  color: #eee;
}
.end-screen-container .tabs a.tab[href=shields] {
  color: #cb28c1;
}
.end-screen-container .tabs a.tab[href=shields].active {
  background: #cb28c1;
  color: #eee;
}
.end-screen-container .tabs a.tab[href=energy] {
  color: #aecb28;
}
.end-screen-container .tabs a.tab[href=energy].active {
  background: #aecb28;
  color: #eee;
}
.end-screen-container h3.result {
  margin: 0;
}
.end-screen-container .tab-section {
  display: none;
}
.end-screen-container .tab-content-container {
  height: 300px;
  overflow-y: scroll;
  margin-top: 10px;
}
.end-screen-container p.description {
  font-size: 12px;
}
.end-screen-container p.description:first-of-type {
  margin-top: 0;
}
.end-screen-container .damage h3 {
  font-size: 14px;
  margin: 0;
  text-align: left;
}
.end-screen-container .damage h3.center {
  text-align: center;
}
.end-screen-container .damage .damage-section {
  padding: 10px;
  border-radius: 12px;
  background: #eee;
  position: relative;
}
.end-screen-container .damage .damage-section .avg-line {
  height: 100%;
  width: 0px;
  border: 1px dotted rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 49.75%;
}
.end-screen-container .damage .avg-label {
  display: inline-block;
  font-size: 12px;
}
.end-screen-container .pokemon-entry {
  display: flex;
  margin-bottom: 15px;
}
.end-screen-container .pokemon-entry:last-of-type {
  margin-bottom: 0;
}
.end-screen-container .pokemon-entry .name {
  text-align: left;
  font-size: 12px;
}
.end-screen-container .pokemon-entry .poke-icon {
  width: 25%;
}
.end-screen-container .pokemon-entry .damage-container {
  width: 75%;
  display: flex;
}
.end-screen-container .pokemon-entry .damage-container .damage-bar,
.end-screen-container .pokemon-entry .damage-container .shield-bar {
  width: 100%;
  height: 12px;
  border-radius: 12px;
  position: relative;
  z-index: 5px;
}
.end-screen-container .pokemon-entry .damage-container .damage-bar {
  background: url("../img/train/damage-bar-bg.jpg");
  background-position: -40px 0;
}
.end-screen-container .pokemon-entry .damage-container .shield-bar {
  background: url(../img/train/shield-bar-bg.jpg);
  background-size: 200%;
  background-position: 70% 0;
  margin-left: 2px;
}
.end-screen-container .stats-table {
  width: 100%;
}

/* POKEMON SPRITES */
.sprite-container {
  width: 36px;
  height: 36px;
  border: 1px solid #0088a6;
  border-radius: 12px;
  padding: 10px;
  background: #333;
  margin: 0 auto;
  position: relative;
}

.main-sprite,
.secondary-sprite,
.white-sprite {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: 600% 300%;
}

.main-sprite {
  background-image: url("../img/train/substitute-sprite-main.png");
}

.secondary-sprite {
  background-image: url("../img/train/substitute-sprite-secondary.png");
}

.white-sprite {
  -webkit-transition: opacity 0.75s ease 0s;
  -moz-transition: opacity 0.75s ease 0s;
  -o-transition: opacity 0.75s ease 0s;
  transition: opacity 0.75s ease 0s;
  background-image: url("../img/train/substitute-sprite-white.png");
  background-size: 100%;
  background-repeat: no-repeat;
  opacity: 0;
  z-index: 25;
}

.pokemon[type-1=bug] .main-sprite,
.pokemon[type-2=bug] .secondary-sprite {
  background-position: 0 0;
}

.pokemon[type-1=dark] .main-sprite,
.pokemon[type-2=dark] .secondary-sprite {
  background-position: -100% 0;
}

.pokemon[type-1=dragon] .main-sprite,
.pokemon[type-2=dragon] .secondary-sprite {
  background-position: -200% 0;
}

.pokemon[type-1=electric] .main-sprite,
.pokemon[type-2=electric] .secondary-sprite {
  background-position: -300% 0;
}

.pokemon[type-1=fairy] .main-sprite,
.pokemon[type-2=fairy] .secondary-sprite {
  background-position: -400% 0;
}

.pokemon[type-1=fighting] .main-sprite,
.pokemon[type-2=fighting] .secondary-sprite {
  background-position: -500% 0;
}

.pokemon[type-1=fire] .main-sprite,
.pokemon[type-2=fire] .secondary-sprite {
  background-position: 0% -100%;
}

.pokemon[type-1=flying] .main-sprite,
.pokemon[type-2=flying] .secondary-sprite {
  background-position: -100% -100%;
}

.pokemon[type-1=ghost] .main-sprite,
.pokemon[type-2=ghost] .secondary-sprite {
  background-position: -200% -100%;
}

.pokemon[type-1=grass] .main-sprite,
.pokemon[type-2=grass] .secondary-sprite {
  background-position: -300% -100%;
}

.pokemon[type-1=ground] .main-sprite,
.pokemon[type-2=ground] .secondary-sprite {
  background-position: -400% -100%;
}

.pokemon[type-1=ice] .main-sprite,
.pokemon[type-2=ice] .secondary-sprite {
  background-position: -500% -100%;
}

.pokemon[type-1=normal] .main-sprite,
.pokemon[type-2=normal] .secondary-sprite {
  background-position: 0% -200%;
}

.pokemon[type-1=poison] .main-sprite,
.pokemon[type-2=poison] .secondary-sprite {
  background-position: -100% -200%;
}

.pokemon[type-1=psychic] .main-sprite,
.pokemon[type-2=psychic] .secondary-sprite {
  background-position: -200% -200%;
}

.pokemon[type-1=rock] .main-sprite,
.pokemon[type-2=rock] .secondary-sprite {
  background-position: -300% -200%;
}

.pokemon[type-1=steel] .main-sprite,
.pokemon[type-2=steel] .secondary-sprite {
  background-position: -400% -200%;
}

.pokemon[type-1=water] .main-sprite,
.pokemon[type-2=water] .secondary-sprite {
  background-position: -500% -200%;
}

/* ANIMATIONS */
.battle-window .scene .pokemon-container[cooldown="500"] {
  -webkit-transition: all 0.25s ease-out 0s;
  -moz-transition: all 0.25s ease-out 0s;
  -o-transition: all 0.25s ease-out 0s;
  transition: all 0.25s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="500"] .fast-move-circle {
  -webkit-transition: all 0.25s ease-out 0s;
  -moz-transition: all 0.25s ease-out 0s;
  -o-transition: all 0.25s ease-out 0s;
  transition: all 0.25s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="1000"] {
  -webkit-transition: all 0.75s ease-out 0s;
  -moz-transition: all 0.75s ease-out 0s;
  -o-transition: all 0.75s ease-out 0s;
  transition: all 0.75s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="1000"] .fast-move-circle {
  -webkit-transition: all 0.5s ease-out 0s;
  -moz-transition: all 0.5s ease-out 0s;
  -o-transition: all 0.5s ease-out 0s;
  transition: all 0.5s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="1500"] {
  -webkit-transition: all 1.25s ease-out 0s;
  -moz-transition: all 1.25s ease-out 0s;
  -o-transition: all 1.25s ease-out 0s;
  transition: all 1.25s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="1500"] .fast-move-circle {
  -webkit-transition: all 1s ease-out 0s;
  -moz-transition: all 1s ease-out 0s;
  -o-transition: all 1s ease-out 0s;
  transition: all 1s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="2000"] {
  -webkit-transition: all 1.75s ease-out 0s;
  -moz-transition: all 1.75s ease-out 0s;
  -o-transition: all 1.75s ease-out 0s;
  transition: all 1.75s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="2000"] .fast-move-circle {
  -webkit-transition: all 1.5s ease-out 0s;
  -moz-transition: all 1.5s ease-out 0s;
  -o-transition: all 1.5s ease-out 0s;
  transition: all 1.5s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="2500"] {
  -webkit-transition: all 2.25s ease-out 0s;
  -moz-transition: all 2.25s ease-out 0s;
  -o-transition: all 2.25s ease-out 0s;
  transition: all 2.25s ease-out 0s;
}
.battle-window .scene .pokemon-container[cooldown="2500"] .fast-move-circle {
  -webkit-transition: all 2s ease-out 0s;
  -moz-transition: all 2s ease-out 0s;
  -o-transition: all 2s ease-out 0s;
  transition: all 2s ease-out 0s;
}
.battle-window .scene .pokemon-container.animate-fast {
  -webkit-transition: all 0.25s ease-out 0s;
  -moz-transition: all 0.25s ease-out 0s;
  -o-transition: all 0.25s ease-out 0s;
  transition: all 0.25s ease-out 0s;
}
.battle-window .scene .pokemon-container.animate-fast.self {
  bottom: 31%;
  left: 21%;
}
.battle-window .scene .pokemon-container.animate-fast.opponent {
  bottom: 51%;
  right: 26%;
}
.battle-window .scene .pokemon-container.animate-fast .fast-move-circle {
  -webkit-transition: all 0s ease-out 0s;
  -moz-transition: all 0s ease-out 0s;
  -o-transition: all 0s ease-out 0s;
  transition: all 0s ease-out 0s;
  width: 20px;
  height: 20px;
  opacity: 1;
}
.battle-window .scene .pokemon-container.animate-switch .pokemon {
  -moz-transform: scale(0, 0);
  -webkit-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  transform: scale(0, 0);
}
.battle-window .scene .pokemon-container.animate-switch .pokemon .white-sprite {
  opacity: 1;
}
.battle-window .scene .pokemon-container.animate-switch .pokemon .shadow {
  display: none;
}
.battle-window .scene .pokemon-container.animate-switch .hp, .battle-window .scene .pokemon-container.animate-switch .name, .battle-window .scene .pokemon-container.animate-switch .messages {
  display: none;
}

.move-bar .bar[type=bug] {
  background-position-x: 0px !important;
}
.move-bar .bar[type=dark] {
  background-position-x: -200px !important;
}
.move-bar .bar[type=dragon] {
  background-position-x: -400px !important;
}
.move-bar .bar[type=electric] {
  background-position-x: -600px !important;
}
.move-bar .bar[type=fairy] {
  background-position-x: -800px !important;
}
.move-bar .bar[type=fighting] {
  background-position-x: -1000px !important;
}
.move-bar .bar[type=fire] {
  background-position-x: -1200px !important;
}
.move-bar .bar[type=flying] {
  background-position-x: -1400px !important;
}
.move-bar .bar[type=ghost] {
  background-position-x: -1600px !important;
}
.move-bar .bar[type=grass] {
  background-position-x: -1800px !important;
}
.move-bar .bar[type=ground] {
  background-position-x: -2000px !important;
}
.move-bar .bar[type=ice] {
  background-position-x: -2200px !important;
}
.move-bar .bar[type=normal] {
  background-position-x: -2400px !important;
}
.move-bar .bar[type=poison] {
  background-position-x: -2600px !important;
}
.move-bar .bar[type=psychic] {
  background-position-x: -2800px !important;
}
.move-bar .bar[type=rock] {
  background-position-x: -3000px !important;
}
.move-bar .bar[type=steel] {
  background-position-x: -3200px !important;
}
.move-bar .bar[type=water] {
  background-position-x: -3400px !important;
}

@media only screen and (max-width: 720px) {
  .train > .poke {
    width: 49%;
  }

  .team-select.self, .team-select.opponent {
    padding: 5px 0;
  }
  .team-select h3.center {
    margin: 5px 0;
    font-size: 16px;
  }
  .team-select h4 {
    margin-bottom: 0;
  }
  .team-select .roster .pokemon .sprite-container {
    width: 25px;
    height: 25px;
  }
  .team-select .roster .pokemon .cp {
    font-size: 12px;
  }
  .team-select .roster .pokemon .name {
    margin-top: 0;
    font-size: 14px;
    line-height: 14px;
  }
  .team-select .number {
    padding-top: 2px !important;
  }
}
@media only screen and (max-width: 350px) {
  .move-labels .label {
    display: none !important;
  }
}

/*# sourceMappingURL=train.css.map */
