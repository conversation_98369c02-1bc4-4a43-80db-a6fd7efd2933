import { useState, useEffect, useRef } from 'react';
import { BattleEvent, SandboxAction } from '@/types/battle';
import { useBattleStore } from '@/stores/battleStore';
import { SandboxActionEditor } from './SandboxActionEditor';
import { Modal } from '../common/Modal';
import clsx from 'clsx';

interface BattleTimelineProps {
  timeline: BattleEvent[];
}

export function BattleTimeline({ timeline }: BattleTimelineProps) {
  const {
    timelineScale,
    playbackSpeed,
    isPlaying,
    currentTurn,
    sandboxMode,
    setTimelineScale,
    setPlaybackSpeed,
    playTimeline,
    pauseTimeline,
    seekTimeline,
    toggleSandboxMode,
    clearSandboxActions
  } = useBattleStore();
  
  const [hoveredEvent, setHoveredEvent] = useState<BattleEvent | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [selectedTurn, setSelectedTurn] = useState<number>(0);
  const [editingAction, setEditingAction] = useState<SandboxAction | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  
  // Effect to scroll to current turn when it changes
  useEffect(() => {
    if (timelineRef.current && currentTurn > 0) {
      const currentEventEl = timelineRef.current.querySelector(`[data-turn="${currentTurn}"]`);
      if (currentEventEl) {
        currentEventEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  }, [currentTurn]);
  
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'fast':
        return '⚡';
      case 'charged':
        return '💥';
      case 'shield':
        return '🛡️';
      case 'switch':
        return '🔄';
      default:
        return '❓';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'fast':
        return 'text-yellow-600 bg-yellow-50';
      case 'charged':
        return 'text-red-600 bg-red-50';
      case 'shield':
        return 'text-blue-600 bg-blue-50';
      case 'switch':
        return 'text-purple-600 bg-purple-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getActorColor = (actor: 'pokemon1' | 'pokemon2') => {
    return actor === 'pokemon1' ? 'border-l-blue-500' : 'border-l-red-500';
  };
  
  const handleTimelineClick = (turn: number) => {
    if (sandboxMode) {
      // In sandbox mode, clicking on a turn should open the action editor
      setSelectedTurn(turn);
      setEditingAction(null); // Start with a new action
      setIsEditorOpen(true);
    } else {
      // In normal mode, clicking on a turn should seek to that turn
      seekTimeline(turn);
    }
  };
  
  const handleMouseMove = (e: React.MouseEvent) => {
    setTooltipPosition({ x: e.clientX, y: e.clientY });
  };

  if (timeline.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Battle Timeline</h3>
        <p className="text-gray-500 text-center py-8">No battle events to display</p>
      </div>
    );
  }
  
  // Get the maximum turn number from the timeline
  const maxTurn = timeline.reduce((max, event) => Math.max(max, event.turn), 0);

  return (
    <div className="bg-white rounded-lg shadow-md p-6" onMouseMove={handleMouseMove}>
      {/* Tooltip */}
      {hoveredEvent && (
        <div 
          className="fixed z-50 bg-white shadow-lg rounded-lg p-3 border border-gray-200 w-64"
          style={{
            left: tooltipPosition.x + 10,
            top: tooltipPosition.y + 10,
            transform: 'translateY(-50%)'
          }}
        >
          <div className="font-medium text-sm mb-1">
            Turn {hoveredEvent.turn}: {hoveredEvent.actor === 'pokemon1' ? 'Pokemon 1' : 'Pokemon 2'}
          </div>
          <div className="text-sm text-gray-700">
            {hoveredEvent.action.charAt(0).toUpperCase() + hoveredEvent.action.slice(1)} move
            {hoveredEvent.move && `: ${hoveredEvent.move.name}`}
          </div>
          {hoveredEvent.damage !== undefined && (
            <div className="text-xs text-red-700 mt-1">Damage: {hoveredEvent.damage}</div>
          )}
          {hoveredEvent.energy !== undefined && (
            <div className="text-xs text-blue-700 mt-1">Energy: {hoveredEvent.energy > 0 ? '+' : ''}{hoveredEvent.energy}</div>
          )}
          {hoveredEvent.hp !== undefined && (
            <div className="text-xs text-green-700 mt-1">HP Remaining: {hoveredEvent.hp}</div>
          )}
        </div>
      )}
      
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Battle Timeline</h3>
        
        {sandboxMode && (
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm font-medium"
              onClick={clearSandboxActions}
            >
              Clear Timeline
            </button>
          </div>
        )}
      </div>
      
      {/* Timeline visualization */}
      <div className={`timeline-container ${timelineScale}`}>
        <div className="relative h-24 bg-gray-50 rounded-lg overflow-hidden mb-4">
          {/* Timeline events visualization */}
          {timeline.map((event) => (
            <div
              key={`${event.turn}-${event.actor}-${event.action}`}
              className={clsx(
                'absolute h-full w-1',
                event.actor === 'pokemon1' ? 'bg-blue-400' : 'bg-red-400',
                event.action === 'charged' && 'w-2'
              )}
              style={{ 
                left: `${(event.turn / maxTurn) * 100}%`,
                opacity: currentTurn >= event.turn ? 1 : 0.3
              }}
              onClick={() => handleTimelineClick(event.turn)}
            />
          ))}
          
          {/* Current position tracker */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-black"
            style={{ left: `${(currentTurn / maxTurn) * 100}%` }}
          />
        </div>
      </div>
      
      {/* Playback controls */}
      <div className="flex items-center justify-between mt-4 mb-6">
        <div className="flex items-center space-x-3">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            onClick={() => seekTimeline(0)}
            title="Restart"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </button>
          
          <button
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            onClick={isPlaying ? pauseTimeline : playTimeline}
            title={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
              </svg>
            )}
          </button>
          
          <select
            value={playbackSpeed}
            onChange={(e) => setPlaybackSpeed(Number(e.target.value) as 1 | 4 | 8 | 16)}
            className="px-2 py-1 text-sm border border-gray-300 rounded"
          >
            <option value={1}>1x speed*</option>
            <option value={4}>4x speed</option>
            <option value={8}>8x speed</option>
            <option value={16}>16x speed</option>
          </select>
          
          <select
            value={timelineScale}
            onChange={(e) => setTimelineScale(e.target.value as 'fit' | 'zoom')}
            className="px-2 py-1 text-sm border border-gray-300 rounded"
          >
            <option value="fit">Scale to fit</option>
            <option value="zoom">Zoom in</option>
          </select>
        </div>
        
        {/* Sandbox mode toggle */}
        <div
          className={clsx(
            'px-3 py-1 rounded-full text-sm font-medium cursor-pointer transition-colors',
            sandboxMode
              ? 'bg-pvpoke-primary text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
          onClick={toggleSandboxMode}
        >
          Sandbox Mode
        </div>
      </div>
      
      <div className="text-xs text-gray-500 mb-4">
        * Results may differ from actual gameplay depending on connectivity, device, player decisions, or other factors.
      </div>
      
      {/* Timeline events list */}
      <div className="space-y-3 max-h-96 overflow-y-auto" ref={timelineRef}>
        {timeline.map((event, index) => (
          <div
            key={index}
            data-turn={event.turn}
            className={clsx(
              'flex items-start space-x-3 p-3 rounded-lg border-l-4',
              getActorColor(event.actor),
              currentTurn >= event.turn ? 'opacity-100' : 'opacity-50'
            )}
            onClick={() => handleTimelineClick(event.turn)}
            onMouseEnter={() => setHoveredEvent(event)}
            onMouseLeave={() => setHoveredEvent(null)}
          >
            {/* Turn Number */}
            <div className="flex-shrink-0 w-12 text-center">
              <span className="text-xs font-medium text-gray-500">
                T{event.turn}
              </span>
            </div>

            {/* Action Icon */}
            <div className={clsx(
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm',
              getActionColor(event.action)
            )}>
              {getActionIcon(event.action)}
            </div>

            {/* Event Details */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <span className={clsx(
                  'font-medium text-sm',
                  event.actor === 'pokemon1' ? 'text-blue-700' : 'text-red-700'
                )}>
                  Pokemon {event.actor === 'pokemon1' ? '1' : '2'}
                </span>
                <span className="text-gray-600 text-sm">
                  used {event.action} move
                </span>
              </div>

              {/* Move Name */}
              {event.move && (
                <div className="text-sm text-gray-700 mt-1">
                  <span className="font-medium">{event.move.name}</span>
                  <span className="text-gray-500 ml-2">({event.move.type})</span>
                </div>
              )}

              {/* Damage/Energy/HP Info */}
              <div className="flex flex-wrap gap-4 mt-2 text-xs text-gray-600">
                {event.damage !== undefined && (
                  <span className="bg-red-100 text-red-700 px-2 py-1 rounded">
                    -{event.damage} damage
                  </span>
                )}
                {event.energy !== undefined && (
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                    {event.energy > 0 ? '+' : ''}{event.energy} energy
                  </span>
                )}
                {event.hp !== undefined && (
                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded">
                    {event.hp} HP remaining
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Timeline Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="text-gray-600">Total Events</div>
            <div className="font-semibold text-lg">{timeline.length}</div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">Fast Moves</div>
            <div className="font-semibold text-lg">
              {timeline.filter(e => e.action === 'fast').length}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">Charged Moves</div>
            <div className="font-semibold text-lg">
              {timeline.filter(e => e.action === 'charged').length}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-600">Shields Used</div>
            <div className="font-semibold text-lg">
              {timeline.filter(e => e.action === 'shield').length}
            </div>
          </div>
        </div>
      </div>
      
      {/* Sandbox Action Editor Modal */}
      <Modal isOpen={isEditorOpen} onClose={() => setIsEditorOpen(false)}>
        <SandboxActionEditor
          action={editingAction}
          availableMoves={{
            pokemon1: {
              fast: [{ moveId: 'mock_fast', name: 'Mock Fast Move', type: 'normal', power: 5, energyGain: 5, cooldown: 0.5 }],
              charged: [{ moveId: 'mock_charged', name: 'Mock Charged Move', type: 'normal', power: 50, energy: 50, cooldown: 2.0 }],
            },
            pokemon2: {
              fast: [{ moveId: 'mock_fast', name: 'Mock Fast Move', type: 'normal', power: 5, energyGain: 5, cooldown: 0.5 }],
              charged: [{ moveId: 'mock_charged', name: 'Mock Charged Move', type: 'normal', power: 50, energy: 50, cooldown: 2.0 }],
            },
          }}
          onSave={(action) => {
            // TODO: Implement saving sandbox action
            console.log('Saving sandbox action:', action);
            setIsEditorOpen(false);
          }}
          onCancel={() => setIsEditorOpen(false)}
          turn={selectedTurn}
        />
      </Modal>
    </div>
  );
}
