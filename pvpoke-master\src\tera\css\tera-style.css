@charset "UTF-8";
/* VARIABLES */
/* MIXINS */
html, body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  color: #fff;
  height: 100%;
}

body {
  /*background: rgb(207,32,32);
  background: -moz-linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);
  background: -webkit-linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);
  background: linear-gradient(167deg, rgba(207,32,32,1) 2%, rgba(171,42,9,1) 14%, rgba(82,8,59,1) 48%, rgba(29,4,65,1) 72%);*/
  background: #52083b;
  background: -moz-linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background: -webkit-linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background: linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background-attachment: fixed;
  background: url("../img/bg.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  background-position: center bottom;
}

a {
  color: #990f44;
}
a:hover {
  color: #ad104c;
}

header {
  padding: 5px;
  background: #1e0441;
  position: fixed;
  z-index: 15;
  width: 100%;
  box-sizing: border-box;
}
header .header-wrap {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}
header a {
  color: #ffcf01;
}
header a img {
  height: 30px;
}

#main {
  max-width: 1000px;
  margin: auto;
  padding: 37px 10px 10px 10px;
  border-radius: 12px;
}
#main > h1 {
  margin: 0;
}
#main p {
  font-size: 16px;
  line-height: 22px;
}

input[type=text] {
  background: none;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  color: #fff;
  outline: none;
  padding-bottom: 5px;
}

::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

:-moz-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

::-moz-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.5);
}

::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.flex {
  display: flex;
  flex-wrap: wrap;
}

.template {
  display: none;
}

.bordered-section {
  background: #1d0441;
  border: 3px solid #0a0018;
  padding: 10px;
  border-bottom-left-radius: 10px;
  border-top-right-radius: 10px;
}

.bordered-section select, .modal select {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.75);
  color: #fff;
  padding: 10px;
  box-sizing: border-box;
  border-bottom-left-radius: 10px;
  border-top-right-radius: 10px;
}
.bordered-section select:hover, .modal select:hover {
  background: rgba(0, 0, 0, 0.75);
}
.bordered-section select option, .modal select option {
  background: #1d0441;
}
.bordered-section select.error, .modal select.error {
  border: 1px solid #ce2c09;
  color: #ce2c09;
}
.bordered-section select.error option, .modal select.error option {
  color: #fff;
}

.section-header {
  margin: 10px 0;
  align-items: center;
}
.section-header h3 {
  font-size: 22px;
  margin: 0 20px 0 0;
}
.section-header.margin-top {
  margin-top: 40px;
}

.hr {
  flex-grow: 1;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}

.poke-search-container input, .poke-search-container select {
  display: block;
  width: 225px;
  box-sizing: border-box;
}
.poke-search-container select {
  margin: 10px 0 10px 0;
  font-size: 20px;
}

.boss-section {
  background: #6d1744;
  background: -moz-linear-gradient(117deg, #a1417f 33%, #6d1744 100%);
  background: -webkit-linear-gradient(117deg, #a1417f 33%, #6d1744 100%);
  background: linear-gradient(117deg, #a1417f 33%, #6d1744 100%);
  position: relative;
  overflow: hidden;
}
.boss-section > .flex {
  position: relative;
  z-index: 10;
  justify-content: space-between;
  margin-bottom: 15px;
}
.boss-section > .flex:last-of-type {
  margin-bottom: 0;
}
.boss-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
}
.boss-section h4.sub-title {
  margin-bottom: 10px;
}
.boss-section .pattern {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 0;
  left: 0;
  top: 0;
  background: url("../img/pattern.png");
  background-position: center center;
  opacity: 0.1;
}
.boss-section .flash {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 0;
  left: -1000px;
  top: 0;
  background: white;
  background: -moz-linear-gradient(123deg, rgba(255, 255, 255, 0) 16%, white 23%, white 25%, rgba(255, 255, 255, 0) 29%, white 30%, rgba(255, 255, 255, 0) 33%);
  background: -webkit-linear-gradient(123deg, rgba(255, 255, 255, 0) 16%, white 23%, white 25%, rgba(255, 255, 255, 0) 29%, white 30%, rgba(255, 255, 255, 0) 33%);
  background: linear-gradient(123deg, rgba(255, 255, 255, 0) 16%, white 23%, white 25%, rgba(255, 255, 255, 0) 29%, white 30%, rgba(255, 255, 255, 0) 33%);
  opacity: 0.2;
}
.boss-section .traits-container {
  display: none;
  margin-top: 20px;
}
.boss-section .traits-container .trait-item {
  padding: 5px 10px;
  width: auto;
  background: rgba(0, 0, 0, 0.5);
}
.boss-section .traits-container .trait-item.on {
  background: #990f44;
}
.boss-section #tera-select {
  font-size: 20px;
  min-width: 100px;
}
.boss-section #tera-select option {
  background: #1d0441;
}
.boss-section #poke-select {
  margin-bottom: 20px;
}
.boss-section .item-list {
  display: flex;
  justify-content: flex-start;
  max-width: 480px;
  flex-wrap: wrap;
}
.boss-section .item-list #attack-type-select {
  margin: 10px 0 0 0;
  font-size: 14px;
  padding: 5px;
  width: 105px;
  border-radius: 10px;
}
.boss-section .tera-type-container .tera-icon {
  display: none;
}
.boss-section[tera-type=bug] {
  background: #5b6c16;
  background: -moz-linear-gradient(117deg, #9ba141 33%, #5b6c16 100%);
  background: -webkit-linear-gradient(117deg, #9ba141 33%, #5b6c16 100%);
  background: linear-gradient(117deg, #9ba141 33%, #5b6c16 100%);
}
.boss-section[tera-type=dark] {
  background: #6f536f;
  background: -moz-linear-gradient(117deg, #a68aa6 33%, #6f536f 100%);
  background: -webkit-linear-gradient(117deg, #a68aa6 33%, #6f536f 100%);
  background: linear-gradient(117deg, #a68aa6 33%, #6f536f 100%);
}
.boss-section[tera-type=dragon] {
  background: #4654cd;
  background: -moz-linear-gradient(117deg, #808dff 33%, #4654cd 100%);
  background: -webkit-linear-gradient(117deg, #808dff 33%, #4654cd 100%);
  background: linear-gradient(117deg, #808dff 33%, #4654cd 100%);
}
.boss-section[tera-type=electric] {
  background: #805d01;
  background: -moz-linear-gradient(117deg, #c59515 33%, #805d01 100%);
  background: -webkit-linear-gradient(117deg, #c59515 33%, #805d01 100%);
  background: linear-gradient(117deg, #c59515 33%, #805d01 100%);
}
.boss-section[tera-type=fairy] {
  background: #9f2ea6;
  background: -moz-linear-gradient(117deg, #d766de 33%, #9f2ea6 100%);
  background: -webkit-linear-gradient(117deg, #d766de 33%, #9f2ea6 100%);
  background: linear-gradient(117deg, #d766de 33%, #9f2ea6 100%);
}
.boss-section[tera-type=fighting] {
  background: #904503;
  background: -moz-linear-gradient(117deg, #d16f1a 33%, #904503 100%);
  background: -webkit-linear-gradient(117deg, #d16f1a 33%, #904503 100%);
  background: linear-gradient(117deg, #d16f1a 33%, #904503 100%);
}
.boss-section[tera-type=fire] {
  background: #b22720;
  background: -moz-linear-gradient(117deg, #dc5750 33%, #b22720 100%);
  background: -webkit-linear-gradient(117deg, #dc5750 33%, #b22720 100%);
  background: linear-gradient(117deg, #dc5750 33%, #b22720 100%);
}
.boss-section[tera-type=flying] {
  background: #485d95;
  background: -moz-linear-gradient(117deg, #8095cd 33%, #485d95 100%);
  background: -webkit-linear-gradient(117deg, #8095cd 33%, #485d95 100%);
  background: linear-gradient(117deg, #8095cd 33%, #485d95 100%);
}
.boss-section[tera-type=ghost] {
  background: #7c4978;
  background: -moz-linear-gradient(117deg, #b481b0 33%, #7c4978 100%);
  background: -webkit-linear-gradient(117deg, #b481b0 33%, #7c4978 100%);
  background: linear-gradient(117deg, #b481b0 33%, #7c4978 100%);
}
.boss-section[tera-type=grass] {
  background: #377b25;
  background: -moz-linear-gradient(117deg, #6ab755 33%, #377b25 100%);
  background: -webkit-linear-gradient(117deg, #6ab755 33%, #377b25 100%);
  background: linear-gradient(117deg, #6ab755 33%, #377b25 100%);
}
.boss-section[tera-type=ground] {
  background: #87512f;
  background: -moz-linear-gradient(117deg, #bf8967 33%, #87512f 100%);
  background: -webkit-linear-gradient(117deg, #bf8967 33%, #87512f 100%);
  background: linear-gradient(117deg, #bf8967 33%, #87512f 100%);
}
.boss-section[tera-type=ice] {
  background: #046d80;
  background: -moz-linear-gradient(117deg, #32b3d3 33%, #046d80 100%);
  background: -webkit-linear-gradient(117deg, #32b3d3 33%, #046d80 100%);
  background: linear-gradient(117deg, #32b3d3 33%, #046d80 100%);
}
.boss-section[tera-type=normal] {
  background: #5d5d5d;
  background: -moz-linear-gradient(117deg, #959595 33%, #5d5d5d 100%);
  background: -webkit-linear-gradient(117deg, #959595 33%, #5d5d5d 100%);
  background: linear-gradient(117deg, #959595 33%, #5d5d5d 100%);
}
.boss-section[tera-type=poison] {
  background: #79469c;
  background: -moz-linear-gradient(117deg, #b379db 33%, #79469c 100%);
  background: -webkit-linear-gradient(117deg, #b379db 33%, #79469c 100%);
  background: linear-gradient(117deg, #b379db 33%, #79469c 100%);
}
.boss-section[tera-type=psychic] {
  background: #964060;
  background: -moz-linear-gradient(117deg, #d67498 33%, #964060 100%);
  background: -webkit-linear-gradient(117deg, #d67498 33%, #964060 100%);
  background: linear-gradient(117deg, #d67498 33%, #964060 100%);
}
.boss-section[tera-type=rock] {
  background: #656135;
  background: -moz-linear-gradient(117deg, #9c986c 33%, #656135 100%);
  background: -webkit-linear-gradient(117deg, #9c986c 33%, #656135 100%);
  background: linear-gradient(117deg, #9c986c 33%, #656135 100%);
}
.boss-section[tera-type=steel] {
  background: #386e64;
  background: -moz-linear-gradient(117deg, #70a69c 33%, #386e64 100%);
  background: -webkit-linear-gradient(117deg, #70a69c 33%, #386e64 100%);
  background: linear-gradient(117deg, #70a69c 33%, #386e64 100%);
}
.boss-section[tera-type=water] {
  background: #2b578d;
  background: -moz-linear-gradient(117deg, #5794df 33%, #2b578d 100%);
  background: -webkit-linear-gradient(117deg, #5794df 33%, #2b578d 100%);
  background: linear-gradient(117deg, #5794df 33%, #2b578d 100%);
}

[tera-type=bug] .tera-icon {
  background-image: url("../img/types/bug.png");
}

[tera-type=dark] .tera-icon {
  background-image: url("../img/types/dark.png");
}

[tera-type=dragon] .tera-icon {
  background-image: url("../img/types/dragon.png");
}

[tera-type=electric] .tera-icon {
  background-image: url("../img/types/electric.png");
}

[tera-type=fairy] .tera-icon {
  background-image: url("../img/types/fairy.png");
}

[tera-type=fighting] .tera-icon {
  background-image: url("../img/types/fighting.png");
}

[tera-type=fire] .tera-icon {
  background-image: url("../img/types/fire.png");
}

[tera-type=flying] .tera-icon {
  background-image: url("../img/types/flying.png");
}

[tera-type=ghost] .tera-icon {
  background-image: url("../img/types/ghost.png");
}

[tera-type=grass] .tera-icon {
  background-image: url("../img/types/grass.png");
}

[tera-type=ground] .tera-icon {
  background-image: url("../img/types/ground.png");
}

[tera-type=ice] .tera-icon {
  background-image: url("../img/types/ice.png");
}

[tera-type=normal] .tera-icon {
  background-image: url("../img/types/normal.png");
}

[tera-type=poison] .tera-icon {
  background-image: url("../img/types/poison.png");
}

[tera-type=psychic] .tera-icon {
  background-image: url("../img/types/psychic.png");
}

[tera-type=rock] .tera-icon {
  background-image: url("../img/types/rock.png");
}

[tera-type=steel] .tera-icon {
  background-image: url("../img/types/steel.png");
}

[tera-type=water] .tera-icon {
  background-image: url("../img/types/water.png");
}

.type-item[tera-type=bug] .tera-icon {
  background-image: url("../img/types/bug.png");
}

.type-item[tera-type=dark] .tera-icon {
  background-image: url("../img/types/dark.png");
}

.type-item[tera-type=dragon] .tera-icon {
  background-image: url("../img/types/dragon.png");
}

.type-item[tera-type=electric] .tera-icon {
  background-image: url("../img/types/electric.png");
}

.type-item[tera-type=fairy] .tera-icon {
  background-image: url("../img/types/fairy.png");
}

.type-item[tera-type=fighting] .tera-icon {
  background-image: url("../img/types/fighting.png");
}

.type-item[tera-type=fire] .tera-icon {
  background-image: url("../img/types/fire.png");
}

.type-item[tera-type=flying] .tera-icon {
  background-image: url("../img/types/flying.png");
}

.type-item[tera-type=ghost] .tera-icon {
  background-image: url("../img/types/ghost.png");
}

.type-item[tera-type=grass] .tera-icon {
  background-image: url("../img/types/grass.png");
}

.type-item[tera-type=ground] .tera-icon {
  background-image: url("../img/types/ground.png");
}

.type-item[tera-type=ice] .tera-icon {
  background-image: url("../img/types/ice.png");
}

.type-item[tera-type=normal] .tera-icon {
  background-image: url("../img/types/normal.png");
}

.type-item[tera-type=poison] .tera-icon {
  background-image: url("../img/types/poison.png");
}

.type-item[tera-type=psychic] .tera-icon {
  background-image: url("../img/types/psychic.png");
}

.type-item[tera-type=rock] .tera-icon {
  background-image: url("../img/types/rock.png");
}

.type-item[tera-type=steel] .tera-icon {
  background-image: url("../img/types/steel.png");
}

.type-item[tera-type=water] .tera-icon {
  background-image: url("../img/types/water.png");
}

/* Typing colors and icons */
.bug, [type=bug] {
  background: -moz-linear-gradient(0deg, #8b9f18 43%, #bec720 55%);
  background: -webkit-linear-gradient(0deg, #8b9f18 43%, #bec720 55%);
  background: linear-gradient(0deg, #8b9f18 43%, #bec720 55%);
}

.dark, [type=dark] {
  background: -moz-linear-gradient(0deg, #4f3f3f 43%, #715a5b 55%);
  background: -webkit-linear-gradient(0deg, #4f3f3f 43%, #715a5b 55%);
  background: linear-gradient(0deg, #4f3f3f 43%, #715a5b 55%);
}

.dragon, [type=dragon] {
  background: -moz-linear-gradient(0deg, #444dcb 43%, #7387f0 55%);
  background: -webkit-linear-gradient(0deg, #444dcb 43%, #7387f0 55%);
  background: linear-gradient(0deg, #444dcb 43%, #7387f0 55%);
}

.electric, [type=electric] {
  background: -moz-linear-gradient(0deg, #f6bc00 43%, #ffcb03 55%);
  background: -webkit-linear-gradient(0deg, #f6bc00 43%, #ffcb03 55%);
  background: linear-gradient(0deg, #f6bc00 43%, #ffcb03 55%);
}

.fairy, [type=fairy] {
  background: -moz-linear-gradient(0deg, #ed6ce5 43%, #f89af3 55%);
  background: -webkit-linear-gradient(0deg, #ed6ce5 43%, #f89af3 55%);
  background: linear-gradient(0deg, #ed6ce5 43%, #f89af3 55%);
}

.fighting, [type=fighting] {
  background: -moz-linear-gradient(0deg, #fc7c00 43%, #fca606 55%);
  background: -webkit-linear-gradient(0deg, #fc7c00 43%, #fca606 55%);
  background: linear-gradient(0deg, #fc7c00 43%, #fca606 55%);
}

.fire, [type=fire] {
  background: -moz-linear-gradient(0deg, #dd2727 43%, #f94043 55%);
  background: -webkit-linear-gradient(0deg, #dd2727 43%, #f94043 55%);
  background: linear-gradient(0deg, #dd2727 43%, #f94043 55%);
}

.flying, [type=flying] {
  background: -moz-linear-gradient(0deg, #7fb4e9 43%, #b3d4f4 55%);
  background: -webkit-linear-gradient(0deg, #7fb4e9 43%, #b3d4f4 55%);
  background: linear-gradient(0deg, #7fb4e9 43%, #b3d4f4 55%);
}

.ghost, [type=ghost] {
  background: -moz-linear-gradient(0deg, #6d3d69 43%, #9e5a9c 55%);
  background: -webkit-linear-gradient(0deg, #6d3d69 43%, #9e5a9c 55%);
  background: linear-gradient(0deg, #6d3d69 43%, #9e5a9c 55%);
}

.grass, [type=grass] {
  background: -moz-linear-gradient(0deg, #409f26 43%, #5fca3d 55%);
  background: -webkit-linear-gradient(0deg, #409f26 43%, #5fca3d 55%);
  background: linear-gradient(0deg, #409f26 43%, #5fca3d 55%);
}

.ground, [type=ground] {
  background: -moz-linear-gradient(0deg, #8f4d24 43%, #bd7033 55%);
  background: -webkit-linear-gradient(0deg, #8f4d24 43%, #bd7033 55%);
  background: linear-gradient(0deg, #8f4d24 43%, #bd7033 55%);
}

.ice, [type=ice] {
  background: -moz-linear-gradient(0deg, #40d3f8 43%, #5bebff 55%);
  background: -webkit-linear-gradient(0deg, #40d3f8 43%, #5bebff 55%);
  background: linear-gradient(0deg, #40d3f8 43%, #5bebff 55%);
}

.normal, [type=normal] {
  background: -moz-linear-gradient(0deg, #9d9d9f 43%, #c7c7c7 55%);
  background: -webkit-linear-gradient(0deg, #9d9d9f 43%, #c7c7c7 55%);
  background: linear-gradient(0deg, #9d9d9f 43%, #c7c7c7 55%);
}

.poison, [type=poison] {
  background: -moz-linear-gradient(0deg, #8a3fc1 43%, #bd5ce3 55%);
  background: -webkit-linear-gradient(0deg, #8a3fc1 43%, #bd5ce3 55%);
  background: linear-gradient(0deg, #8a3fc1 43%, #bd5ce3 55%);
}

.psychic, [type=psychic] {
  background: -moz-linear-gradient(0deg, #eb4481 43%, #fe64b1 55%);
  background: -webkit-linear-gradient(0deg, #eb4481 43%, #fe64b1 55%);
  background: linear-gradient(0deg, #eb4481 43%, #fe64b1 55%);
}

.rock, [type=rock] {
  background: -moz-linear-gradient(0deg, #aba77b 43%, #d0c9b0 55%);
  background: -webkit-linear-gradient(0deg, #aba77b 43%, #d0c9b0 55%);
  background: linear-gradient(0deg, #aba77b 43%, #d0c9b0 55%);
}

.steel, [type=steel] {
  background: -moz-linear-gradient(0deg, #5d9cb0 43%, #86c8d4 55%);
  background: -webkit-linear-gradient(0deg, #5d9cb0 43%, #86c8d4 55%);
  background: linear-gradient(0deg, #5d9cb0 43%, #86c8d4 55%);
}

.water, [type=water] {
  background: -moz-linear-gradient(0deg, #267ee7 43%, #35b5f7 55%);
  background: -webkit-linear-gradient(0deg, #267ee7 43%, #35b5f7 55%);
  background: linear-gradient(0deg, #267ee7 43%, #35b5f7 55%);
}

.type-item, .trait-item {
  box-sizing: border-box;
  font-size: 14px;
  width: 105px;
  margin: 10px 15px 0 0;
  border: 1px solid rgba(0, 0, 0, 0.75);
  border-radius: 10px;
  overflow: hidden;
}
.type-item.tera, .trait-item.tera {
  border-top-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.type-item .type-name-container, .trait-item .type-name-container {
  background: rgba(0, 0, 0, 0.1);
  align-items: center;
  flex-grow: 1;
}
.type-item .type-name-container .type-name, .trait-item .type-name-container .type-name {
  padding: 5px 10px 5px 0;
  text-align: center;
  flex-grow: 1;
}
.type-item .tera-icon, .trait-item .tera-icon {
  width: 18px;
  height: 18px;
  margin: 0 2px 0 5px;
}
.type-item a, .trait-item a {
  background: rgba(0, 0, 0, 0.5);
  padding: 5px;
  text-decoration: none;
  color: #fff;
}
.type-item a:hover, .trait-item a:hover {
  background: rgba(0, 0, 0, 0.75);
}

.tera-icon {
  width: 85px;
  height: 85px;
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 15px;
}

.results-container {
  display: none;
}

#results {
  text-align: left;
  width: 100%;
}
#results th {
  top: 0px;
  position: sticky;
  background: #1d0441;
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
  z-index: 5;
}
#results th, #results td {
  padding: 10px;
}
#results tbody tr:nth-of-type(odd) {
  background: rgba(255, 255, 255, 0.1);
}
#results tbody tr {
  position: relative;
  top: 0;
  opacity: 1;
  -webkit-transition: all 250ms ease 0ms;
  -moz-transition: all 250ms ease 0ms;
  -o-transition: all 250ms ease 0ms;
  transition: all 250ms ease 0ms;
}
#results tbody tr.animate {
  top: 20px;
  opacity: 0;
}
#results .type-item {
  margin-top: 0;
  margin-right: 3px;
}
#results .type-item:not(.tera) .type-name-container {
  background: #1d0441;
  margin: 2px;
  border-radius: 10px;
}
#results a.score {
  color: #fff;
  text-decoration: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}

button#run, .modal button {
  margin-top: 20px;
  outline: none;
  border: none;
  padding: 10px;
  font-size: 20px;
  display: block;
  width: 230px;
  cursor: pointer;
  color: #fff;
  border: 2px solid #0a0018;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.6);
  background: -moz-linear-gradient(0deg, #46052c 43%, #990f44 55%);
  background: -webkit-linear-gradient(0deg, #46052c 43%, #990f44 55%);
  background: linear-gradient(0deg, #46052c 43%, #990f44 55%);
}
button#run:hover, .modal button:hover {
  background: -moz-linear-gradient(0deg, #4f0532 43%, #ad104c 55%);
  background: -webkit-linear-gradient(0deg, #4f0532 43%, #ad104c 55%);
  background: linear-gradient(0deg, #4f0532 43%, #ad104c 55%);
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.8);
}
button#run:active, .modal button:active {
  position: relative;
  top: 2px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
}

.results-section .table-container {
  max-height: 800px;
  overflow-y: auto;
}
.results-section .search-instructions {
  font-size: 12px;
  margin: 5px 0 10px 0;
  opacity: 0.5;
}
.results-section .results-controls {
  justify-content: space-between;
  flex-wrap: nowrap;
}
.results-section .results-controls a.results-options {
  display: block;
  width: 30px;
  height: 30px;
  margin-left: 20px;
  text-decoration: none;
  background: url("../img/options.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.score-details {
  align-items: center;
  justify-content: center;
  text-align: center;
}
.score-details .overall .score {
  font-size: 44px;
}
.score-details .offense .score, .score-details .defense .score {
  font-size: 26px;
}
.score-details .score {
  font-weight: bold;
}
.score-details .label {
  font-size: 12px;
}
.score-details span.multiply {
  margin: 0 10px;
}
.score-details .type-item {
  margin-top: 0;
}
.score-details .typings, .score-details .tera-type {
  flex-basis: 100%;
  justify-content: center;
}
.score-details .tera-type .type-item,
.score-details .traits {
  margin-bottom: 3px;
  margin-right: 0;
}
.score-details .traits {
  justify-content: center;
}
.score-details .tera-type > .label {
  flex-basis: 100%;
}
.score-details .full-row {
  flex-basis: 100%;
}
.score-details .border-bottom {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

footer {
  max-width: 1000px;
  text-align: center;
  font-size: 12px;
  margin: 80px auto 0 auto;
}
footer p:first-of-type {
  margin-top: 0;
}

/* MODAL WINDOW */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: scroll;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal .modal-container {
  position: relative;
  background: #52083b;
  background: -moz-linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background: -webkit-linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background: linear-gradient(167deg, #52083b 14%, #1d0441 72%);
  background-attachment: fixed;
  margin: 0 auto;
  max-width: 400px;
  vertical-align: middle;
  margin-top: 50px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 75px;
  flex-grow: 1;
}
.modal .modal-container .modal-header {
  padding: 10px;
  background: #0a0018;
  font-size: 24px;
  font-weight: bold;
}
.modal .modal-container .modal-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 10px;
  font-size: 30px;
  cursor: pointer;
}
.modal .modal-container .modal-close:after {
  content: "×";
}
.modal .modal-container .modal-content {
  padding: 10px;
}
.modal h4 {
  margin: 20px 0 10px 0;
}
.modal h4:first-of-type {
  margin-top: 0;
}
.modal select {
  font-size: 18px;
  width: 200px;
  margin-bottom: 10px;
}
.modal button {
  background: #990f44 !important;
  margin: 30px auto 0 auto;
  font-size: 18px;
  padding: 10px;
}
.modal button:hover {
  background: #ad104c !important;
}
.modal p {
  font-size: 14px;
  text-align: left;
}

.share-link-container {
  text-align: center;
  margin-top: 20px;
}
.share-link-container p {
  margin: 0;
}

.share-link {
  display: inline-block;
  background: #000;
  background-image: url("../../img/link.png");
  background-repeat: no-repeat;
  background-position: 6px center;
  background-size: 20px;
  padding: 10px 10px 10px 35px;
  border-radius: 8px;
  margin: 5px auto;
}
.share-link input {
  width: auto;
  display: inline-block;
  border: none;
  background: #0a0018;
  color: #990f44;
  padding: 5px;
  font-size: 18px;
}
.share-link .copy {
  display: inline-block;
  font-weight: bold;
  font-size: 13px;
  cursor: pointer;
  color: #fff;
  margin: 5px;
  border-radius: 8px;
}

.check {
  margin-top: 10px;
  cursor: pointer;
}
.check span {
  display: block;
  width: 17px;
  height: 17px;
  border-radius: 2px;
  background: #222;
  border: 2px solid #000;
  box-sizing: border-box;
  margin: 0px 10px 0 0;
  float: left;
}
.check.on span {
  font-size: 18px;
  padding-left: 1px;
  line-height: 10px;
  font-weight: bold;
  overflow: hidden;
  background: #990f44;
}
.check.on span:after {
  content: "✓";
}
.check:hover:not(.on) span {
  background: #333;
}

.traits {
  flex-wrap: wrap;
}
.traits .trait {
  font-size: 12px;
  background: #990f44;
  padding: 2px 6px;
  border-radius: 10px;
  margin: 4px 4px 0 0;
}

/* Responsive styling */
@media only screen and (max-width: 820px) {
  h1 {
    font-size: 26px;
  }

  h3 {
    font-size: 20px;
  }

  #main p {
    font-size: 14px;
    line-height: 20px;
  }

  .tera-type-container {
    margin-top: 30px;
  }
  .tera-type-container .flex {
    flex-direction: row-reverse;
  }
  .tera-type-container .tera-icon {
    width: 75px;
    height: 75px;
    margin-right: 0;
    margin-left: 15px;
  }

  #poke-select, .boss-section #tera-select {
    font-size: 18px;
    padding: 5px;
  }

  #poke-search, #poke-select, .boss-section #tera-select {
    width: 175px;
  }

  .type-item {
    font-size: 12px;
    margin: 10px 10px 0 0;
  }
  .type-item .type-name-container .type-name {
    padding: 3px 5px 3px 0;
  }

  .boss-section .boss-attack-types #attack-type-select {
    padding: 3px;
  }

  #results .type-item {
    width: 85px;
  }
  #results td, #results th {
    font-size: 14px;
    padding: 5px;
  }

  header a.home-link {
    font-size: 12px;
  }
}
@media only screen and (max-width: 480px) {
  #results td > .flex {
    display: block;
  }
  #results .type-item:nth-of-type(2) {
    margin-top: 5px;
  }
  #results .type-item {
    width: 75px;
  }
  #results .type-item .tera-icon {
    width: 12px;
    height: 12px;
  }
  #results .results-section .table-container {
    max-height: 550px;
  }
}
/* Ad styling and display */
#nitro-header-mobile,
#nitro-body-mobile,
#nitro-body-desktop,
#nitro-body-mobile,
#nitro-sidebar-right,
#nitro-sidebar-right-300,
#nitro-sidebar-left,
#nitro-sidebar-left-300 {
  display: none;
}

#nitro-header-mobile {
  overflow: hidden;
}

.mobile-ad-container,
.desktop-ad-container {
  overflow: hidden;
  clip-path: inset(0 0 0 0);
  margin: 0 auto 5px auto;
  display: none;
}

.section.battle .mobile-ad-container {
  margin: 10px auto 5px auto;
}

iframe#bh-div {
  display: none !important;
}

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .nitro-pin-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 25;
  }

  #nitro-header-mobile {
    display: block;
    margin: 0 auto;
  }

  #nitro-body-mobile {
    display: block;
  }

  .mobile-ad-container {
    display: block;
    width: 320px;
    height: 70px;
  }
}
@media only screen and (min-width: 768px) {
  #nitro-header-desktop {
    display: block;
  }

  #nitro-body-desktop {
    display: block;
  }

  .desktop-ad-container {
    display: block;
    width: 728px;
    height: 110px;
    margin: 20px auto;
  }
  .desktop-ad-container.nitro-header {
    margin: 0 auto;
  }
}
@media only screen and (min-width: 1390px) and (max-width: 1649px) {
  #nitro-sidebar-left {
    display: block;
    position: fixed;
    top: 95px;
    left: 20px;
  }

  #nitro-sidebar-right {
    display: block;
    position: fixed;
    top: 95px;
    right: 20px;
  }
}
@media only screen and (min-width: 1650px) {
  #nitro-sidebar-left-300 {
    display: block;
    position: fixed;
    top: 95px;
    left: 20px;
  }

  #nitro-sidebar-right-300 {
    display: block;
    position: fixed;
    top: 95px;
    right: 20px;
  }
}

/*# sourceMappingURL=tera-style.css.map */
