import { Pokemon, League, PokemonType } from './pokemon';

// Re-export League for convenience
export type { League } from './pokemon';

export interface RankingEntry {
  pokemon: Pokemon;
  score: number;
  rank: number;
  wins: number;
  losses: number;
  rating: number;
  lead: number;
  closer: number;
  switch: number;
  charger: number;
  attacker: number;
  consistency: number;
  threats: RankingThreat[];
  counters: RankingCounter[];
}

export interface RankingThreat {
  pokemon: Pokemon;
  rating: number;
  matchup: 'win' | 'loss' | 'tie';
}

export interface RankingCounter {
  pokemon: Pokemon;
  rating: number;
  effectiveness: number;
}

export interface RankingFilters {
  league: League;
  cup?: string;
  category?: RankingCategory;
  types?: PokemonType[];
  tags?: string[];
  searchQuery?: string;
  minRating?: number;
  maxRating?: number;
}

export type RankingCategory = 
  | 'overall' 
  | 'leads' 
  | 'closers' 
  | 'switches' 
  | 'chargers' 
  | 'attackers';

export interface RankingMetadata {
  league: League;
  cup?: string;
  lastUpdated: string;
  totalPokemon: number;
  algorithm: string;
  version: string;
}

export interface RankingResponse {
  rankings: RankingEntry[];
  metadata: RankingMetadata;
  filters: RankingFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
