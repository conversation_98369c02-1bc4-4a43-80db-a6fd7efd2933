# PvPoke Next.js Migration

A modern rewrite of PvPoke using Next.js, TypeScript, Zustand, and Tailwind CSS.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

2. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── components/          # React components
│   ├── common/         # Reusable UI components
│   ├── battle/         # Battle simulator components
│   ├── rankings/       # Rankings components
│   └── team-builder/   # Team building components
├── stores/             # Zustand state stores
├── services/           # Business logic layer
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── styles/             # Global styles and Tailwind CSS

pages/                  # Next.js pages (file-based routing)
public/                 # Static assets
```

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with Pages Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with Pokemon-themed design tokens
- **State Management**: Zustand
- **Testing**: Jest + React Testing Library
- **Code Quality**: ESLint + Prettier

## 🎨 Design System

The project includes a custom Tailwind configuration with Pokemon-themed design tokens:

### Pokemon Type Colors
- Fire: `bg-fire` (#F08030)
- Water: `bg-water` (#6890F0)
- Grass: `bg-grass` (#78C850)
- Electric: `bg-electric` (#F8D030)
- And more...

### League Colors
- Great League: `bg-league-great`
- Ultra League: `bg-league-ultra`
- Master League: `bg-league-master`

### Custom Components
- `.pokemon-card` - Styled Pokemon cards
- `.type-badge` - Pokemon type indicators
- `.btn-primary` / `.btn-secondary` - Consistent button styles

## 🧪 Testing

Run tests:
```bash
npm test
# or
yarn test
# or
pnpm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

Generate coverage report:
```bash
npm run test:coverage
```

## 📝 Development Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run type-check` - Run TypeScript type checking

## 🔧 Configuration

### ESLint
Developer-friendly configuration that catches real issues without being overly restrictive.

### Prettier
Consistent code formatting with sensible defaults.

### TypeScript
Strict mode enabled with path mapping for clean imports:
- `@/*` → `src/*`
- `@/components/*` → `src/components/*`
- `@/stores/*` → `src/stores/*`
- etc.

## 📋 Migration Progress

This project is part of migrating the legacy PvPoke codebase to modern technologies.

**See detailed task tracking in [TASKS.md](./TASKS.md)**

For the complete implementation plan and requirements, see:
- Requirements: `.kiro/specs/pvpoke-modern-migration/requirements.md`
- Design: `.kiro/specs/pvpoke-modern-migration/design.md`
- Implementation Plan: `.kiro/specs/pvpoke-modern-migration/tasks.md`

## 🤝 Contributing

1. Follow the existing code style and conventions
2. Write tests for new functionality
3. Update documentation as needed
4. Ensure all tests pass before submitting changes

## 📄 License

This project maintains the same license as the original PvPoke project.
