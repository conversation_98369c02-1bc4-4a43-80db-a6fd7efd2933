/* CSS Document */

$color-blue-dark:#003462;
$dark: #080c10;
$font-color: #f9fdff;
$color-gold:#ffcf01;
$link-color:#3eca9f;
$green-medium: #203c49;
$green-faded: #183737;
$green-light: #0f6647;

body{
	background-color:#030509;
	background-image:url('../../img/themes/sunflower/sunflower-bg-night.jpg');
	color:$font-color;
}

a{
	color:$link-color;
}

.section.white,
.poke .poke-stats
{
	background:$dark;
}

select, .league-select, .cup-select,
option, .category-select optgroup,
.poke input, .poke select,
input,
.poke-search,
textarea{
    background: $color-blue-dark;
	border: 1px solid $green-light;
    color: $font-color;

	&::-webkit-input-placeholder {
	  color: $link-color;
	}
	&::-moz-placeholder {
	  color: $link-color;
	}
	&:-ms-input-placeholder {
	  color: $link-color;
	}
	&:-moz-placeholder {
	  color: $link-color;
	}

	&:focus, &:focus-visible{
		outline: 1px solid $link-color;
	}
}

.league-select, .cup-select, .format-select, .slot-select, .category-select{
	background: $color-blue-dark;
}

.poke .poke-select{
	border: 2px solid $color-blue-dark;
	background: $dark;
}

.modal .modal-container .poke-search{
	background: $color-blue-dark !important;
}

.modal .sandbox-move-select .move-select option{
	background: #222 !important;
}

.poke input, .poke select{
	border:1px solid $green-light;

	&::-webkit-input-placeholder {
	  color: $link-color !important;
	}
	&::-moz-placeholder {
	  color: $link-color !important;
	}
	&:-ms-input-placeholder {
	  color: $link-color !important;
	}
	&:-moz-placeholder {
	  color: $link-color !important;
	}
}

.poke .form-select-container .form-select, .poke .form-select-container a.form-link{
	color: $link-color;
}

.poke-search{
	border: 1px solid $green-light;
}

.poke input.poke-search{
	background: $color-blue-dark;
	border: 1px solid $green-light;

	&::-webkit-input-placeholder {
	  color: $green-light;
	}
	&::-moz-placeholder {
	  color: $green-light;
	}
	&:-ms-input-placeholder {
	  color: $green-light;
	}
	&:-moz-placeholder {
	  color: $green-light;
	}
}

header{
	background:$dark;

	.menu a,
	.title a{
		color:$font-color;
	}

	.hamburger .meat{
		background:$font-color;
	}

	.icon-battle{
		background-image:url('../../img/themes/sunflower/nav-battle-white.png');
	}

	.icon-train{
		background-image:url('../../img/themes/sunflower/nav-train-white.png');
	}

	.icon-rankings{
		background-image:url('../../img/themes/sunflower/nav-rankings-white.png');
	}

	.icon-team{
		background-image:url('../../img/themes/sunflower/nav-team-white.png');
	}

	.icon-contribute{
		background-image:url('../../img/themes/sunflower/nav-heart-white.png');
	}
}
h1{
	color:$font-color;
}

.poke{
	.poke-stats{
		.stat-container{
			color:$font-color;
		}

		.stat-container .bar-back .bar{
			background: rgba(255,255,255,0.6);
		}

		a{
			color:$link-color;
		}

		h3.section-title{
			color:$font-color;
		}

		.move-select, .type{
			color:#000;
		}

		.dark, .dragon, .ghost {
			color:#eee;
		}

	}

	.form-select-container a.form-link{
		background: #222;
	}
}

.check.on span{
	background: $link-color !important;
	border: 2px solid $color-blue-dark;
}

.check span{
	border: 2px solid $green-medium;
	background: $color-blue-dark;
}

.check:hover:not(.on) span{
	background: $green-faded;
}


a.toggle{
	color:$link-color;
}

.move-bars .move-bar.active {
    border: 2px solid white;
}

.battle-results .timeline-container{
	background:rgba(50,50,50,0.5);
	border:1px solid #888;

	.timeline{
		border-top: 1px dashed #888;

		.item-container{
			.item.fast{
				border:1px solid #888
			}
		}
	}

	.tracker{
		border-left:1px solid #aaa;
	}
}

.sandbox.clear-btn{
	color:#000;
}

.stats-table tr:nth-child(2n) {
    background: rgba(255,255,255, 0.15);
}

.histograms .histogram .chart {
    border: 1px solid #888;
    background: #222;

	.segment:nth-of-type(10) {
    	border-right: 1px dashed #888;
	}
}

.rank{
	color:#000;
}

.ranking-categories{
	a{
		color:$link-color;

		&.selected{
			background: $color-blue-dark;
			color:$link-color;
		}
	}
}

.modal{
	.modal-header{
		color:#000;
	}

	.modal-container{
		border:2px solid $green-faded;
	}

	.modal-content{
		background: $dark;
	}
}

.section.typings{
	li span{
		color:#000;
	}

	table{
		color: #000;
		background: rgba(255,255,255,0.6);
		border-radius: 12px;
	}
}

.contribute .supporters .supporter{
	background:#222;
}

.battle-window{
	box-shadow:0px 5px 15px #000;

	.scene .background{
		background-image: url('../../img/themes/night/pvp-background.jpg');
	}

	.top .team-indicator{
		color:#000;
	}

	.end-screen-container .damage .damage-section{
		background:#222;

		.avg-line{
			border:1px dotted rgba(255, 255, 255, 0.5)
		}
	}
}

.team-select .self, .team-select .opponent{
	background:#444;
}

.custom-rankings{
	.include .filter{
		background:#29505b;

		.remove{
			color:#ff8b8b;
		}
	}

	.exclude .filter{
		background:#582b2b;

		a.toggle{
			color:#f1b9b9;
		}

		.remove{
			color:#ff8b8b;
		}
	}
}

.custom-rankings-import textarea.import,
.custom-rankings-list .pokemon-list{
	background:#222;
	color:$font-color;
}

.poke-search-container{
	a.search-info{
		color:$link-color;
		border:1px solid $link-color;
	}
}

.battle-results.matrix .table-container tbody th,
.alternatives-table tbody th,
.threats-table tbody th,
.meta-table tbody th{
	background:#000;
	color:#eee;
}

.battle-results.matrix .table-container tr:nth-of-type(2n) th,
.alternatives-table tr:nth-of-type(2n) th,
.threats-table tr:nth-of-type(2n) th,
.meta-table tr:nth-of-type(2n) th{
	background:#444;
	color:#eee;
}

.electric{
	color:#000;
}

.pokebox a.open-pokebox{
	background-color:$color-blue-dark;
	color:$font-color;
}

.modal .pokebox-options a.pokebox-edit,
.modal .pokebox-options a.pvpoke-sponsor{
	color:$font-color;
	background-color:$dark;
}

.modal .iv-rank-details .iv-rank-result.primary{
	border: 1px solid $font-color;
}

.modal .iv-rank-details h3{
	border-bottom: 1px solid $font-color;
}

.matrix-table, .meta-table, .threats-table, .alternatives-table{
	thead td:first-of-type{
		background-image:url('../../img/matrix-arrow-header-dark.png') !important;
	}
}

.poke .form-group{
	border-color: $link-color;
}

.poke .form-group .option{
	background: $color-blue-dark;

	border-color: $link-color;

	&.on{
		background:$green-light;
		-moz-box-shadow: inset 0 0 10px $link-color;
	    -webkit-box-shadow: inset 0 0 10px $link-color;
	    box-shadow: inset 0 0 10px $link-color;
	}
}

.home .flex.new-header a{
	color: $link-color;
	background-image: url('../../img/themes/sunflower/rss-green.png');
}

.feed-container{
	background: rgba(255,255,255,0.1);
	border: 1px solid rgba(255,255,255,0.3);
}

.feed .news-item{
	border-bottom: 1px solid rgba(255,255,255,0.15);
}

@media only screen and (min-width: 721px){
	.menu .submenu .submenu-wrap{
		background:$color-blue-dark;
	}

	header .menu-content > a:hover, header .menu-content > a.selected,
	header .menu-content > .parent-menu:hover > a, header .menu-content .parent-menu > a.selected,
	.ranking-categories a.selected, .ranking-categories a:hover{
		background-color: $color-blue-dark !important;
	}
}
