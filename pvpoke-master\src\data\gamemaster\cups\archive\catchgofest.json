{"name": "catchgofest", "title": "Catch Cup (GO Fest)", "include": [{"filterType": "id", "values": ["magnemite", "magneton", "magnezone", "grimer_alolan", "muk_alolan", "hitmonchan", "baltoy", "claydol", "wormadam_trash", "bron<PERSON>", "bronzong", "pidove", "tranquill", "unfezant", "trubbish", "gar<PERSON><PERSON>", "gothita", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "go<PERSON>", "golurk", "litten", "torracat", "incineroar", "pikachu", "r<PERSON><PERSON>", "weezing_galarian", "girafarig", "dunsparce", "<PERSON>r<PERSON><PERSON>", "pupitar", "tyranitar", "numel", "camerupt", "trapinch", "vibrava", "flygon", "buizel", "floatzel", "patrat", "watchog", "drilbur", "excadrill", "shelmet", "accelgor", "rufflet", "braviary", "litleo", "pyroar", "axew", "fraxure", "haxorus", "mudkip", "marshtomp", "swampert", "seedot", "nuzleaf", "shiftry", "shroomish", "breloom", "slakoth", "vigoroth", "slaking", "turtwig", "grotle", "torterra", "chim<PERSON>r", "monferno", "infernape", "venipede", "whirlipede", "scolipede", "karrablast", "escavalier", "binacle", "skrelp", "dragalge", "rowlet", "dart<PERSON>", "decid<PERSON><PERSON>", "pancha<PERSON>", "pangoro", "omanyte", "omastar", "swin<PERSON>", "piloswine", "ma<PERSON><PERSON>", "wingull", "pelipper", "meditite", "medicham", "wailmer", "wailord", "spheal", "sealeo", "walrein", "pip<PERSON>p", "prin<PERSON><PERSON><PERSON>", "empoleon", "vanillite", "vanillish", "vanilluxe", "bergmite", "avalugg", "popp<PERSON>", "brionne", "primarina", "darum<PERSON>_galarian", "darmanitan_galarian_standard", "rockruff", "lycanroc", "lycanroc_midnight", "d<PERSON><PERSON><PERSON>", "geodude", "graveler", "golem", "poliwag", "poliwhirl", "poliwrath", "politoed", "vulpix", "ninetales", "hippopotas", "hippo<PERSON><PERSON>", "snover", "abomasnow", "<PERSON><PERSON>", "diglett_alolan", "<PERSON><PERSON>o", "dug<PERSON><PERSON>_alolan", "magi<PERSON><PERSON>", "gyarados", "dratini", "dragonair", "bagon", "shelgon", "murkrow", "honch<PERSON><PERSON>", "hoppip", "skip<PERSON>", "<PERSON><PERSON><PERSON>", "yanma", "yanmega", "mantine", "umbreon", "espeon", "vaporeon", "jolteon", "flareon", "sylveon", "leafeon", "glaceon", "sudowoodo", "rhyhorn", "rhydon", "rhyperior", "ralts", "kirlia", "gardevoir", "gallade", "beldum", "metang", "metagross", "anorith", "arm<PERSON>", "lileep", "cradily", "snubbull", "gran<PERSON>", "onix", "steelix", "spinda", "gible", "gabite", "bulbasaur", "ivysaur", "venusaur", "charmander", "charmeleon", "charizard", "squirtle", "wartortle", "blastoise", "chikorita", "bayleef", "meganium", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "totodile", "cro<PERSON><PERSON>", "feraligatr", "treecko", "g<PERSON><PERSON>", "sceptile", "torchic", "combusken", "blaziken", "koffing", "weezing", "venonat", "venomoth", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "kabuto", "kabutops", "aerodactyl", "grimer", "muk", "<PERSON><PERSON><PERSON>", "bunnelby", "diggersby", "chinchou", "lanturn", "stunfisk", "vullaby", "mandibuzz", "pawniard", "bisharp", "scraggy", "scrafty", "sandile", "krokorok", "deino", "<PERSON><PERSON><PERSON>", "s<PERSON><PERSON><PERSON>", "drapion", "saland<PERSON>", "salazzle", "absol", "starly", "staravia", "golbat", "stunky", "skuntank", "raticate_alolan", "beedrill", "forretress", "<PERSON><PERSON><PERSON>", "gran<PERSON>_shadow", "machamp_shadow", "sableye", "electrode", "bibarel", "gardevoir_shadow", "mr_mime_galarian", "unown", "torkoal", "tropius", "klink", "klang", "klinklang", "garbodor"]}], "exclude": [], "partySize": 3}