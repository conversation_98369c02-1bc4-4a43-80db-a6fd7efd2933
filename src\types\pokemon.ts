// Core Pokemon types based on the design document

export type PokemonType = 
  | 'normal' | 'fire' | 'water' | 'electric' | 'grass' | 'ice'
  | 'fighting' | 'poison' | 'ground' | 'flying' | 'psychic' | 'bug'
  | 'rock' | 'ghost' | 'dragon' | 'dark' | 'steel' | 'fairy';

export interface BaseStats {
  attack: number;
  defense: number;
  stamina: number;
}

export interface IVs {
  attack: number;
  defense: number;
  stamina: number;
}

export interface MoveBuffs {
  target: 'self' | 'opponent';
  stat: 'attack' | 'defense';
  stages: number;
  chance: number;
}

export interface Move {
  moveId: string;
  name: string;
  type: PokemonType;
  power: number;
  energy: number;
  energyGain?: number;
  cooldown: number;
  buffs?: MoveBuffs[];
  archetype: string;
}

export interface Pokemon {
  speciesId: string;
  speciesName: string;
  dex: number;
  types: PokemonType[];
  baseStats: BaseStats;
  fastMoves: Move[];
  chargedMoves: Move[];
  tags: string[];
  buddyDistance: number;
  thirdMoveCost: number;
  released: boolean;
}

export interface BattlePokemon extends Pokemon {
  level: number;
  ivs: IVs;
  selectedMoves: {
    fastMove: Move;
    chargedMoves: Move[];
  };
  cp: number;
  hp: number;
}

export type League = 'great' | 'ultra' | 'master' | 'little' | 'premier';

export interface Cup {
  name: string;
  title: string;
  include: string[];
  exclude: string[];
  overrides: string[];
}

export interface PokemonFilters {
  types?: PokemonType[];
  leagues?: League[];
  searchQuery?: string;
  tags?: string[];
}
