[{"name": "<PERSON><PERSON><PERSON>", "slug": "arrohh", "img": "arrohh", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "Season 1 ranked #38 in the world Ace Trainer Elite 4 Taco Truck GoStadium Coach just keep tapping", "link": "https://twitter.com/TheArrohh", "pokemon": [{"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["SHADOW_BALL", "POISON_FANG"]}, {"speciesId": "bibarel", "fastMove": "WATER_GUN", "chargedMoves": ["SURF", "HYPER_FANG"]}, {"speciesId": "muk_alolan", "fastMove": "SNARL", "chargedMoves": ["DARK_PULSE", "SLUDGE_WAVE"]}, {"speciesId": "claydol", "fastMove": "CONFUSION", "chargedMoves": ["EARTH_POWER", "PSYCHIC"]}, {"speciesId": "pidgeot", "fastMove": "WING_ATTACK", "chargedMoves": ["AERIAL_ACE", "HURRICANE"]}]}, {"name": "HouseStark93", "slug": "housestark93", "img": "housestark93", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "Man. Looking to be ranked #1 globally for Pogo pvp and to continue to grow the pvp community. Season 1 rank: #11", "link": "https://twitter.com/HouseStark_93", "pokemon": [{"speciesId": "escavalier", "fastMove": "COUNTER", "chargedMoves": ["AERIAL_ACE", "MEGAHORN"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["SHADOW_BALL", "POISON_FANG"]}, {"speciesId": "qwilfish", "fastMove": "WATER_GUN", "chargedMoves": ["FELL_STINGER", "AQUA_TAIL"]}, {"speciesId": "bibarel", "fastMove": "WATER_GUN", "chargedMoves": ["SURF", "HYPER_FANG"]}, {"speciesId": "wormadam_trash", "fastMove": "CONFUSION", "chargedMoves": ["IRON_HEAD", "BUG_BUZZ"]}, {"speciesId": "flygon", "fastMove": "MUD_SHOT", "chargedMoves": ["DRAGON_CLAW", "EARTH_POWER"]}]}, {"name": "4TheBattles", "slug": "<PERSON><PERSON><PERSON><PERSON>", "img": "<PERSON><PERSON><PERSON><PERSON>", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "370 mil xp, 50k Battle Girl, 7600 Great League wins, aspiring to help new trainers succeed in PvP. GoStadium Team Analyst. Team ExeGGutors! Wobbuffet is life.", "link": "https://twitter.com/gingerlykimber1", "pokemon": [{"speciesId": "medicham", "fastMove": "COUNTER", "chargedMoves": ["ICE_PUNCH", "PSYCHIC"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["SHADOW_BALL", "BONE_CLUB"]}, {"speciesId": "rhydon", "fastMove": "MUD_SLAP", "chargedMoves": ["SURF", "STONE_EDGE"]}, {"speciesId": "probopass", "fastMove": "ROCK_THROW", "chargedMoves": ["MAGNET_BOMB", "ROCK_SLIDE"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["POISON_FANG", "SHADOW_BALL"]}, {"speciesId": "wigglytuff", "fastMove": "CHARM", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}]}, {"name": "<PERSON><PERSON><PERSON>", "slug": "arrohh_rose", "img": "arrohh", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "Season 1 ranked #38 in the world Ace Trainer Elite 4 Taco Truck GoStadium Coach just keep tapping", "link": "https://twitter.com/TheArrohh", "pokemon": [{"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["SHADOW_BALL", "BONE_CLUB"]}, {"speciesId": "lickilicky", "fastMove": "LICK", "chargedMoves": ["BODY_SLAM", "EARTHQUAKE"]}, {"speciesId": "magcargo", "fastMove": "ROCK_THROW", "chargedMoves": ["STONE_EDGE", "OVERHEAT"]}, {"speciesId": "medicham", "fastMove": "COUNTER", "chargedMoves": ["ICE_PUNCH", "PSYCHIC"]}, {"speciesId": "vileplume", "fastMove": "RAZOR_LEAF", "chargedMoves": ["SLUDGE_BOMB", "MOONBLAST"]}, {"speciesId": "wigglytuff", "fastMove": "CHARM", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}]}, {"name": "R6RacingR6", "slug": "r6racingr6", "img": "r6racingr6", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "Proud to be #DadsThatPvP...Father of the 7 yr old Kid Prodigy @BaDookie1111 #KidsThatPvP ... Proud supporter of #GirlsThatPvP.", "link": "https://twitter.com/R6RacingR6", "pokemon": [{"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["SHADOW_BALL", "BONE_CLUB"]}, {"speciesId": "machamp", "fastMove": "COUNTER", "chargedMoves": ["CROSS_CHOP", "ROCK_SLIDE"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["POISON_FANG", "SHADOW_BALL"]}, {"speciesId": "probopass", "fastMove": "ROCK_THROW", "chargedMoves": ["MAGNET_BOMB", "ROCK_SLIDE"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"]}, {"speciesId": "gliscor", "fastMove": "WING_ATTACK", "chargedMoves": ["NIGHT_SLASH", "EARTHQUAKE"]}]}, {"name": "ThoTechtical", "slug": "thotechical", "img": "thotechtical", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "I like Pokémon Go. Moderator @europeanpokemon & battle analyst @gostadiumPvP.", "link": "https://twitter.com/ThoTechtical", "pokemon": [{"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["SHADOW_BALL", "BONE_CLUB"]}, {"speciesId": "machamp", "fastMove": "COUNTER", "chargedMoves": ["CROSS_CHOP", "ROCK_SLIDE"]}, {"speciesId": "rhydon", "fastMove": "MUD_SLAP", "chargedMoves": ["SURF", "STONE_EDGE"]}, {"speciesId": "probopass", "fastMove": "SPARK", "chargedMoves": ["MAGNET_BOMB", "ROCK_SLIDE"]}, {"speciesId": "charizard", "fastMove": "FIRE_SPIN", "chargedMoves": ["DRAGON_CLAW", "BLAST_BURN"]}, {"speciesId": "wigglytuff", "fastMove": "CHARM", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}]}, {"name": "WildSusanBoyle", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "Machine • Team Canada • Valor • 100M • PvP coach • Canada's 1st ACE Trainer • Ultimate Showdown Champion • Silph Global Rank #34 (Season 1)", "link": "https://twitter.com/WildSusanBoyle", "pokemon": [{"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["SHADOW_BALL", "BONE_CLUB"]}, {"speciesId": "machamp", "fastMove": "COUNTER", "chargedMoves": ["CROSS_CHOP", "ROCK_SLIDE"]}, {"speciesId": "gliscor", "fastMove": "WING_ATTACK", "chargedMoves": ["NIGHT_SLASH", "EARTHQUAKE"]}, {"speciesId": "probopass", "fastMove": "SPARK", "chargedMoves": ["MAGNET_BOMB", "ROCK_SLIDE"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["SHADOW_SNEAK", "FOUL_PLAY"]}, {"speciesId": "gran<PERSON>", "fastMove": "CHARM", "chargedMoves": ["CLOSE_COMBAT", "CRUNCH"]}]}, {"name": "ValorAsh", "slug": "valorash_rose", "img": "valorash", "cup": "rose", "cupName": "<PERSON>", "league": 1500, "description": "PokemonGo PvP player. Like <PERSON> I am chasing my childhood dream of becoming the best there ever was. Was #1 for a bit.", "link": "https://twitter.com/ash_valor", "pokemon": [{"speciesId": "machamp", "fastMove": "COUNTER", "chargedMoves": ["CLOSE_COMBAT", "ROCK_SLIDE"], "level": 18.5, "ivs": [0, 14, 11]}, {"speciesId": "steelix", "fastMove": "DRAGON_TAIL", "chargedMoves": ["CRUNCH", "EARTHQUAKE"], "level": 24, "ivs": [0, 14, 15]}, {"speciesId": "haunter", "fastMove": "SHADOW_CLAW", "chargedMoves": ["SHADOW_PUNCH", "SHADOW_BALL"], "level": 28.5, "ivs": [1, 15, 14]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"], "level": 27.5, "ivs": [0, 15, 14]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "POWER_GEM"], "level": 41, "ivs": [15, 15, 15]}, {"speciesId": "charizard", "fastMove": "FIRE_SPIN", "chargedMoves": ["BLAST_BURN", "DRAGON_CLAW"], "level": 19.5, "ivs": [0, 15, 13]}]}]