{
  "extends": [
    "next/core-web-vitals",
    "prettier"
  ],
  "rules": {
    // Relaxed rules for better DX
    "@next/next/no-img-element": "warn",
    "react/no-unescaped-entities": "warn",
    "react-hooks/exhaustive-deps": "warn",
    
    // Helpful rules that catch real issues
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
    "prefer-const": "warn",
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    
    // Consistency rules
    "quotes": ["warn", "single", { "avoidEscape": true }],
    "semi": ["warn", "always"]
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2021,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  }
}
