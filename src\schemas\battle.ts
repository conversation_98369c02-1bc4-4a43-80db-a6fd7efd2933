import { z } from 'zod';
import { BattlePokemonSchema, MoveSchema } from './pokemon';

// Damage result schema
export const DamageResultSchema = z.object({
  damage: z.number().min(0),
  effectiveness: z.number().min(0),
  isCritical: z.boolean(),
});

// Battle event schema
export const BattleEventSchema = z.object({
  turn: z.number().min(0),
  actor: z.enum(['pokemon1', 'pokemon2']),
  action: z.enum(['fast', 'charged', 'switch', 'shield']),
  move: MoveSchema.optional(),
  damage: z.number().optional(),
  energy: z.number().optional(),
  hp: z.number().optional(),
});

// Battle Pokemon result schema
export const BattlePokemonResultSchema = z.object({
  pokemon: BattlePokemonSchema,
  remainingHp: z.number().min(0),
  energyUsed: z.number().min(0),
  damageDealt: z.number().min(0),
  damageTaken: z.number().min(0),
  movesUsed: z.object({
    fast: z.number().min(0),
    charged: z.number().min(0),
  }),
});

// Battle result schema
export const BattleResultSchema = z.object({
  winner: z.enum(['pokemon1', 'pokemon2', 'tie']),
  pokemon1: BattlePokemonResultSchema,
  pokemon2: BattlePokemonResultSchema,
  timeline: z.array(BattleEventSchema),
  duration: z.number().min(0),
  turns: z.number().min(0),
});

// Battle options schema
export const BattleOptionsSchema = z.object({
  shields: z.object({
    pokemon1: z.number().min(0).max(2),
    pokemon2: z.number().min(0).max(2),
  }),
  strategy: z.enum(['neutral', 'aggressive', 'defensive']),
  allowSwitching: z.boolean(),
  timeLimit: z.number().min(0).optional(),
});

// Type effectiveness schema
export const TypeEffectivenessSchema = z.record(
  z.string(),
  z.record(z.string(), z.number())
);

// Battle simulation request schema
export const BattleSimulationRequestSchema = z.object({
  pokemon1: z.object({
    speciesId: z.string().min(1),
    level: z.number().min(1).max(50),
    ivs: z.object({
      attack: z.number().min(0).max(15),
      defense: z.number().min(0).max(15),
      stamina: z.number().min(0).max(15),
    }),
    fastMove: z.string().min(1),
    chargedMoves: z.array(z.string().min(1)).min(1).max(2),
  }),
  pokemon2: z.object({
    speciesId: z.string().min(1),
    level: z.number().min(1).max(50),
    ivs: z.object({
      attack: z.number().min(0).max(15),
      defense: z.number().min(0).max(15),
      stamina: z.number().min(0).max(15),
    }),
    fastMove: z.string().min(1),
    chargedMoves: z.array(z.string().min(1)).min(1).max(2),
  }),
  options: BattleOptionsSchema,
});

// Export types inferred from schemas
export type DamageResult = z.infer<typeof DamageResultSchema>;
export type BattleEvent = z.infer<typeof BattleEventSchema>;
export type BattlePokemonResult = z.infer<typeof BattlePokemonResultSchema>;
export type BattleResult = z.infer<typeof BattleResultSchema>;
export type BattleOptions = z.infer<typeof BattleOptionsSchema>;
export type TypeEffectiveness = z.infer<typeof TypeEffectivenessSchema>;
export type BattleSimulationRequest = z.infer<typeof BattleSimulationRequestSchema>;
