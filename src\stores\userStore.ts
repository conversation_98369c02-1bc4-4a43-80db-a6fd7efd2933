import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  UserPreferences, 
  SavedTeam, 
  UIState, 
  Notification,
  Breadcrumb,
  League,
  PokemonType 
} from '@/types';

interface UserState {
  // User preferences
  preferences: UserPreferences;
  savedTeams: SavedTeam[];
  favoritesPokemon: string[];
  recentSearches: string[];
  
  // UI state
  ui: UIState;
  
  // Actions
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  saveTeam: (team: SavedTeam) => void;
  deleteTeam: (teamId: string) => void;
  updateTeam: (teamId: string, updates: Partial<SavedTeam>) => void;
  addFavoritePokemon: (speciesId: string) => void;
  removeFavoritePokemon: (speciesId: string) => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  
  // UI actions
  setSidebarOpen: (open: boolean) => void;
  setModalOpen: (modalId: string | null) => void;
  setLoading: (loading: boolean) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  removeNotification: (id: string) => void;
  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void;
}

const defaultPreferences: UserPreferences = {
  defaultLeague: 'great',
  favoriteTypes: [],
  theme: 'auto',
  compactMode: false,
  showIVs: true,
  showCPs: true,
  animationsEnabled: true,
  soundEnabled: false,
};

const defaultUIState: UIState = {
  sidebarOpen: false,
  modalOpen: null,
  loading: false,
  notifications: [],
  breadcrumbs: [],
};

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // Initial state
      preferences: defaultPreferences,
      savedTeams: [],
      favoritesPokemon: [],
      recentSearches: [],
      ui: defaultUIState,
      
      // Preference actions
      updatePreferences: (newPreferences) =>
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences },
        })),
      
      // Team actions
      saveTeam: (team) =>
        set((state) => ({
          savedTeams: [...state.savedTeams, team],
        })),
      
      deleteTeam: (teamId) =>
        set((state) => ({
          savedTeams: state.savedTeams.filter(team => team.id !== teamId),
        })),
      
      updateTeam: (teamId, updates) =>
        set((state) => ({
          savedTeams: state.savedTeams.map(team =>
            team.id === teamId
              ? { ...team, ...updates, updatedAt: new Date().toISOString() }
              : team
          ),
        })),
      
      // Favorites actions
      addFavoritePokemon: (speciesId) =>
        set((state) => ({
          favoritesPokemon: state.favoritesPokemon.includes(speciesId)
            ? state.favoritesPokemon
            : [...state.favoritesPokemon, speciesId],
        })),
      
      removeFavoritePokemon: (speciesId) =>
        set((state) => ({
          favoritesPokemon: state.favoritesPokemon.filter(id => id !== speciesId),
        })),
      
      // Search history actions
      addRecentSearch: (query) =>
        set((state) => {
          const trimmedQuery = query.trim();
          if (!trimmedQuery) return state;
          
          const filtered = state.recentSearches.filter(search => search !== trimmedQuery);
          return {
            recentSearches: [trimmedQuery, ...filtered].slice(0, 10), // Keep last 10
          };
        }),
      
      clearRecentSearches: () =>
        set({ recentSearches: [] }),
      
      // UI actions
      setSidebarOpen: (open) =>
        set((state) => ({
          ui: { ...state.ui, sidebarOpen: open },
        })),
      
      setModalOpen: (modalId) =>
        set((state) => ({
          ui: { ...state.ui, modalOpen: modalId },
        })),
      
      setLoading: (loading) =>
        set((state) => ({
          ui: { ...state.ui, loading },
        })),
      
      addNotification: (notification) => {
        const id = Math.random().toString(36).substr(2, 9);
        const createdAt = new Date().toISOString();
        
        set((state) => ({
          ui: {
            ...state.ui,
            notifications: [
              ...state.ui.notifications,
              { ...notification, id, createdAt },
            ],
          },
        }));
        
        // Auto-remove notification after duration
        if (notification.duration && notification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration);
        }
      },
      
      removeNotification: (id) =>
        set((state) => ({
          ui: {
            ...state.ui,
            notifications: state.ui.notifications.filter(n => n.id !== id),
          },
        })),
      
      setBreadcrumbs: (breadcrumbs) =>
        set((state) => ({
          ui: { ...state.ui, breadcrumbs },
        })),
    }),
    {
      name: 'pvpoke-user-store',
      // Only persist user data, not UI state
      partialize: (state) => ({
        preferences: state.preferences,
        savedTeams: state.savedTeams,
        favoritesPokemon: state.favoritesPokemon,
        recentSearches: state.recentSearches,
      }),
    }
  )
);
