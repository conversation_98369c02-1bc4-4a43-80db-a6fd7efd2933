import type { NextApiRequest, NextApiResponse } from 'next';
import { pokemonService } from '@/services/pokemonService';
import type { ApiResponse } from '@/types/api';
import type { Pokemon } from '@/types/pokemon';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Pokemon>>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    const { speciesId } = req.query;

    if (!speciesId || typeof speciesId !== 'string') {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SPECIES_ID',
          message: 'Species ID is required and must be a string',
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    const pokemon = await pokemonService.getPokemonBySpecies(speciesId);

    if (!pokemon) {
      res.status(404).json({
        success: false,
        error: {
          code: 'POKEMON_NOT_FOUND',
          message: `Pokemon with species ID '${speciesId}' not found`,
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: pokemon,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
        cache: {
          cached: false,
        },
      },
    });
  } catch (error) {
    console.error(`Pokemon API error for species ${req.query.speciesId}:`, error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch Pokemon data',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
