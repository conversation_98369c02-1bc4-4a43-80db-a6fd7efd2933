{"name": "ufc-untapped-master", "title": "UFC Untapped (Master League)", "include": [], "exclude": [{"filterType": "id", "values": ["victini", "mew", "pidgeot_mega", "tyranitar_mega", "gengar_mega", "swampert_mega", "gardevoir_mega", "latios_mega", "latias_mega", "charizard_mega_y", "gyarados_mega", "groudon_primal", "kyogre_primal", "zygarde_complete", "rayquaza_mega", "garchomp_mega", "heracross_mega", "zygarde_10", "solgaleo", "aggron_mega", "dialga_origin", "palkia_origin", "x<PERSON><PERSON>", "steelix_mega", "grou<PERSON>", "charizard_mega_x", "meloetta_aria", "salamence_mega", "scizor_mega", "land<PERSON><PERSON>_therian", "lugia", "mewtwo_shadow", "giratina_altered", "palkia", "slowbro_mega", "mewtwo", "reshiram", "zekrom", "blaziken_mega", "kyurem", "dragonite", "ho_oh", "grou<PERSON>_shadow", "zacian_hero", "dialga", "alakazam_mega", "blastoise_mega", "heatran", "ho_oh_shadow", "giratina_origin", "lugia_shadow", "aerodactyl_mega", "zarude", "dragonite_shadow", "melmetal", "kyogre", "kyogre_shadow", "venusaur_mega", "yveltal", "zap<PERSON>_galarian", "enamorus_incarnate", "buzzwole", "keldeo_ordinary", "landorus_incarnate", "nihilego", "ampharos_mega", "regirock", "regirock_shadow", "garcho<PERSON>", "garcho<PERSON>_shadow", "lunala", "diancie_mega", "pinsir_mega", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "sceptile_mega", "celebi", "nec<PERSON><PERSON><PERSON>", "necrozma_dawn_wings", "necrozma_dusk_mane", "marshadow"]}]}