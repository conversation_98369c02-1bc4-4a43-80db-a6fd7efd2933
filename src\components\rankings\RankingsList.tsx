import { useState, useEffect } from 'react';
import { useRankingsStore } from '@/stores/rankingsStore';
import { RankingEntry, League, RankingCategory } from '@/types/rankings';
import { TypeIcon } from '@/components/common/TypeIcon';
import clsx from 'clsx';

interface RankingsListProps {
  league: League;
  category?: RankingCategory;
  className?: string;
}

export function RankingsList({
  league,
  category = 'overall',
  className = '',
}: RankingsListProps) {
  const {
    filteredRankings,
    loadingState,
    currentPage,
    itemsPerPage,
    totalItems,
    loadRankings,
    setCategory,
    setPage,
  } = useRankingsStore();

  const [selectedCategory, setSelectedCategory] = useState<RankingCategory>(category);

  // Load rankings when league or category changes
  useEffect(() => {
    loadRankings(league);
  }, [league, loadRankings]);

  useEffect(() => {
    setCategory(selectedCategory);
  }, [selectedCategory, setCategory]);

  // Handle category change
  const handleCategoryChange = (newCategory: RankingCategory) => {
    setSelectedCategory(newCategory);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPage(page);
  };

  // Get category display value
  const getCategoryValue = (entry: RankingEntry, cat: RankingCategory): number => {
    switch (cat) {
      case 'leads': return entry.lead;
      case 'closers': return entry.closer;
      case 'switches': return entry.switch;
      case 'chargers': return entry.charger;
      case 'attackers': return entry.attacker;
      case 'overall':
      default: return entry.score;
    }
  };

  // Get category color
  const getCategoryColor = (value: number): string => {
    if (value >= 90) return 'text-green-600';
    if (value >= 80) return 'text-yellow-600';
    if (value >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  if (loadingState.status === 'loading') {
    return (
      <div className={clsx('flex items-center justify-center py-12', className)}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pvpoke-primary"></div>
        <span className="ml-3 text-gray-600">Loading rankings...</span>
      </div>
    );
  }

  if (loadingState.status === 'error') {
    return (
      <div className={clsx('text-center py-12', className)}>
        <div className="text-red-600 mb-2">Failed to load rankings</div>
        <div className="text-sm text-gray-500">{loadingState.error}</div>
      </div>
    );
  }

  return (
    <div className={clsx('bg-white rounded-xl shadow-pokemon border border-gray-200', className)}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 capitalize">
            {league} League Rankings
          </h2>
          <div className="text-sm text-gray-500">
            {totalItems} Pokemon
          </div>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2">
          {(['overall', 'leads', 'closers', 'switches', 'chargers', 'attackers'] as RankingCategory[]).map((cat) => (
            <button
              key={cat}
              onClick={() => handleCategoryChange(cat)}
              className={clsx(
                'px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize',
                selectedCategory === cat
                  ? 'bg-pvpoke-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
            >
              {cat}
            </button>
          ))}
        </div>
      </div>

      {/* Rankings List */}
      <div className="divide-y divide-gray-200">
        {filteredRankings.map((entry) => (
          <div
            key={entry.pokemon.speciesId}
            className="p-4 hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-center space-x-4">
              {/* Rank */}
              <div className="flex-shrink-0 w-12 text-center">
                <div className={clsx(
                  'text-lg font-bold',
                  entry.rank <= 3 ? 'text-pvpoke-primary' : 'text-gray-600'
                )}>
                  #{entry.rank}
                </div>
              </div>

              {/* Pokemon Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        #{entry.pokemon.dex}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-gray-900 truncate">
                      {entry.pokemon.speciesName}
                    </div>
                    <div className="flex space-x-1 mt-1">
                      {entry.pokemon.types.map((type) => (
                        <TypeIcon key={type} type={type} size="sm" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="flex-shrink-0 grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-xs text-gray-500">Score</div>
                  <div className={clsx('font-bold', getCategoryColor(getCategoryValue(entry, selectedCategory)))}>
                    {getCategoryValue(entry, selectedCategory).toFixed(1)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500">Rating</div>
                  <div className="font-bold text-gray-900">
                    {entry.rating}
                  </div>
                </div>
              </div>

              {/* Win/Loss */}
              <div className="flex-shrink-0 text-right text-sm">
                <div className="text-xs text-gray-500">W/L</div>
                <div className="font-medium text-gray-900">
                  {entry.wins}/{entry.losses}
                </div>
              </div>

              {/* Detailed Stats (for larger screens) */}
              <div className="hidden lg:flex flex-shrink-0 space-x-4 text-xs">
                <div className="text-center">
                  <div className="text-gray-500">Lead</div>
                  <div className={clsx('font-medium', getCategoryColor(entry.lead))}>
                    {entry.lead.toFixed(1)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500">Switch</div>
                  <div className={clsx('font-medium', getCategoryColor(entry.switch))}>
                    {entry.switch.toFixed(1)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-gray-500">Closer</div>
                  <div className={clsx('font-medium', getCategoryColor(entry.closer))}>
                    {entry.closer.toFixed(1)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="p-4 border-t border-gray-200 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            
            {/* Page Numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = Math.max(1, currentPage - 2) + i;
              if (page > totalPages) return null;
              
              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={clsx(
                    'px-3 py-1 text-sm border rounded',
                    currentPage === page
                      ? 'bg-pvpoke-primary text-white border-pvpoke-primary'
                      : 'border-gray-300 hover:bg-gray-50'
                  )}
                >
                  {page}
                </button>
              );
            })}
            
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
