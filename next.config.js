/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Using pages router by default (no app directory)

  // Image optimization for Pokemon sprites
  images: {
    domains: ['raw.githubusercontent.com', 'assets.pokemon.com'],
    formats: ['image/webp', 'image/avif'],
  },

  // Performance optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
}

module.exports = nextConfig
