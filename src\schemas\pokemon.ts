import { z } from 'zod';

// Pokemon type enum
export const PokemonTypeSchema = z.enum([
  'normal', 'fire', 'water', 'electric', 'grass', 'ice',
  'fighting', 'poison', 'ground', 'flying', 'psychic', 'bug',
  'rock', 'ghost', 'dragon', 'dark', 'steel', 'fairy'
]);

// Base stats schema
export const BaseStatsSchema = z.object({
  attack: z.number().min(1).max(500),
  defense: z.number().min(1).max(500),
  stamina: z.number().min(1).max(500),
});

// IVs schema
export const IVsSchema = z.object({
  attack: z.number().min(0).max(15),
  defense: z.number().min(0).max(15),
  stamina: z.number().min(0).max(15),
});

// Move buffs schema
export const MoveBuffsSchema = z.object({
  target: z.enum(['self', 'opponent']),
  stat: z.enum(['attack', 'defense']),
  stages: z.number().min(-4).max(4),
  chance: z.number().min(0).max(1),
});

// Move schema
export const MoveSchema = z.object({
  moveId: z.string().min(1),
  name: z.string().min(1),
  type: PokemonTypeSchema,
  power: z.number().min(0),
  energy: z.number(),
  energyGain: z.number().optional(),
  cooldown: z.number().min(0),
  buffs: z.array(MoveBuffsSchema).optional(),
  archetype: z.string(),
});

// Pokemon schema
export const PokemonSchema = z.object({
  speciesId: z.string().min(1),
  speciesName: z.string().min(1),
  dex: z.number().min(1),
  types: z.array(PokemonTypeSchema).min(1).max(2),
  baseStats: BaseStatsSchema,
  fastMoves: z.array(MoveSchema),
  chargedMoves: z.array(MoveSchema),
  tags: z.array(z.string()),
  buddyDistance: z.number().min(1),
  thirdMoveCost: z.number().min(0),
  released: z.boolean(),
});

// Battle Pokemon schema
export const BattlePokemonSchema = PokemonSchema.extend({
  level: z.number().min(1).max(50),
  ivs: IVsSchema,
  selectedMoves: z.object({
    fastMove: MoveSchema,
    chargedMoves: z.array(MoveSchema).min(1).max(2),
  }),
  cp: z.number().min(10),
  hp: z.number().min(10),
});

// League schema
export const LeagueSchema = z.enum(['great', 'ultra', 'master', 'little', 'premier']);

// Cup schema
export const CupSchema = z.object({
  name: z.string().min(1),
  title: z.string().min(1),
  include: z.array(z.string()),
  exclude: z.array(z.string()),
  overrides: z.array(z.string()),
});

// Pokemon filters schema
export const PokemonFiltersSchema = z.object({
  types: z.array(PokemonTypeSchema).optional(),
  leagues: z.array(LeagueSchema).optional(),
  searchQuery: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// Game Master data schema
export const GameMasterDataSchema = z.object({
  pokemon: z.array(PokemonSchema),
  moves: z.array(MoveSchema),
  types: z.record(z.string(), z.record(z.string(), z.number())),
});

// Export types inferred from schemas
export type PokemonType = z.infer<typeof PokemonTypeSchema>;
export type BaseStats = z.infer<typeof BaseStatsSchema>;
export type IVs = z.infer<typeof IVsSchema>;
export type Move = z.infer<typeof MoveSchema>;
export type Pokemon = z.infer<typeof PokemonSchema>;
export type BattlePokemon = z.infer<typeof BattlePokemonSchema>;
export type League = z.infer<typeof LeagueSchema>;
export type Cup = z.infer<typeof CupSchema>;
export type PokemonFilters = z.infer<typeof PokemonFiltersSchema>;
export type GameMasterData = z.infer<typeof GameMasterDataSchema>;
