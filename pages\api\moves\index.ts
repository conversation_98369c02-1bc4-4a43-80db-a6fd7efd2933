import type { NextApiRequest, NextApiResponse } from 'next';
import { pokemonService } from '@/services/pokemonService';
import type { ApiResponse } from '@/types/api';
import type { Move, PokemonType } from '@/types/pokemon';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Move[]>>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    const {
      type,
      archetype,
      page = '1',
      limit = '50',
    } = req.query;

    let moves = await pokemonService.getAllMoves();

    // Apply filters
    if (type && typeof type === 'string') {
      moves = await pokemonService.getMovesByType(type as PokemonType);
    }

    if (archetype && typeof archetype === 'string') {
      moves = moves.filter(move => 
        move.archetype.toLowerCase().includes(archetype.toLowerCase())
      );
    }

    // Apply pagination
    const pageNum = parseInt(page as string) || 1;
    const limitNum = Math.min(parseInt(limit as string) || 50, 100);
    const startIndex = (pageNum - 1) * limitNum;
    const paginatedMoves = moves.slice(startIndex, startIndex + limitNum);

    res.status(200).json({
      success: true,
      data: paginatedMoves,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: moves.length,
          totalPages: Math.ceil(moves.length / limitNum),
          hasNext: startIndex + limitNum < moves.length,
          hasPrev: pageNum > 1,
        },
      },
    });
  } catch (error) {
    console.error('Moves API error:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch moves data',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
