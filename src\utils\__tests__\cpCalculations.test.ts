import { calculateCP, calculateHP, getCPMultiplier, findMaxLevel } from '../cpCalculations';
import { BaseStats, IVs } from '@/types/pokemon';

describe('CP Calculations', () => {
  const mockBaseStats: BaseStats = {
    attack: 223,
    defense: 173,
    stamina: 186,
  };

  const perfectIVs: IVs = {
    attack: 15,
    defense: 15,
    stamina: 15,
  };

  describe('calculateCP', () => {
    it('should calculate CP correctly for level 20 perfect IV Charizard', () => {
      const cp = calculateCP(mockBaseStats, perfectIVs, 20);
      expect(cp).toBeGreaterThan(0);
      expect(cp).toBeLessThan(5000); // Reasonable upper bound
    });

    it('should return minimum CP of 10 for very low stats', () => {
      const lowStats: BaseStats = { attack: 1, defense: 1, stamina: 1 };
      const lowIVs: IVs = { attack: 0, defense: 0, stamina: 0 };
      const cp = calculateCP(lowStats, lowIVs, 1);
      expect(cp).toBe(10);
    });

    it('should increase CP with higher level', () => {
      const cp20 = calculateCP(mockBaseStats, perfectIVs, 20);
      const cp30 = calculateCP(mockBaseStats, perfectIVs, 30);
      expect(cp30).toBeGreaterThan(cp20);
    });
  });

  describe('calculateHP', () => {
    it('should calculate HP correctly', () => {
      const hp = calculateHP(186, 15, 20);
      expect(hp).toBeGreaterThan(0);
      expect(hp).toBeLessThan(1000); // Reasonable upper bound
    });

    it('should return minimum HP of 10', () => {
      const hp = calculateHP(1, 0, 1);
      expect(hp).toBe(10);
    });
  });

  describe('getCPMultiplier', () => {
    it('should return correct multiplier for level 20', () => {
      const multiplier = getCPMultiplier(20);
      expect(multiplier).toBe(0.5974);
    });

    it('should return default multiplier for invalid level', () => {
      const multiplier = getCPMultiplier(999);
      expect(multiplier).toBe(0.094);
    });
  });

  describe('findMaxLevel', () => {
    it('should find correct max level for Great League (1500 CP)', () => {
      const maxLevel = findMaxLevel(mockBaseStats, perfectIVs, 1500);
      expect(maxLevel).toBeGreaterThan(0);
      expect(maxLevel).toBeLessThanOrEqual(50);
      
      // Verify the CP at this level is under the limit
      const cp = calculateCP(mockBaseStats, perfectIVs, maxLevel);
      expect(cp).toBeLessThanOrEqual(1500);
    });

    it('should return 1 if even level 1 exceeds CP limit', () => {
      const maxLevel = findMaxLevel(mockBaseStats, perfectIVs, 5);
      expect(maxLevel).toBe(1);
    });
  });
});
