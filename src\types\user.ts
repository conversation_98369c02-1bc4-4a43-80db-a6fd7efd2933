import type { League, PokemonType, BattlePokemon } from './pokemon';

export interface UserPreferences {
  defaultLeague: League;
  favoriteTypes: PokemonType[];
  theme: 'light' | 'dark' | 'auto';
  compactMode: boolean;
  showIVs: boolean;
  showCPs: boolean;
  animationsEnabled: boolean;
  soundEnabled: boolean;
}

export interface SavedTeam {
  id: string;
  name: string;
  league: League;
  pokemon: BattlePokemon[];
  notes?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
}

export interface UserProfile {
  id: string;
  username?: string;
  email?: string;
  preferences: UserPreferences;
  savedTeams: SavedTeam[];
  favoritesPokemon: string[]; // Pokemon species IDs
  recentSearches: string[];
  createdAt: string;
  lastLoginAt: string;
}

export interface UIState {
  sidebarOpen: boolean;
  modalOpen: string | null;
  loading: boolean;
  notifications: Notification[];
  breadcrumbs: Breadcrumb[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
  createdAt: string;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

export interface Breadcrumb {
  label: string;
  href?: string;
  current: boolean;
}

export interface SearchHistory {
  query: string;
  timestamp: string;
  results: number;
  filters?: Record<string, any>;
}
