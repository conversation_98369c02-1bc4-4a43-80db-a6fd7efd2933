import type { NextApiRequest, NextApiResponse } from 'next';
import { pokemonService } from '@/services/pokemonService';
import { BattleSimulationRequestSchema } from '@/schemas/battle';
import type { ApiResponse } from '@/types/api';
import type { BattleResult } from '@/types/battle';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<BattleResult>>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only POST requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    // Validate request body
    const validationResult = BattleSimulationRequestSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST_BODY',
          message: 'Invalid battle simulation request',
          details: process.env.NODE_ENV === 'development' ? {
            validationErrors: validationResult.error.errors,
          } : undefined,
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    const { pokemon1, pokemon2, options } = validationResult.data;

    // Get Pokemon data
    const pokemon1Data = await pokemonService.getPokemonBySpecies(pokemon1.speciesId);
    const pokemon2Data = await pokemonService.getPokemonBySpecies(pokemon2.speciesId);

    if (!pokemon1Data || !pokemon2Data) {
      res.status(404).json({
        success: false,
        error: {
          code: 'POKEMON_NOT_FOUND',
          message: 'One or both Pokemon not found',
          details: {
            pokemon1Found: !!pokemon1Data,
            pokemon2Found: !!pokemon2Data,
          },
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    // Get moves data
    const pokemon1FastMove = await pokemonService.getMoveById(pokemon1.fastMove);
    const pokemon1ChargedMovesRaw = await Promise.all(
      pokemon1.chargedMoves.map(moveId => pokemonService.getMoveById(moveId))
    );

    const pokemon2FastMove = await pokemonService.getMoveById(pokemon2.fastMove);
    const pokemon2ChargedMovesRaw = await Promise.all(
      pokemon2.chargedMoves.map(moveId => pokemonService.getMoveById(moveId))
    );

    // Filter out undefined moves and validate
    const pokemon1ChargedMoves = pokemon1ChargedMovesRaw.filter(move => move !== undefined);
    const pokemon2ChargedMoves = pokemon2ChargedMovesRaw.filter(move => move !== undefined);

    // Validate moves exist
    if (!pokemon1FastMove || !pokemon2FastMove ||
        pokemon1ChargedMoves.length === 0 ||
        pokemon2ChargedMoves.length === 0) {
      res.status(404).json({
        success: false,
        error: {
          code: 'MOVE_NOT_FOUND',
          message: 'One or more moves not found',
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    // TODO: Implement actual battle simulation logic
    // For now, create a mock battle result
    const mockResult: BattleResult = {
      winner: Math.random() > 0.5 ? 'pokemon1' : 'pokemon2',
      pokemon1: {
        pokemon: {
          ...pokemon1Data,
          level: pokemon1.level,
          ivs: pokemon1.ivs,
          selectedMoves: {
            fastMove: pokemon1FastMove!,
            chargedMoves: pokemon1ChargedMoves,
          },
          cp: 1500, // Mock CP calculation
          hp: 150,  // Mock HP calculation
        },
        remainingHp: Math.floor(Math.random() * 150),
        energyUsed: Math.floor(Math.random() * 100),
        damageDealt: Math.floor(Math.random() * 200),
        damageTaken: Math.floor(Math.random() * 150),
        movesUsed: {
          fast: Math.floor(Math.random() * 20),
          charged: Math.floor(Math.random() * 5),
        },
      },
      pokemon2: {
        pokemon: {
          ...pokemon2Data,
          level: pokemon2.level,
          ivs: pokemon2.ivs,
          selectedMoves: {
            fastMove: pokemon2FastMove!,
            chargedMoves: pokemon2ChargedMoves,
          },
          cp: 1500, // Mock CP calculation
          hp: 150,  // Mock HP calculation
        },
        remainingHp: Math.floor(Math.random() * 150),
        energyUsed: Math.floor(Math.random() * 100),
        damageDealt: Math.floor(Math.random() * 200),
        damageTaken: Math.floor(Math.random() * 150),
        movesUsed: {
          fast: Math.floor(Math.random() * 20),
          charged: Math.floor(Math.random() * 5),
        },
      },
      timeline: [], // Mock timeline
      duration: Math.floor(Math.random() * 180),
      turns: Math.floor(Math.random() * 50),
    };

    res.status(200).json({
      success: true,
      data: mockResult,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
      },
    });

  } catch (error) {
    console.error('Battle simulation API error:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to simulate battle',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
