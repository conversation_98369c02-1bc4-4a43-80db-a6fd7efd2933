import { League } from '@/types/pokemon';
import { BattleOptions as BattleOptionsType } from '@/types/battle';
import { useBattleStore } from '@/stores/battleStore';
import clsx from 'clsx';

const LEAGUES: { value: League; label: string; cp: number }[] = [
  { value: 'great', label: 'Great League', cp: 1500 },
  { value: 'ultra', label: 'Ultra League', cp: 2500 },
  { value: 'master', label: 'Master League', cp: 10000 },
];

export function BattleOptions() {
  const { league, battleMode, battleOptions, setLeague, setBattleOptions } = useBattleStore();

  const handleShieldChange = (pokemon: 'pokemon1' | 'pokemon2', shields: number) => {
    setBattleOptions({
      shields: {
        ...battleOptions.shields,
        [pokemon]: shields,
      },
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Battle Options</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* League Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            League
          </label>
          <div className="space-y-2">
            {LEAGUES.map((leagueOption) => (
              <label key={leagueOption.value} className="flex items-center">
                <input
                  type="radio"
                  name="league"
                  value={leagueOption.value}
                  checked={league === leagueOption.value}
                  onChange={(e) => setLeague(e.target.value as League)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">
                  {leagueOption.label}
                  <span className="text-gray-500 ml-1">({leagueOption.cp} CP)</span>
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Pokemon 1 Shields - Only show in single battle mode */}
        {battleMode === 'single' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pokemon 1 Shields
            </label>
            <div className="space-y-2">
              {[0, 1, 2].map((shields) => (
                <label key={shields} className="flex items-center">
                  <input
                    type="radio"
                    name="shields1"
                    value={shields}
                    checked={battleOptions.shields.pokemon1 === shields}
                    onChange={() => handleShieldChange('pokemon1', shields)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {shields} shield{shields !== 1 ? 's' : ''}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Pokemon 2 Shields - Only show in single battle mode */}
        {battleMode === 'single' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pokemon 2 Shields
            </label>
            <div className="space-y-2">
              {[0, 1, 2].map((shields) => (
                <label key={shields} className="flex items-center">
                  <input
                    type="radio"
                    name="shields2"
                    value={shields}
                    checked={battleOptions.shields.pokemon2 === shields}
                    onChange={() => handleShieldChange('pokemon2', shields)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {shields} shield{shields !== 1 ? 's' : ''}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Multi-battle options - Only show in multi battle mode */}
        {battleMode === 'multi' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shield Scenario
            </label>
            <select
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="0-0">0 vs 0 shields</option>
              <option value="1-1" selected>1 vs 1 shields</option>
              <option value="2-2">2 vs 2 shields</option>
              <option value="0-1">0 vs 1 shields</option>
              <option value="0-2">0 vs 2 shields</option>
              <option value="1-0">1 vs 0 shields</option>
              <option value="1-2">1 vs 2 shields</option>
              <option value="2-0">2 vs 0 shields</option>
              <option value="2-1">2 vs 1 shields</option>
            </select>
          </div>
        )}

        {/* Matrix battle options - Only show in matrix battle mode */}
        {battleMode === 'matrix' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shield Scenario
            </label>
            <select
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="0-0">0 vs 0 shields</option>
              <option value="1-1" selected>1 vs 1 shields</option>
              <option value="2-2">2 vs 2 shields</option>
              <option value="0-1">0 vs 1 shields</option>
              <option value="0-2">0 vs 2 shields</option>
              <option value="1-0">1 vs 0 shields</option>
              <option value="1-2">1 vs 2 shields</option>
              <option value="2-0">2 vs 0 shields</option>
              <option value="2-1">2 vs 1 shields</option>
            </select>
          </div>
        )}
      </div>
    </div>
  );
}
