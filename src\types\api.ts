// API Response types

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: ApiMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface ApiMeta {
  timestamp: string;
  version: string;
  requestId: string;
  pagination?: PaginationMeta;
  cache?: CacheMeta;
  stats?: {
    pokemonCount: number;
    moveCount: number;
    shadowPokemonCount: number;
    cupCount: number;
    timestamp: string;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface CacheMeta {
  cached: boolean;
  ttl?: number;
  lastModified?: string;
}

// API Request types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  [key: string]: string | number | boolean | string[] | undefined;
}

export interface SearchParams extends PaginationParams, SortParams, FilterParams {
  q?: string;
}

// Specific API endpoint types
export interface PokemonSearchParams extends SearchParams {
  types?: string[];
  leagues?: string[];
  tags?: string[];
  released?: boolean;
  minCp?: number;
  maxCp?: number;
}

export interface BattleSimulationRequest {
  pokemon1: {
    speciesId: string;
    level: number;
    ivs: { attack: number; defense: number; stamina: number };
    fastMove: string;
    chargedMoves: string[];
  };
  pokemon2: {
    speciesId: string;
    level: number;
    ivs: { attack: number; defense: number; stamina: number };
    fastMove: string;
    chargedMoves: string[];
  };
  options: {
    shields: { pokemon1: number; pokemon2: number };
    strategy: 'neutral' | 'aggressive' | 'defensive';
    allowSwitching: boolean;
  };
}

export interface RankingsRequest extends SearchParams {
  league: string;
  cup?: string;
  category?: string;
  types?: string[];
  tags?: string[];
  minRating?: number;
  maxRating?: number;
}
