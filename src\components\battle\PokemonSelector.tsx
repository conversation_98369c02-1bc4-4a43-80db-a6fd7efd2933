import { useState, useEffect } from 'react';
import { BattlePokemon, Pokemon, League } from '@/types/pokemon';
import { calculateCP, calculateHP, IVs } from '@/utils/cpCalculations';
import { getLeagueCP } from '@/utils/leagues';
import clsx from 'clsx';

interface PokemonSelectorProps {
  pokemon: BattlePokemon | null;
  onPokemonChange: (pokemon: BattlePokemon | null) => void;
  league: League;
  side: 'left' | 'right';
}

export function PokemonSelector({ pokemon, onPokemonChange, league, side }: PokemonSelectorProps) {
  const [selectedPokemon, setSelectedPokemon] = useState<Pokemon | null>(null);
  const [level, setLevel] = useState(40);
  const [ivs, setIVs] = useState<IVs>({ attack: 15, defense: 15, stamina: 15 });
  const [fastMove, setFastMove] = useState<string>('');
  const [chargedMove1, setChargedMove1] = useState<string>('');
  const [chargedMove2, setChargedMove2] = useState<string>('');
  const [shields, setShields] = useState(1);
  const [shadowForm, setShadowForm] = useState('normal');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  const leagueCP = getLeagueCP(league);

  // Calculate current CP and HP
  const currentCP = selectedPokemon ? calculateCP(selectedPokemon.baseStats, ivs, level) : 0;
  const currentHP = selectedPokemon ? calculateHP(selectedPokemon.baseStats.stamina, ivs.stamina, level) : 0;

  // Check if CP exceeds league limit
  const exceedsCP = currentCP > leagueCP;

  // Update battle pokemon when any parameter changes
  useEffect(() => {
    if (selectedPokemon && fastMove && chargedMove1) {
      const fastMoveObj = selectedPokemon.fastMoves.find(m => m.moveId === fastMove);
      const chargedMove1Obj = selectedPokemon.chargedMoves.find(m => m.moveId === chargedMove1);
      const chargedMove2Obj = chargedMove2 ? selectedPokemon.chargedMoves.find(m => m.moveId === chargedMove2) : undefined;

      if (fastMoveObj && chargedMove1Obj) {
        const battlePokemon: BattlePokemon = {
          ...selectedPokemon,
          level,
          ivs,
          selectedMoves: {
            fastMove: fastMoveObj,
            chargedMoves: [chargedMove1Obj, chargedMove2Obj].filter(Boolean) as any[],
          },
          cp: currentCP,
          hp: currentHP,
        };
        onPokemonChange(battlePokemon);
      } else {
        onPokemonChange(null);
      }
    } else {
      onPokemonChange(null);
    }
  }, [selectedPokemon, level, ivs, fastMove, chargedMove1, chargedMove2, currentCP, currentHP, onPokemonChange]);

  const handlePokemonSelect = (pokemon: Pokemon) => {
    setSelectedPokemon(pokemon);
    // Set default moves
    if (pokemon.fastMoves.length > 0) {
      setFastMove(pokemon.fastMoves[0].moveId);
    }
    if (pokemon.chargedMoves.length > 0) {
      setChargedMove1(pokemon.chargedMoves[0].moveId);
    }
    if (pokemon.chargedMoves.length > 1) {
      setChargedMove2(pokemon.chargedMoves[1].moveId);
    }
  };

  return (
    <div className="poke single">
      <a className="random" href="#">Random</a>
      <a className="swap" href="#">Swap</a>

      <div className="poke-search-container flex">
        <input className="poke-search" type="text" placeholder="Search name" />
        <a href="#" className="search-info" title="Search Help">?</a>
      </div>

      <select className="poke-select">
        <option disabled selected value="">Select a Pokemon</option>
        {/* Pokemon options will be populated here */}
      </select>

      <div className="form-select-container">
        <div className="form-select-border"></div>
        <select className="form-select">
          <option value="" selected disabled>Select another form</option>
        </select>
        <a className="form-link" href="#"></a>
      </div>

      {/* Pokemon Box - placeholder for now */}
      <div className="pokebox">
        {selectedPokemon && (
          <div className="pokemon-display">
            <img
              src={`/images/pokemon/${selectedPokemon.speciesId}.png`}
              alt={selectedPokemon.speciesName}
              onError={(e) => {
                e.currentTarget.src = '/images/pokemon/0.png';
              }}
            />
          </div>
        )}
      </div>

      <div className="poke-stats">
        <h3 className="cp">
          <span className="identifier" title="Shadow"></span> cp <span className="stat">{currentCP}</span>
        </h3>
        <div className="types">
          {selectedPokemon?.types.map((type) => (
            <div key={type} className={`type ${type}`}>{type}</div>
          ))}
        </div>

        <div className="stat-container attack clear">
          <div className="stat-label">
            <span className="label-name">attack</span>
            <span className="stat">{selectedPokemon?.baseStats.attack || 100}</span>
          </div>
          <div className="bar-back">
            <div className="bar"></div>
          </div>
        </div>

        <div className="stat-container defense clear">
          <div className="stat-label">
            <span className="label-name">defense</span>
            <span className="stat">{selectedPokemon?.baseStats.defense || 100}</span>
          </div>
          <div className="bar-back">
            <div className="bar"></div>
          </div>
        </div>

        <div className="stat-container stamina clear">
          <div className="stat-label">
            <span className="label-name">stamina</span>
            <span className="stat">{selectedPokemon?.baseStats.stamina || 100}</span>
          </div>
          <div className="bar-back">
            <div className="bar"></div>
          </div>
        </div>

        <div className="clear"></div>
        <div className="stat-container overall clear">
          <div className="stat-label">
            <span className="label-name">overall</span>
            <span className="stat">100</span>
          </div>
        </div>
        <div className="clear"></div>

        <div className="mega-cp-container">
          <h3 className="base-name section-title">{selectedPokemon?.speciesName || 'Pokemon'}</h3>
          <h3 className="mega-cp">cp <span className="stat">{currentCP}</span></h3>
        </div>

        <div className="advanced-section">
          <a
            className="advanced section-title"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setShowAdvanced(!showAdvanced);
            }}
          >
            Edit/Check IVs <span className="arrow-down">&#9660;</span><span className="arrow-up">&#9650;</span>
          </a>
          <div className={clsx("fields", !showAdvanced && "hide")}>
            <div className="ivs">
              <div className="flex">
                <input
                  className="level"
                  type="number"
                  placeholder="Level"
                  min="1"
                  max="40"
                  step=".5"
                  value={level}
                  onChange={(e) => setLevel(parseFloat(e.target.value) || 1)}
                />
                <div className="ivs-group">
                  <input
                    className="iv"
                    type="number"
                    placeholder="Atk"
                    min="0"
                    max="15"
                    step="1"
                    value={ivs.attack}
                    onChange={(e) => setIVs(prev => ({ ...prev, attack: parseInt(e.target.value) || 0 }))}
                  />
                  <span>/</span>
                  <input
                    className="iv"
                    type="number"
                    placeholder="Def"
                    min="0"
                    max="15"
                    step="1"
                    value={ivs.defense}
                    onChange={(e) => setIVs(prev => ({ ...prev, defense: parseInt(e.target.value) || 0 }))}
                  />
                  <span>/</span>
                  <input
                    className="iv"
                    type="number"
                    placeholder="HP"
                    min="0"
                    max="15"
                    step="1"
                    value={ivs.stamina}
                    onChange={(e) => setIVs(prev => ({ ...prev, stamina: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>

              <div className="flex">
                <label className="level">Level</label>
                <div className="ivs-group labels">
                  <label className="iv">Atk</label><span>&nbsp;</span>
                  <label className="iv">Def</label><span>&nbsp;</span>
                  <label className="iv">HP</label>
                </div>
              </div>
              <div className="iv-rank">Rank&nbsp;<span className="value"></span><span className="count"></span><span className="info-icon">i</span></div>
            </div>

            <div className="maximize-section">
              <div className="check-group">
                <div className="check on" value="overall"><span></span>Overall</div>
                <div className="check" value="atk"><span></span>Atk</div>
                <div className="check" value="def"><span></span>Def</div>
              </div>
              <div className="level-cap-group">
                <div>Level Cap:</div>
                <div className="check on" value="40"><span></span>40</div>
                <div className="check" value="41"><span></span>41</div>
                <div className="check" value="50"><span></span>50</div>
                <div className="check" value="51"><span></span>51</div>
              </div>
              <div className="check auto-level on"><span></span>Auto level</div>
              <button className="maximize-stats">Maximize</button>
              <button className="restore-default">Default</button>
            </div>
          </div>
        </div>

        <div className="move-select-container">
          <h3 className="section-title">Fast Move</h3>
          <select
            className="move-select fast"
            value={fastMove}
            onChange={(e) => setFastMove(e.target.value)}
          >
            <option value="">Select Fast Move</option>
            {selectedPokemon?.fastMoves.map((move) => (
              <option key={move.moveId} value={move.moveId}>
                {move.name} ({move.type})
              </option>
            ))}
          </select>

          <h3 className="section-title">Charged Moves</h3>
          <select
            className="move-select charged"
            value={chargedMove1}
            onChange={(e) => setChargedMove1(e.target.value)}
          >
            <option value="">Select Charged Move</option>
            {selectedPokemon?.chargedMoves.map((move) => (
              <option key={move.moveId} value={move.moveId}>
                {move.name} ({move.type}) - {move.energy} energy
              </option>
            ))}
          </select>

          <select
            className="move-select charged"
            value={chargedMove2}
            onChange={(e) => setChargedMove2(e.target.value)}
          >
            <option value="">Select Charged Move</option>
            {selectedPokemon?.chargedMoves.map((move) => (
              <option key={move.moveId} value={move.moveId}>
                {move.name} ({move.type}) - {move.energy} energy
              </option>
            ))}
          </select>

          <button className="auto-select">Select Recommended Moves</button>
          <div className="legacy">* Exclusive move<br /><sup>†</sup> Unobtainable move</div>
        </div>

        <div className="options">
          <div className="shield-section">
            <h3 className="section-title">Shields</h3>
            <div className="form-group shield-picker">
              <div
                className={clsx("option", shields === 0 && "on")}
                onClick={() => setShields(0)}
              >
                <div className="icon"></div> 0
              </div>
              <div
                className={clsx("option", shields === 1 && "on")}
                onClick={() => setShields(1)}
              >
                <div className="icon"></div> 1
              </div>
              <div
                className={clsx("option", shields === 2 && "on")}
                onClick={() => setShields(2)}
              >
                <div className="icon"></div> 2
              </div>
            </div>
          </div>

          <div className="shadow-section">
            <h3 className="section-title">Shadow Form</h3>
            <div className="form-group shadow-picker">
              <div
                className={clsx("option", shadowForm === 'normal' && "on")}
                onClick={() => setShadowForm('normal')}
              >
                Normal
              </div>
              <div
                className={clsx("option", shadowForm === 'shadow' && "on")}
                onClick={() => setShadowForm('shadow')}
              >
                Shadow
              </div>
            </div>
          </div>

          <a
            href="#"
            className="section-title toggle"
            onClick={(e) => {
              e.preventDefault();
              setShowOptions(!showOptions);
            }}
          >
            Options <span className="arrow-down">&#9660;</span><span className="arrow-up">&#9650;</span>
          </a>

          <div className={clsx("toggle-content", !showOptions && "hide")}>
            <div className="flex">
              <div className="label">HP:</div>
              <input className="start-hp" type="number" min="0" placeholder="Starting HP" />
            </div>
            <div className="flex">
              <div className="label">Energy:</div>
              <input className="start-energy" type="number" min="0" max="100" placeholder="Starting Energy" />
            </div>
            <button className="add-fast-move">+ Move</button>
            <button className="pull-from-timeline">Pull from timeline</button>

            <h3 className="section-title">Baiting</h3>
            <div className="form-group bait-picker">
              <div className="option" value="0">Off</div>
              <div className="option on" value="1">Selective</div>
              <div className="option" value="2">On</div>
            </div>

            <div className="stat-modifiers">
              <h3 className="section-title">Stat Modifiers (-4 to 4)</h3>
              <input className="stat-mod" type="number" placeholder="Atk" min="-4" max="4" step="1" />
              <input className="stat-mod" type="number" placeholder="Def" min="-4" max="4" step="1" />
            </div>

            <div className="damage-adjustments">
              <div className="adjustment attack">
                <div className="value">x1</div>
                <div className="label">damage dealt</div>
              </div>
              <div className="adjustment defense">
                <div className="value">x1</div>
                <div className="label">damage taken</div>
              </div>
            </div>

            <div className="check optimize-timing on"><span></span>Optimize move timing</div>
            <div className="check switch-delay"><span></span>1 turn switch</div>
          </div>
        </div>

        <div className="custom-ranking-options">
          <h3 className="section-title">Ranking Weight Multiplier</h3>
          <input className="ranking-weight" type="number" placeholder="Weight" min="0" defaultValue="1" />
          <div className="legacy">Matchup scores against this Pokemon will be multiplied by the value above. Default is 1. Use values of 2-10 depending on meta relevancy. Use 0 to remove all weighting.</div>
        </div>

        <a href="#" className="clear-selection">Clear Selection</a>

        <div className="hp bar-back">
          <div className="bar"></div>
          <div className="bar damage"></div>
          <div className="stat">{currentHP}</div>
        </div>

        <div className="move-bars">
          <div className="move-bar">
            <div className="label">CM</div>
            <div className="bar"></div>
            <div className="bar"></div>
            <div className="bar"></div>
            <div className="bar-back"></div>
          </div>
          <div className="energy-label">
            <div className="num">0</div>
            <div>energy</div>
          </div>
          <div className="move-bar">
            <div className="label">CM</div>
            <div className="bar"></div>
            <div className="bar"></div>
            <div className="bar"></div>
            <div className="bar-back"></div>
          </div>
        </div>
      </div>

      <div className="tooltip">
        <h3 className="name"></h3>
        <div className="details"></div>
      </div>

      <div className="clear-confirm hide">
        <p>Clear <b><span className="name"></span></b> from the selection?</p>
        <div className="center flex">
          <div className="button yes">Yes</div>
          <div className="button no">No</div>
        </div>
      </div>
    </div>
  );
}
