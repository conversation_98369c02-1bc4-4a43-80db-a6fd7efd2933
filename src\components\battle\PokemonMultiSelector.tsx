import { useState } from 'react';
import { BattlePokemon, Pokemon } from '@/types/pokemon';
import { PokemonSearch } from '@/components/common/PokemonSearch';
import clsx from 'clsx';

interface PokemonMultiSelectorProps {
  selectedPokemon: BattlePokemon[];
  onPokemonAdd: (pokemon: BattlePokemon) => void;
  onPokemonRemove: (index: number) => void;
  onPokemonClear: () => void;
  title: string;
  maxSelections?: number;
}

export function PokemonMultiSelector({
  selectedPokemon,
  onPokemonAdd,
  onPokemonRemove,
  onPokemonClear,
  title,
  maxSelections = 100
}: PokemonMultiSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');

  const handlePokemonSelect = (pokemon: Pokemon) => {
    if (selectedPokemon.length >= maxSelections) {
      return;
    }

    // Create a battle Pokemon with default values
    const battlePokemon: BattlePokemon = {
      ...pokemon,
      level: 40,
      ivs: { attack: 15, defense: 15, stamina: 15 },
      selectedMoves: {
        fastMove: pokemon.fastMoves[0],
        chargedMoves: pokemon.chargedMoves.slice(0, 2),
      },
      cp: 1500, // This is a placeholder, would need to calculate actual CP
      hp: 100, // This is a placeholder, would need to calculate actual HP
    };

    onPokemonAdd(battlePokemon);
    setSearchQuery('');
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        <div className="flex space-x-2">
          <span className="text-sm text-gray-500">
            {selectedPokemon.length}/{maxSelections}
          </span>
          {selectedPokemon.length > 0 && (
            <button
              onClick={onPokemonClear}
              className="text-sm text-red-600 hover:text-red-800 transition-colors"
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Pokemon Search */}
      <div className="mb-4">
        <PokemonSearch
          onSelect={handlePokemonSelect}
          placeholder="Search for a Pokemon..."
          value={searchQuery}
          onChange={setSearchQuery}
          disabled={selectedPokemon.length >= maxSelections}
        />
        {selectedPokemon.length >= maxSelections && (
          <p className="text-xs text-red-600 mt-1">
            Maximum number of Pokemon selected ({maxSelections})
          </p>
        )}
      </div>

      {/* Selected Pokemon List */}
      <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Pokemon</h3>
        
        {selectedPokemon.length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-4 text-center text-gray-500">
            No Pokemon selected
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
            {selectedPokemon.map((pokemon, index) => (
              <div
                key={`${pokemon.speciesId}-${index}`}
                className="bg-gray-50 rounded-lg p-2 flex flex-col items-center relative group"
              >
                <button
                  onClick={() => onPokemonRemove(index)}
                  className="absolute top-1 right-1 w-5 h-5 rounded-full bg-red-100 text-red-600 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label="Remove Pokemon"
                >
                  &times;
                </button>
                
                <img
                  src={`/images/pokemon/${pokemon.speciesId}.png`}
                  alt={pokemon.speciesName}
                  className="w-12 h-12"
                  onError={(e) => {
                    e.currentTarget.src = '/images/pokemon/0.png';
                  }}
                />
                
                <div className="text-xs text-center mt-1 truncate w-full">
                  {pokemon.speciesName}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}