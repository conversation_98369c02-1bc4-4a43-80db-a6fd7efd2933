{"name": "pillar", "title": "Devon Pillar Cup", "include": [{"filterType": "id", "name": "Species", "values": ["bibarel", "bibarel_shadow", "clodsire", "feraligatr", "feraligatr_shadow", "a<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "malamar", "<PERSON><PERSON><PERSON>", "jumpluff_shadow", "drapion", "drapion_shadow", "lickilicky", "samu<PERSON>t", "walrein", "walrein_shadow", "arctibax", "goodra", "hippo<PERSON><PERSON>", "hippow<PERSON>_shadow", "miltank", "ursaring", "ursaring_shadow", "sableye", "sableye_shadow", "weezing_galarian", "weezing_gal<PERSON>_shadow", "cresselia", "cress<PERSON>a_shadow", "regirock", "regirock_shadow", "stunfisk", "gligar", "gligar_shadow", "sealeo", "sealeo_shadow", "castform_rainy", "froslass", "froslass_shadow", "lokix", "greedent", "trevenant", "leavanny", "nidoqueen", "nidoqueen_shadow", "golisopod", "spiritomb", "raticate_alolan", "raticate_alolan_shadow", "cradily", "cradily_shadow", "empoleon", "empoleon_shadow", "altaria", "jellicent", "politoed", "politoed_shadow", "furfrou", "beedrill", "beedrill_shadow", "s<PERSON><PERSON>", "sci<PERSON>_shadow", "seaking", "pelipper", "dragapult", "typhlosion", "typhlosion_shadow", "tapu_fini", "rapidash", "furret", "pidgeot", "pidgeot_shadow", "gyarados", "gyarado<PERSON>_shadow", "<PERSON>ras", "lap<PERSON>_shadow", "noctowl", "zangoose", "forretress", "forretress_shadow", "<PERSON><PERSON><PERSON>", "sneasler", "snea<PERSON>_shadow", "snea<PERSON>_his<PERSON>an", "snea<PERSON>_<PERSON><PERSON>an_shadow", "qwilfish", "qwilfish_his<PERSON>an", "lunatone", "solrock", "victini", "poliwhirl", "poliwhirl_shadow", "magmar", "magmar_shadow", "magmortar", "magmortar_shadow", "sceptile", "sceptile_shadow", "marowak_alolan", "marowak_alolan_shadow", "run<PERSON><PERSON>", "gliscor", "gliscor_shadow", "regice", "regice_shadow", "nidoking", "nidoking_shadow", "flygon", "flygon_shadow", "sandslash", "sandslash_shadow", "rapidash_galarian", "swalot", "aurorus", "bewear", "starmie", "diggersby"]}], "exclude": [], "overrides": [], "levelCap": 50, "includeLowStatProduct": true}