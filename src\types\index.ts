// Re-export all types for easier imports

// Pokemon types
export type {
  PokemonType,
  BaseStats,
  IVs,
  MoveBuffs,
  Move,
  Pokemon,
  BattlePokemon,
  League,
  Cup,
  PokemonFilters,
} from './pokemon';

// Battle types
export type {
  DamageResult,
  BattleEvent,
  BattlePokemonResult,
  BattleResult,
  BattleOptions,
  TypeEffectiveness,
} from './battle';

// Rankings types
export type {
  RankingEntry,
  RankingThreat,
  RankingCounter,
  RankingFilters,
  RankingCategory,
  RankingMetadata,
  RankingResponse,
} from './rankings';

// User types
export type {
  UserPreferences,
  SavedTeam,
  UserProfile,
  UIState,
  Notification,
  NotificationAction,
  Breadcrumb,
  SearchHistory,
} from './user';

// API types
export type {
  ApiResponse,
  ApiError,
  ApiMeta,
  PaginationMeta,
  CacheMeta,
  PaginationParams,
  SortParams,
  FilterParams,
  SearchParams,
  PokemonSearchParams,
  BattleSimulationRequest,
  RankingsRequest,
} from './api';

// Utility types
export type {
  DeepPartial,
  RequireFields,
  OptionalFields,
  ArrayElement,
  ValueOf,
  NonNullable,
  AsyncReturnType,
  Parameters,
  Brand,
  PokemonId,
  MoveId,
  UserId,
  TeamId,
  Result,
  Option,
  LoadingState,
  Paginated,
  SearchResult,
  FieldState,
  FormState,
  ApiEndpoint,
  EventHandler,
  PropsWithChildren,
  PropsWithClassName,
  PropsWithHtmlAttributes,
  StoreActions,
  StoreState,
  ComponentVariant,
  ThemeColor,
  ResponsiveValue,
} from './utils';
