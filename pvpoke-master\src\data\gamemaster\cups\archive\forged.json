{"name": "forged", "title": "<PERSON><PERSON><PERSON>", "include": [{"filterType": "id", "includeShadows": true, "values": ["nidoqueen", "cresselia", "wobbuffet", "lickitung", "diggersby", "ninetales_alolan", "abomasnow", "talonflame", "trevenant", "cofagrigus", "<PERSON><PERSON><PERSON><PERSON>", "jellicent", "froslass", "chansey", "araquanid", "tapu_fini", "nidorina", "golbat", "beedrill", "hypno", "mew", "pidgeot", "noctowl", "lickilicky", "snorlax", "munchlax", "greedent", "gran<PERSON>", "wigglytuff", "clefable", "sylveon", "dewgong", "<PERSON>ras", "marowak_alolan", "ninetales", "stunfisk", "<PERSON><PERSON><PERSON>", "p<PERSON><PERSON><PERSON>", "lanturn", "mantine", "politoed", "venusaur", "meganium", "serperior", "<PERSON><PERSON><PERSON>", "muk", "grimer", "<PERSON><PERSON><PERSON>", "scolipede", "venomoth", "crobat", "tentacruel", "garbodor", "amoon<PERSON>s", "nidoking", "arbok", "weezing", "mewtwo", "rapidash_galarian", "slowpoke", "slowpoke_galarian", "slowbro", "slowking", "slowbro_galarian", "slowking_galarian", "celebi", "uxie", "espeon", "exeggcute", "exeggutor", "mr_rime", "<PERSON><PERSON><PERSON>", "girafarig", "me<PERSON><PERSON>", "meowstic_female", "swoobat", "<PERSON><PERSON><PERSON>", "bouffalant", "bibarel", "litleo", "pyroar", "furfrou", "porygon2", "porygon_z", "purugly", "<PERSON><PERSON><PERSON><PERSON>", "farfetchd", "bewear", "florges", "floette", "togetic", "hitmonchan", "hitmontop", "heracross", "machoke", "gur<PERSON><PERSON>", "farfetchd_galarian", "avalugg", "piloswine", "ma<PERSON><PERSON>", "cryogonal", "glaceon", "beartic", "cloyster", "darmanitan_galarian_standard", "charizard", "fletchinder", "magmortar", "rapidash", "heatmor", "typhlosion", "arcanine", "darminitan_standard", "chandelure", "delphox", "emboar", "infernape", "flareon", "barbara<PERSON>", "dwebble", "carracosta", "omastar", "kabutops", "aerodactyl", "sudowoodo", "bonsly", "rhyperior", "rhydon", "gigalith", "lycanroc_midday", "gengar", "haunter", "decid<PERSON><PERSON>", "golurk", "dusknoir", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "rai<PERSON>u", "electivire", "electrode", "electrode_hisuian", "dedenne", "em<PERSON>ga", "ampha<PERSON>", "zebstrika", "eelektross", "jolteon", "heliolisk", "samu<PERSON>t", "poliwrath", "gyarados", "blastoise", "seaking", "gastrodon", "seismitoad", "vaporeon", "poliwhirl", "golduck", "feraligatr", "kingler", "lumineon", "prin<PERSON><PERSON><PERSON>", "swanna", "chesnaught", "quilladin", "<PERSON><PERSON>e", "leafeon", "tangrowth", "leavanny", "cherrim_sunny", "wormadam_plant", "wormadam_sandy", "sandslash", "<PERSON><PERSON><PERSON>", "marowak", "hippo<PERSON><PERSON>", "vespiquen", "pinsir", "<PERSON><PERSON>", "kricketune", "parasect", "scyther", "yanmega", "qwilfish", "roserade", "a<PERSON><PERSON>", "salazzle", "oranguru", "victini", "lugia", "gallade", "whimsicott", "aromatisse", "slurpuff", "spritzee", "<PERSON><PERSON><PERSON><PERSON>", "primarina", "machamp", "primeape", "sir<PERSON><PERSON><PERSON>", "toxicroak", "articuno", "crustle", "graveler_alolan", "golem_alolan", "geodude_alolan", "magcargo", "lycanroc", "lycanroc_midnight", "gourgeist_super", "gourgeist_large", "gourgeist_average", "gourgeist_small", "drifb<PERSON>", "drifloon", "zapdos", "luxray", "alomomola", "quagsire", "lura<PERSON>s", "bellossom", "victreebel", "weepinbell", "gloom", "vileplume", "oddish", "grotle", "torterra", "bayleef", "ivysaur", "gligar", "gliscor"]}], "exclude": [], "tierRules": {"max": 17, "floor": 1, "tiers": [{"points": 8, "pokemon": ["nidoqueen", "cresselia", "wobbuffet", "lickitung", "diggersby", "ninetales_alolan", "abomasnow", "talonflame", "trevenant", "cofagrigus", "<PERSON><PERSON><PERSON><PERSON>", "jellicent", "froslass", "chansey", "araquanid", "tapu_fini"]}, {"points": 4, "pokemon": ["nidorina", "golbat", "beedrill", "hypno", "mew", "pidgeot", "noctowl", "lickilicky", "snorlax", "munchlax", "greedent", "gran<PERSON>", "wigglytuff", "clefable", "sylveon", "dewgong", "<PERSON>ras", "marowak_alolan", "ninetales", "stunfisk", "<PERSON><PERSON><PERSON>", "p<PERSON><PERSON><PERSON>", "lanturn", "mantine", "politoed", "venusaur", "meganium", "serperior", "<PERSON><PERSON><PERSON>"]}, {"points": 2, "pokemon": ["qwilfish", "roserade", "a<PERSON><PERSON>", "salazzle", "oranguru", "victini", "lugia", "gallade", "whimsicott", "aromatisse", "slurpuff", "spritzee", "<PERSON><PERSON><PERSON><PERSON>", "primarina", "machamp", "primeape", "sir<PERSON><PERSON><PERSON>", "toxicroak", "articuno", "crustle", "graveler_alolan", "golem_alolan", "geodude_alolan", "magcargo", "lycanroc", "lycanroc_midnight", "gourgeist_super", "gourgeist_large", "gourgeist_average", "gourgeist_small", "drifb<PERSON>", "drifloon", "zapdos", "luxray", "alomomola", "quagsire", "lura<PERSON>s", "bellossom", "victreebel", "weepinbell", "gloom", "vileplume", "oddish", "grotle", "torterra", "bayleef", "ivysaur", "gligar", "gliscor"]}, {"points": 1, "pokemon": ["muk", "grimer", "<PERSON><PERSON><PERSON>", "scolipede", "venomoth", "crobat", "tentacruel", "garbodor", "amoon<PERSON>s", "nidoking", "arbok", "weezing", "mewtwo", "rapidash_galarian", "slowpoke", "slowpoke_galarian", "slowbro", "slowking", "slowbro_galarian", "slowking_galarian", "celebi", "uxie", "espeon", "exeggcute", "exeggutor", "mr_rime", "<PERSON><PERSON><PERSON>", "girafarig", "me<PERSON><PERSON>", "meowstic_female", "swoobat", "<PERSON><PERSON><PERSON>", "bouffalant", "bibarel", "litleo", "pyroar", "furfrou", "porygon2", "porygon_z", "purugly", "<PERSON><PERSON><PERSON><PERSON>", "farfetchd", "bewear", "florges", "floette", "togetic", "hitmonchan", "hitmontop", "heracross", "machoke", "gur<PERSON><PERSON>", "farfetchd_galarian", "avalugg", "piloswine", "ma<PERSON><PERSON>", "cryogonal", "glaceon", "beartic", "cloyster", "darmanitan_galarian_standard", "charizard", "fletchinder", "magmortar", "rapidash", "heatmor", "typhlosion", "arcanine", "darminitan_standard", "chandelure", "delphox", "emboar", "infernape", "flareon", "barbara<PERSON>", "dwebble", "carracosta", "omastar", "kabutops", "aerodactyl", "sudowoodo", "bonsly", "rhyperior", "rhydon", "gigalith", "lycanroc_midday", "gengar", "haunter", "decid<PERSON><PERSON>", "golurk", "dusknoir", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "rai<PERSON>u", "electivire", "electrode", "ne<PERSON><PERSON><PERSON>_<PERSON><PERSON>an", "dedenne", "em<PERSON>ga", "ampha<PERSON>", "zebstrika", "eelektross", "jolteon", "heliolisk", "samu<PERSON>t", "poliwrath", "gyarados", "blastoise", "seaking", "gastrodon", "seismitoad", "vaporeon", "poliwhirl", "golduck", "feraligatr", "kingler", "lumineon", "prin<PERSON><PERSON><PERSON>", "swanna", "chesnaught", "quilladin", "<PERSON><PERSON>e", "leafeon", "tangrowth", "leavanny", "cherrim_sunny", "wormadam_plant", "wormadam_sandy", "sandslash", "<PERSON><PERSON><PERSON>", "marowak", "hippo<PERSON><PERSON>", "vespiquen", "pinsir", "<PERSON><PERSON>", "kricketune", "parasect", "scyther", "yanmega"]}]}}