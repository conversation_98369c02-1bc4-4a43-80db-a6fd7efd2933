import { PokemonType } from '@/types/pokemon';
import clsx from 'clsx';

interface TypeIconProps {
  type: PokemonType;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function TypeIcon({ type, size = 'sm', className = '' }: TypeIconProps) {
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  return (
    <span
      className={clsx(
        'type-badge',
        `type-${type}`,
        sizeClasses[size],
        className
      )}
    >
      {type.charAt(0).toUpperCase() + type.slice(1)}
    </span>
  );
}
