Gamemaster Files
The primary JSON files loaded by the project are data/gamemaster.json and data/gamemaster.min.json. The project uses the minified version by default. If the hostname includes "localhost", the project uses the non-minified version.

The files above are compiled from separate JSON files for each category of data. These are located in the data/gamemaster directory. These files include:

base.json contains the base data structure into which all other JSON data is inserted. It includes basic game settings, ranking scenario settings, Pokemon tags, Pokemon traits, generation definitions, and the list of current Shadow Pokemon.
formats.json contains data that defines which formats and metas appear in the Rankings, Team Builder, and Multi-Battle dropdowns. This file does not contain cup rules data (see below).
moves.json contains all Fast Move and Charged Move game data.
pokemon.json contains all Pokemon game data, including base stats, types, moves, tags, and more.
The data/gamemaster/cups directory contains cup rule files for each cup. The contents of each file placed in this directory is compiled into gamemaster.json. The data/gamemaster/cups/archive directory contains the cup rule files for inactive cups. These are not compiled into gamemaster.json but exist for fast reference and can be moved into the active cup directory.
These files are separated to help maintain the game data and make searching for specific data entries easier.

Modifying & Compiling the Gamemaster Files
If you are working with a local version of PvPoke and want to modify Pokemon or move data for personal use, you can do so by editing gamemaster.json directly. After making your changes, refresh your local copy of the website and the changes should be reflected with no additional steps. Note that these changes are likely to be incompatible with and overwritten by future site updates.

If you are working on a forked version of the project or want to make changes to submit to the project, follow the workflow below:

Modify one or more of the separate JSON files listed above. Do not modify gamemaster.json directly as compiling the gamemaster will overwrite those changes.
In your project directory, visit the URL src/data/compile.php' in your browser or click the "Compile Gamemaster" link in the Developer Panel. This will combine the contents of the separate JSON files into gamemaster.json`. You should then see the changes reflected on the site.
If site content fails to load after performing these steps, it's likely there is an error in your JSON syntax. You can validate the JSON file you modified at jsonlint.com to identify any errors.

Note that modifying gamemaster data will not change ranking results because the ranking data is pregenerated. However, changes to the gamemaster will reflect in custom rankings.

GameMaster.js
The GameMaster.js script is loaded on almost all pages and contains methods to load and interact with the gamemaster files above. It is a singleton object (only one instance of it can exist at a time) and this instance can be globally access through GameMaster.getInstance(). It is usually assigned the variable name gm for shorthand.

In addition to its normal utility functions, GameMaster.js also contains developer functions to help maintain the gamemaster files above. These functions include:

generateDefaultIVs() generates default IV combinations for each Pokemon entry. Default IVs are calculated with specific IV floor and rank parameters defined in the function. They are calculated for each league, and for classic formats for Ultra League and Master League. This function outputs an updated version of pokemon.json in the developer console.
updateShadowStatus() creates new entries for each Shadow Pokemon listed in the shadowPokemon array of base.json. It outputs an updated version of pokemon.json in the developer console.
Both of these functions can be triggered from the Developer Panel.