[{"name": "all", "title": "All Pokemon", "include": [], "exclude": [{"filterType": "tag", "values": ["mega"]}]}, {"name": "custom", "title": "Custom", "include": [], "exclude": []}, {"name": "gen-5", "title": "Generation 5", "include": [], "exclude": []}, {"name": "gobattleleague", "title": "GO Battle League", "include": [], "exclude": [], "partySize": 3}, {"name": "boulder", "title": "Boulder Cup", "include": [{"filterType": "type", "values": ["rock", "steel", "fighting", "ground"]}], "exclude": [], "link": "https://silph.gg/t/gyqx"}, {"name": "twilight", "title": "Twilight Cup", "include": [{"filterType": "type", "values": ["dark", "poison", "fairy", "ghost"]}], "exclude": []}, {"name": "twilightfactions", "title": "Twilight Cup", "include": [{"filterType": "type", "values": ["dark", "poison", "fairy", "ghost"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["nidoqueen", "pawniard"]}]}, {"name": "tempest", "title": "Tempest Cup", "include": [{"filterType": "type", "values": ["electric", "ice", "flying", "ground"]}], "exclude": []}, {"name": "kingdom", "title": "Kingdom Cup", "include": [{"filterType": "type", "values": ["fire", "ice", "dragon", "steel"]}], "exclude": []}, {"name": "nightmare", "title": "Nightmare Cup", "include": [{"filterType": "type", "values": ["psychic", "dark", "fighting"]}], "exclude": [{"filterType": "tag", "values": ["mythical", "legendary"]}, {"filterType": "id", "values": ["medicham", "sableye"]}]}, {"name": "regionals-1", "title": "Season 1 Regionals", "include": [{"filterType": "type", "values": ["rock", "steel", "fighting", "ground", "dark", "poison", "fairy", "ghost", "electric", "ice", "flying", "fire", "dragon"]}], "exclude": []}, {"name": "championships-1", "title": "Season 1 Championships", "include": [], "exclude": [{"filterType": "tag", "values": ["mythical", "legendary"]}]}, {"name": "rainbow", "title": "Rainbow Cup", "include": [{"filterType": "type", "values": ["fire", "water", "electric", "grass", "bug"]}, {"filterType": "dex", "values": [1, 251]}], "exclude": [{"filterType": "tag", "values": ["alolan"]}]}, {"name": "jungle", "title": "Jungle Cup", "include": [{"filterType": "type", "values": ["normal", "grass", "bug", "electric"]}], "exclude": [{"filterType": "id", "values": ["tropius", "wormadam_trash", "wormadam_sandy", "wormadam_plant", "<PERSON><PERSON>"]}]}, {"name": "safari", "title": "Safari Cup", "restrictedPicks": 2, "restrictedPokemon": ["steelix", "swampert", "swampert_shadow", "bastiodon", "bastiodon_xs", "vigoroth", "stunfisk_galarian", "<PERSON><PERSON><PERSON><PERSON>", "azumarill_xs", "<PERSON><PERSON><PERSON><PERSON>", "skar<PERSON><PERSON>_shadow", "altaria", "abomasnow", "abomasnow_shadow", "dragalge"], "include": [{"filterType": "type", "values": ["grass", "ground", "rock", "bug", "normal", "poison", "flying", "water"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}], "link": "https://www.challengermode.com/tournaments/5f92f3d5-ef90-4b52-bf88-08d98fb816d9"}, {"name": "fantasy", "title": "Fantasy Cup", "restrictedPicks": 1, "include": [{"filterType": "type", "values": ["bug", "dark", "fighting", "poison", "fairy", "ghost", "psychic"]}], "exclude": []}, {"name": "cliffhanger", "title": "<PERSON><PERSON><PERSON>", "include": [], "exclude": [], "tierRules": {"max": 17, "floor": 0, "tiers": [{"points": 9, "pokemon": ["altaria", "<PERSON><PERSON><PERSON><PERSON>", "bastiodon", "deoxys_defense", "lickitung", "<PERSON><PERSON><PERSON><PERSON>", "medicham", "stunfisk_galarian"]}, {"points": 5, "pokemon": ["abomasnow", "chansey", "cofagrigus", "cresselia", "dewgong", "diggersby", "ferrothorn", "<PERSON><PERSON><PERSON>", "hypno", "jellicent", "<PERSON>ras", "marowak_alolan", "machamp", "mandibuzz", "meganium", "nidoqueen", "ninetales_alolan", "pidgeot", "politoed", "registeel", "sableye", "scrafty", "serperior", "swampert", "talonflame", "tropius", "umbreon", "venusaur", "whiscash", "wobbuffet", "<PERSON><PERSON><PERSON>"]}, {"points": 1, "pokemon": ["aromatisse", "beedrill", "bronzong", "castform_rainy", "castform_sunny", "chesnaught", "clefable", "drapion", "drifb<PERSON>", "empoleon", "froslass", "gardevoir", "gran<PERSON>", "greedent", "gengar", "golbat", "grimer_alolan", "haunter", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "lanturn", "lugia", "mantine", "melmetal", "mew", "muk_alolan", "ninetales", "noctowl", "obstagoon", "p<PERSON><PERSON><PERSON>", "pelipper", "probopass", "regirock", "r<PERSON><PERSON>_alolan", "roserade", "shiftry", "sir<PERSON><PERSON><PERSON>", "skuntank", "slurpuff", "spritzee", "stunfisk", "sylveon", "toxicroak", "victreebel", "vigoroth", "whimsicott", "wigglytuff", "zapdos"]}]}}, {"name": "sinister", "title": "Sinister Cup", "include": [{"filterType": "type", "values": ["ghost", "psychic", "fighting", "steel"]}], "exclude": [{"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "hypno"]}, {"filterType": "tag", "values": ["mythical", "shadow"]}, {"filterType": "type", "values": ["dark"]}]}, {"name": "sinister-mirror", "title": "Sinister Cup", "include": [{"filterType": "type", "values": ["ghost", "psychic", "fighting", "steel"]}, {"filterType": "id", "values": ["shiftry", "honch<PERSON><PERSON>", "murkrow", "crawdaunt", "sharpedo"]}], "exclude": [{"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "hypno", "drifb<PERSON>", "be<PERSON><PERSON><PERSON>"]}, {"filterType": "tag", "values": ["legendary", "mythical", "shadow", "gal<PERSON>", "mega"]}, {"filterType": "type", "values": ["dark"]}]}, {"name": "ferocious", "title": "Ferocious Cup", "include": [{"filterType": "id", "values": ["absol", "aggron", "ampha<PERSON>", "arcanine", "aron", "bagon", "bibarel", "bidoof", "blitzle", "buizel", "buneary", "camerupt", "cranidos", "cubone", "delcatty", "<PERSON><PERSON><PERSON>", "drillbur", "eevee", "electrike", "entei", "espeon", "excadrill", "exploud", "flaaffy", "flareon", "floatzel", "furret", "gabite", "garcho<PERSON>", "gible", "girafarig", "glaceon", "glam<PERSON>w", "gran<PERSON>", "growlithe", "grumpig", "heatmor", "herdier", "hippopotas", "hippo<PERSON><PERSON>", "houndoom", "houndour", "jolteon", "kangaskhan", "<PERSON>on", "<PERSON>r<PERSON><PERSON>", "lickilicky", "lickitung", "lie<PERSON>", "lillipup", "linoone", "lopunny", "luxio", "luxray", "ma<PERSON><PERSON>", "manectric", "mareep", "marowak", "marowak_alolan", "meowth", "meowth_alolan", "<PERSON><PERSON>", "miltank", "minun", "nidoking", "nidoqueen", "nidoran_female", "nidoran_male", "nidorina", "<PERSON><PERSON><PERSON>", "ninetales", "ninetales_alolan", "numel", "p<PERSON><PERSON><PERSON>", "patrat", "persian", "persian_alolan", "phanpy", "pichu", "pikachu", "piloswine", "plusle", "ponyta", "poochyena", "pupitar", "purrloin", "purugly", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "rai<PERSON>u", "rampardos", "rapidash", "raticate", "raticate_alolan", "rattata", "rattata_alolan", "rhydon", "rhyhorn", "rhyperior", "sandslash", "sandslash_alolan", "sandshrew", "sandshrew_alolan", "sentret", "shelgon", "shinx", "skitty", "skuntank", "smeargle", "sneasel", "spoink", "stantler", "stoutland", "stunky", "suicune", "swin<PERSON>", "tauros", "te<PERSON><PERSON><PERSON>", "torkoal", "tyranitar", "umbreon", "ursaring", "vaporeon", "vulpix", "vulpix_alolan", "watchog", "weavile", "zangoose", "zebstrika", "zigzagoon"]}], "exclude": []}, {"name": "ferocious-mirror", "title": "Ferocious Cup", "include": [{"filterType": "id", "values": ["absol", "aggron", "ampha<PERSON>", "arcanine", "aron", "bagon", "bibarel", "bidoof", "blitzle", "buizel", "buneary", "camerupt", "cranidos", "cubone", "delcatty", "<PERSON><PERSON><PERSON>", "drillbur", "eevee", "electrike", "entei", "espeon", "excadrill", "exploud", "flaaffy", "flareon", "floatzel", "furret", "gabite", "garcho<PERSON>", "gible", "girafarig", "glaceon", "glam<PERSON>w", "gran<PERSON>", "growlithe", "grumpig", "heatmor", "herdier", "hippopotas", "hippo<PERSON><PERSON>", "houndoom", "houndour", "jolteon", "kangaskhan", "<PERSON>on", "<PERSON>r<PERSON><PERSON>", "lickilicky", "lickitung", "lie<PERSON>", "lillipup", "linoone", "lopunny", "luxio", "luxray", "ma<PERSON><PERSON>", "manectric", "mareep", "marowak", "marowak_alolan", "meowth", "meowth_alolan", "<PERSON><PERSON>", "miltank", "minun", "nidoking", "nidoqueen", "nidoran_female", "nidoran_male", "nidorina", "<PERSON><PERSON><PERSON>", "ninetales", "ninetales_alolan", "numel", "p<PERSON><PERSON><PERSON>", "patrat", "persian", "persian_alolan", "phanpy", "pichu", "pikachu", "piloswine", "plusle", "ponyta", "poochyena", "pupitar", "purrloin", "purugly", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "rai<PERSON>u", "rampardos", "rapidash", "raticate", "raticate_alolan", "rattata", "rattata_alolan", "rhydon", "rhyhorn", "rhyperior", "sandslash", "sandslash_alolan", "sandshrew", "sandshrew_alolan", "sentret", "shelgon", "shinx", "skitty", "skuntank", "smeargle", "sneasel", "spoink", "stantler", "stoutland", "stunky", "suicune", "swin<PERSON>", "tauros", "te<PERSON><PERSON><PERSON>", "torkoal", "tyranitar", "ursaring", "vaporeon", "vulpix", "vulpix_alolan", "watchog", "weavile", "zebstrika", "zigzagoon", "linoone_galarian", "<PERSON>rserker", "beartic", "gabite", "garcho<PERSON>", "aerodactyl", "articuno", "moltres", "zapdos", "lugia", "ho_oh", "salamence"]}], "exclude": []}, {"name": "timeless", "title": "Timeless Cup", "restrictedPicks": 1, "restrictedPokemon": ["bulbasaur", "ivysaur", "venusaur", "squirtle", "wartortle", "blastoise", "charmander", "charmeleon", "charizard", "chikorita", "bayleef", "meganium", "totodile", "cro<PERSON><PERSON>", "feraligatr", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "treecko", "g<PERSON><PERSON>", "sceptile", "mudkip", "marshtomp", "swampert", "torchic", "combusken", "blaziken", "turtwig", "grotle", "torterra", "pip<PERSON>p", "prin<PERSON><PERSON><PERSON>", "empoleon", "chim<PERSON>r", "monferno", "infernape"], "include": [{"filterType": "dex", "values": [1, 488]}, {"filterType": "id", "values": ["blaziken", "charizard", "charizard_shadow", "infernape", "empoleon", "monferno", "prin<PERSON><PERSON><PERSON>", "combusken"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "alolan", "shadow"]}, {"filterType": "type", "values": ["fighting", "flying", "normal", "psychic", "steel", "fairy"]}, {"filterType": "id", "values": ["umbreon", "sableye", "sableye_shadow"]}]}, {"name": "timeless-mirror", "title": "Timeless Cup", "restrictedPicks": 1, "restrictedPokemon": ["bulbasaur", "ivysaur", "venusaur", "squirtle", "wartortle", "blastoise", "charmander", "charmeleon", "charizard", "chikorita", "bayleef", "meganium", "totodile", "cro<PERSON><PERSON>", "feraligatr", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "treecko", "g<PERSON><PERSON>", "sceptile", "mudkip", "marshtomp", "swampert", "torchic", "combusken", "blaziken", "turtwig", "grotle", "torterra", "pip<PERSON>p", "prin<PERSON><PERSON><PERSON>", "empoleon", "chim<PERSON>r", "monferno", "infernape"], "include": [{"filterType": "dex", "values": [1, 488]}, {"filterType": "id", "values": ["blaziken", "charizard", "infernape", "empoleon", "monferno", "prin<PERSON><PERSON><PERSON>", "combusken"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "alolan", "shadow", "mega"]}, {"filterType": "type", "values": ["fighting", "flying", "normal", "psychic", "steel", "fairy"]}, {"filterType": "id", "values": ["umbreon", "king<PERSON>", "abomasnow", "castform_snowy", "froslass", "rotom_wash", "rotom_wash"]}]}, {"name": "fusion", "title": "Fusion Cup", "include": [], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "shadow"]}, {"filterType": "type", "values": ["none"]}, {"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "medicham", "dewgong", "wormadam_trash", "wormadam_plant", "wormadam_sandy", "hypno", "munchlax", "linoone", "onix", "<PERSON><PERSON><PERSON>", "hitmonchan", "chansey", "mr.mime", "scyther", "jynx", "electabuzz", "magmar", "magmar_shadow", "pinsir", "<PERSON>ras", "eevee", "vaporeon", "jolteon", "flareon", "omanyte", "omastar", "omastar_shadow", "kabuto", "kabutops", "aerodactyl", "snorlax", "dratini", "dragonair", "dragonite", "mareep", "flaafy", "ampha<PERSON>", "sudowoodo", "espeon", "umbreon", "pineco", "forretress", "gligar", "steelix", "s<PERSON><PERSON>", "delibird", "mantine", "<PERSON><PERSON><PERSON><PERSON>", "hitmontop", "miltank", "blissey", "<PERSON>r<PERSON><PERSON>", "pupitar", "tyranitar", "ralts", "kirlia", "gardevoir", "slakoth", "vigoroth", "slaking", "nincada", "ninjask", "sableye", "ma<PERSON>le", "trapinch", "vibrava", "flygon", "milotic", "castform", "castform_sunny", "castform_rainy", "castform_snowy", "tropius", "chimecho", "absol", "absol_shadow", "relicanth", "bagon", "shelgon", "salamence", "metang", "metagross", "shinx", "luxio", "luxray", "cranidos", "rampardos", "shieldon", "bastiodon", "p<PERSON><PERSON><PERSON>", "drifloon", "drifb<PERSON>", "chatot", "spiritomb", "gible", "gabite", "garcho<PERSON>", "lucario", "s<PERSON><PERSON><PERSON>", "drapion", "carnivine", "electivire", "magmortar", "leafeon", "glaceon", "gliscor", "gallade", "ferroseed", "ferrothorn", "klink", "klang", "klinklang", "litwick", "lampent", "chandelure", "go<PERSON>", "golurk", "deino", "<PERSON><PERSON><PERSON>", "hydreigon", "mr_mime", "t<PERSON><PERSON><PERSON>", "carracosta", "archeops", "archen", "stunfisk", "alomomola", "timburr", "gur<PERSON><PERSON>", "con<PERSON><PERSON><PERSON>", "rufflet", "braviary", "cryogonal", "axew", "haxorus", "fraxure", "sigilyph", "t<PERSON><PERSON><PERSON>", "carracosta"]}]}, {"name": "fusionfactions", "title": "Fusion Cup", "include": [{"filterType": "cost", "values": [10000, 50000]}], "exclude": [{"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "medicham", "dewgong", "scrafty", "jellicent", "nidoqueen", "diggersby", "walrein", "electrode_hisuian", "oricorio_baile", "oricorio_pau", "oricorio_pom_pom", "oricorio_sensu"]}, {"filterType": "tag", "values": ["mega"]}, {"filterType": "type", "values": ["none"]}]}, {"name": "rose", "title": "Rose Cup", "include": [{"filterType": "id", "values": ["charmander", "charmeleon", "charizard", "vileplume", "paras", "parasect", "voltorb", "electrode", "jynx", "magmar", "flareon", "ledyba", "<PERSON><PERSON>", "a<PERSON><PERSON>", "yanma", "s<PERSON><PERSON>", "slugma", "magcargo", "delibird", "porygon2", "magby", "torchic", "combusken", "blaziken", "wur<PERSON>", "medicham", "camerupt", "solrock", "castform_sunny", "kricketot", "kricketune", "wormadam_trash", "magmortar", "porygon_z", "tepig", "pignite", "emboar", "pansear", "simisear", "heatmor", "rattata", "ekans", "arbok", "nidoran_male", "<PERSON><PERSON><PERSON>", "nidoking", "zubat", "golbat", "venonat", "venomoth", "grimer", "muk", "gastly", "haunter", "gengar", "marowak_alolan", "koffing", "weezing", "aerodactyl", "crobat", "aipom", "espeon", "forretress", "gligar", "gran<PERSON>", "tyrogue", "cascoon", "delcatty", "sableye", "illumise", "swalot", "grumpig", "lileep", "cherrim_overcast", "ambipom", "drifloon", "drifb<PERSON>", "mismagius", "stunky", "skuntank", "spiritomb", "s<PERSON><PERSON><PERSON>", "gliscor", "purrloin", "lie<PERSON>", "cle<PERSON>y", "clefable", "jigglypuff", "wigglytuff", "exeggcute", "mr_mime", "porygon", "cleffa", "igglybuff", "flaaffy", "hoppip", "snubbull", "smoochum", "miltank", "blissey", "whismur", "skitty", "cherubi", "cherrim_sunny", "happiny", "lickilicky", "drapion", "lickitung", "throh", "venipede", "whirlipede", "scolipede", "throh", "dwebble", "crustle", "shelmet", "accelgor", "machoke", "machop", "machamp", "geodude_alolan", "graveler_alolan", "golem_alolan", "magnemite", "magneton", "onix", "weezing_galarian", "rhyhorn", "rhydon", "misdreavus", "pineco", "steelix", "remoraid", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "pupitar", "poochyena", "<PERSON><PERSON>", "nincada", "nosepass", "aron", "<PERSON>on", "aggron", "volbeat", "anorith", "arm<PERSON>", "castform", "snorunt", "glalie", "glam<PERSON>w", "purugly", "magnezone", "rhyperior", "probopass", "herdier", "stoutland", "pidove", "tranquill", "unfezant", "drilbur", "excadrill", "timburr", "gur<PERSON><PERSON>", "whirlipede", "escavalier", "ferroseed", "ferrothorn", "klink", "klang", "klinklang", "durant", "chansey", "minccino", "cinccino", "darum<PERSON>", "darmanitan_standard", "scrafty"]}], "exclude": [{"filterType": "type", "values": ["water"]}]}, {"name": "toxic", "title": "Toxic Cup", "include": [{"filterType": "type", "values": ["bug", "poison", "normal", "ground", "grass"]}], "exclude": [{"filterType": "id", "values": ["noctowl", "vigoroth", "tropius", "whiscash", "swampert", "quagsire", "gastrodon", "gastrodon", "seismitoad", "palpitoad", "marshtomp", "swampert_shadow", "marshtomp_shadow", "genesect"]}]}, {"name": "voyager", "title": "Voyager Cup", "restrictedPicks": 1, "restrictedPokemon": ["altaria", "<PERSON><PERSON><PERSON><PERSON>", "bastiodon", "cresselia", "deoxys_defense", "dewgong", "gardevoir", "gardevoir_shadow", "hypno", "hypno_shadow", "ivysaur", "ivy<PERSON>_shadow", "jellicent", "<PERSON>ras", "lap<PERSON>_shadow", "mandibuzz", "marowak_alolan", "medicham", "melmetal", "mew", "probopass", "registeel", "scrafty", "<PERSON><PERSON><PERSON><PERSON>", "swampert", "swampert_shadow", "<PERSON><PERSON><PERSON><PERSON>", "tropius", "umbreon", "venusaur", "venusaur_shadow", "whiscash", "wigglytuff", "<PERSON><PERSON><PERSON>"], "include": [], "exclude": []}, {"name": "beam", "title": "Get Beamed", "include": [{"filterType": "id", "values": ["meganium", "venusaur", "mew", "wigglytuff", "lickitung", "grotle", "typhlosion", "ivysaur", "lickilicky", "victreebel", "ludico<PERSON>", "snorlax", "milotic", "leafeon", "dragonite", "vileplume", "wailord", "tangrowth", "castform_sunny", "latios", "sunflora", "ninetales", "torterra", "electrode", "cherrim_sunny", "cherrim_overcast", "tangela", "bibarel", "raticate_alolan", "infernape", "torkoal", "parasect", "celebi", "simisage", "cryogonal", "exeggutor_alolan", "chansey", "ursaring", "exeggutor", "cinccino", "unfezant", "gigalith", "luxray", "aerodactyl", "furret", "solrock", "blissey", "roserade", "porygon", "<PERSON><PERSON><PERSON>", "maractus", "porygon2", "ho_oh", "ambipom", "raticate", "camerupt", "audino", "weezing_galarian", "porygon_z", "lopunny", "slaking"]}], "exclude": [{"filterType": "type", "values": ["steel"]}, {"filterType": "tag", "values": ["shadow"]}]}, {"name": "forest", "title": "Forest Cup", "include": [{"filterType": "type", "values": ["grass", "water", "fire", "electric", "bug"]}, {"filterType": "dex", "values": [152, 649]}], "exclude": [{"filterType": "id", "values": ["tropius", "genesect", "rai<PERSON>u"]}, {"filterType": "tag", "values": ["shadow"]}]}, {"name": "premier", "title": "Premier Cup", "include": [], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "mega"]}], "partySize": 3}, {"name": "premierclassic", "title": "Premier Classic", "include": [], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "mega"]}], "partySize": 3, "levelCap": 40}, {"name": "classic", "title": "Classic", "include": [], "exclude": [{"filterType": "tag", "values": ["xl", "mega"]}], "partySize": 3, "levelCap": 40}, {"name": "grunt", "title": "Grunt Cup", "restrictedPokemon": ["ivysaur", "ivy<PERSON>_shadow", "venusaur", "venusaur_shadow", "wartortle", "wartortle_shadow", "blastoise", "blastoise_shadow", "charmeleon", "charm<PERSON>on_shadow", "charizard", "charizard_shadow"], "include": [{"filterType": "id", "values": ["ferrothorn", "alomomola", "escavalier", "<PERSON><PERSON><PERSON>", "victini", "serperior", "klang", "<PERSON>ras", "shiftry", "gallade", "hypno", "sableye", "<PERSON><PERSON><PERSON>", "muk", "grimer", "grimer_shadow", "muk_shadow", "lap<PERSON>_shadow", "shiftry_shadow", "gallade_shadow", "hypno_shadow", "sableye_shadow", "al<PERSON><PERSON>_shadow", "exeggutor", "exeggutor_shadow", "sharpedo", "sharpedo_shadow", "metagross", "metagross_shadow", "houndoom", "houndoom_shadow", "charizard", "charizard_shadow", "blastoise", "blastoise_shadow", "fortress", "fortress_shadow", "dragonite", "dragonite_shadow", "steelix", "crobat", "crobat_shadow", "ma<PERSON>le", "mawile_shadow", "s<PERSON><PERSON>", "sci<PERSON>_shadow", "zapdos", "zap<PERSON>_shadow", "moltres", "moltres_shadow", "articuno", "articuno_shadow", "suicune", "suicune_shadow", "rhyperior", "machamp", "machamp_shadow", "rhydon", "khangaskan", "golem_alolan", "graveler_alolan", "gliscor", "gliscor_shadow", "victreebel", "victree<PERSON>_shadow", "gyarados", "gyarado<PERSON>_shadow", "arcanine", "arcanine_shadow", "venomoth", "venomoth_shadow", "cradily", "lanturn", "skuntank", "skuntank_shadow", "sealeo", "beedrill", "beedrill_shadow", "weezing", "weezing_shadow", "golbat", "arbok", "arbok_shadow", "wobbuffet", "wobbuffet_shadow", "lickitung", "tentacruel", "electrode", "haunter", "cloyster", "gengar", "clefable", "golbat_shadow", "poliwrath", "poliwrath_shadow", "forretress", "forretress_shadow", "magneton", "magneton_shadow"]}], "exclude": [], "slots": [{"pokemon": ["ferrothorn", "alomomola", "escavalier", "<PERSON><PERSON><PERSON>", "victini", "serperior", "klang", "muk", "grimer", "muk_shadow", "grimer_shadow"]}, {"pokemon": ["<PERSON>ras", "lap<PERSON>_shadow", "shiftry", "shiftry_shadow", "gallade", "gallade_shadow", "hypno", "hypno_shadow", "sableye", "sableye_shadow", "<PERSON><PERSON><PERSON>", "al<PERSON><PERSON>_shadow", "exeggutor", "exeggutor_shadow", "sharpedo", "sharpedo_shadow", "metagross", "metagross_shadow", "houndoom", "houndoom_shadow"]}, {"pokemon": ["charizard", "charizard_shadow", "blastoise", "blastoise_shadow", "forretress", "forretress_shadow", "dragonite", "dragonite_shadow", "magneton", "magneton_shadow", "steelix", "golbat", "golbat_shadow", "crobat", "crobat_shadow", "ma<PERSON>le", "mawile_shadow", "s<PERSON><PERSON>", "sci<PERSON>_shadow"]}, {"pokemon": ["zapdos", "moltres", "articuno", "zap<PERSON>_shadow", "moltres_shadow", "articuno_shadow", "suicune", "suicune_shadow", "rhyperior", "machamp", "machamp_shadow", "rhydon", "khangaskan", "graveler_alolan", "golem_alolan", "gliscor", "gliscor_shadow"]}, {"pokemon": ["victreebel", "victree<PERSON>_shadow", "arcanine", "arcanine_shadow", "gyarados", "gyarado<PERSON>_shadow", "venomoth", "venomoth_shadow", "cradily", "weezing", "weezing_shadow", "lanturn", "skuntank", "skuntank_shadow", "sealeo", "beedrill", "beedrill_shadow"]}, {"pokemon": ["wobbuffet", "wobbuffet_shadow", "arbok", "arbok_shadow", "lickitung", "poliwrath", "poliwrath_shadow", "tentacruel", "electrode", "haunter", "gengar", "cloyster", "clefable"]}]}, {"name": "sorcerous", "title": "Sorcerous Cup", "include": [{"filterType": "type", "values": ["ice", "poison", "fairy", "psychic", "normal"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "shadow"]}, {"filterType": "id", "values": ["drapion", "ma<PERSON>le", "metang", "metagross", "<PERSON>ras", "dewgong", "snorlax", "vigoroth", "slaking", "castform", "rufflet", "braviary", "chansey", "blissey", "mr_mime", "jynx", "eevee", "espeon", "delibird", "miltank", "gardevoir", "gallade", "chimecho", "chatot", "s<PERSON><PERSON><PERSON>", "sigilyph", "cryogonal", "glaceon", "castform_snowy", "r<PERSON><PERSON>_alolan", "linoone_galarian", "obstagoon"]}]}, {"name": "sorcerous-mirror", "title": "Sorcerous Cup", "include": [{"filterType": "type", "values": ["ice", "poison", "fairy", "psychic", "normal"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "shadow", "mega"]}, {"filterType": "id", "values": ["drapion", "ma<PERSON>le", "<PERSON>ras", "dewgong", "vigoroth", "slaking", "castform", "rufflet", "braviary", "chansey", "blissey", "mr_mime", "jynx", "eevee", "espeon", "delibird", "miltank", "gardevoir", "metang", "chimecho", "chatot", "s<PERSON><PERSON><PERSON>", "sigilyph", "cryogonal", "glaceon", "castform_snowy", "r<PERSON><PERSON>_alolan", "obstagoon", "bouffalant", "whimsicott"]}]}, {"name": "<PERSON><PERSON>mu<PERSON>", "title": "GOTeamUp", "link": "https://www.stadiumgaming.gg/post/gtu6-is-here", "include": [], "exclude": [{"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "skar<PERSON><PERSON>_shadow", "altaria", "registeel", "meganium", "abomasnow", "abomasnow_shadow"]}, {"filterType": "tag", "values": ["mega"]}]}, {"name": "continentals-2", "title": "Season 2 continentals", "include": [{"filterType": "id", "values": ["charizard", "blaziken", "typhlosion", "castform_sunny", "ninetales", "torkoal", "heatmor", "moltres", "bellossom", "tangrowth", "ludico<PERSON>", "sceptile", "cherrim_sunny", "serperior", "breloom", "exeggutor", "grotle", "tangela", "carnivine", "parasect", "castform_rainy", "blastoise", "alomomola", "seaking", "empoleon", "milotic", "wailord", "prin<PERSON><PERSON><PERSON>", "bibarel", "gyarados", "feraligatr", "golduck", "kingler", "suicune", "entei", "crawdaunt", "sharpedo", "cloyster", "vaporeon", "marowak_alolan", "sableye", "froslass", "drifb<PERSON>", "banette", "dusclops", "golurk", "munchlax", "snorlax", "lickitung", "lickilicky", "zangoose", "linoone", "raticate_alolan", "noctowl", "pidgeot", "dunsparce", "ursaring", "castform", "furret", "porygon", "porygon2", "porygon_z", "delcatty", "girafarig", "machamp", "hitmontop", "primeape", "lucario", "poliwrath", "gallade", "hitmonchan", "<PERSON>rserker", "chansey", "blissey", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "dragonair", "shelgon", "wigglytuff", "clefable", "gran<PERSON>", "ma<PERSON>le", "registeel", "melmetal", "klang", "klinklang", "muk_alolan", "haunter", "gengar", "golbat", "skuntank", "drapion", "muk", "nidoqueen", "nidoking", "amoon<PERSON>s", "qwilfish", "tentacruel", "<PERSON>ras", "ninetales_alolan", "abomasnow", "walrein", "piloswine", "ma<PERSON><PERSON>", "cryogonal", "glaceon", "beartic", "regice", "relicanth", "regirock", "carracosta", "corsola", "cradily", "graveler_alolan", "golem_alolan", "sudowoodo", "aggron", "<PERSON>on", "magcargo", "bonsly", "aerodactyl", "archen", "archeops", "victreebel", "vileplume", "gloom", "seviper", "swalot", "rhyperior", "lunatone", "solrock", "probopass", "mantine", "zapdos", "dragonite", "gliscor", "gligar", "<PERSON><PERSON><PERSON><PERSON>", "honch<PERSON><PERSON>", "electivire", "rai<PERSON>u", "lanturn", "magneton", "magnezone", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "electrode", "zebstrika", "minun", "plusle", "ampha<PERSON>", "stunfisk", "p<PERSON><PERSON><PERSON>", "marshtomp", "quagsire", "seismitoad", "palpitoad", "flygon", "torterra", "steelix", "claydol", "excadrill", "garcho<PERSON>", "gastrodon", "gastrodon", "hippo<PERSON><PERSON>", "camerupt", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "bronzong", "lugia", "wobbuffet", "celebi", "latios", "latias", "slowbro", "slowking", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "metagross", "sneasel", "weavile", "tyranitar", "shiftry", "escavalier", "forretress", "heracross", "<PERSON><PERSON><PERSON>", "crustle", "s<PERSON><PERSON>", "beedrill", "vespiquen", "scolipede", "ninjask", "venomoth", "dustox", "obstagoon", "wormadam_trash", "wormadam_plant", "wormadam_sandy", "durant", "hydreigon", "mr_mime", "metang", "pinsir", "kricketune", "masquerain", "yanma", "yanmega"]}], "exclude": [], "slots": [{"pokemon": ["charizard", "blaziken", "typhlosion", "castform_sunny", "ninetales", "torkoal", "heatmor", "moltres", "bellossom", "tangrowth", "ludico<PERSON>", "sceptile", "cherrim_sunny", "serperior", "breloom", "exeggutor", "grotle", "tangela", "carnivine", "parasect", "castform_rainy", "blastoise", "alomomola", "seaking", "empoleon", "milotic", "wailord", "prin<PERSON><PERSON><PERSON>", "bibarel", "gyarados", "feraligatr", "golduck", "kingler", "suicune", "entei", "crawdaunt", "sharpedo", "cloyster", "vaporeon"]}, {"pokemon": ["marowak_alolan", "sableye", "froslass", "drifb<PERSON>", "banette", "dusclops", "golurk", "munchlax", "snorlax", "lickitung", "lickilicky", "zangoose", "linoone", "raticate_alolan", "noctowl", "pidgeot", "dunsparce", "ursaring", "castform", "furret", "porygon", "porygon2", "porygon_z", "delcatty", "girafarig", "machamp", "hitmontop", "primeape", "lucario", "poliwrath", "gallade", "hitmonchan", "<PERSON>rserker", "chansey", "blissey"]}, {"pokemon": ["<PERSON><PERSON><PERSON><PERSON>", "altaria", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "dragonair", "shelgon", "wigglytuff", "clefable", "gran<PERSON>", "ma<PERSON>le", "registeel", "stunfisk_galarian", "melmetal", "klang", "klinklang"]}, {"pokemon": ["muk_alolan", "haunter", "gengar", "golbat", "skuntank", "drapion", "muk", "nidoqueen", "nidoking", "amoon<PERSON>s", "qwilfish", "tentacruel", "<PERSON>ras", "ninetales_alolan", "abomasnow", "castform_snowy", "articuno", "walrein", "sandslash_alolan", "glalie", "piloswine", "ma<PERSON><PERSON>", "cryogonal", "glaceon", "beartic", "regice", "relicanth", "regirock", "carracosta", "corsola", "cradily", "graveler_alolan", "golem_alolan", "sudowoodo", "aggron", "<PERSON>on", "magcargo", "bonsly", "aerodactyl", "archen", "archeops", "victreebel", "vileplume", "gloom", "seviper", "swalot", "toxicroak", "rhyperior", "lunatone", "solrock", "probopass"]}, {"pokemon": ["mantine", "zapdos", "dragonite", "gliscor", "gligar", "<PERSON><PERSON><PERSON><PERSON>", "honch<PERSON><PERSON>", "electivire", "rai<PERSON>u", "lanturn", "magneton", "magnezone", "r<PERSON><PERSON>", "electrode", "zebstrika", "minun", "plusle", "ampha<PERSON>", "stunfisk", "p<PERSON><PERSON><PERSON>", "marshtomp", "quagsire", "seismitoad", "palpitoad", "flygon", "torterra", "steelix", "claydol", "excadrill", "garcho<PERSON>", "gastrodon", "gastrodon", "hippo<PERSON><PERSON>", "camerupt", "<PERSON><PERSON><PERSON>"]}, {"pokemon": ["<PERSON><PERSON><PERSON>", "bronzong", "lugia", "wobbuffet", "celebi", "latios", "latias", "slowbro", "slowking", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "metagross", "r<PERSON><PERSON>_alolan", "sneasel", "weavile", "tyranitar", "shiftry", "escavalier", "forretress", "heracross", "<PERSON><PERSON><PERSON>", "crustle", "s<PERSON><PERSON>", "beedrill", "vespiquen", "scolipede", "ninjask", "venomoth", "dustox", "linoone_galarian", "obstagoon", "wormadam_trash", "wormadam_plant", "wormadam_sandy", "durant", "hydreigon", "mr_mime", "metang", "pinsir", "kricketune", "masquerain", "yanma", "yanmega"]}]}, {"name": "catacomb", "title": "Catacomb Cup", "include": [{"filterType": "type", "values": ["dark", "grass", "rock", "ice", "bug"]}], "exclude": [{"filterType": "id", "values": ["scrafty", "obstagoon", "dewgong", "ninetales_alolan", "crustle"]}, {"filterType": "type", "values": ["steel"]}, {"filterType": "tag", "values": ["legendary", "mythical", "shadow", "regional"]}]}, {"name": "scoville", "title": "Scoville Cup", "include": [], "exclude": [{"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "registeel", "altaria", "stunfisk_galarian", "deoxys_defense", "swampert", "mantine", "umbreon", "tropius", "cresselia", "swampert_shadow", "dewgong", "mew", "bastiodon", "raikou_shadow", "<PERSON><PERSON><PERSON>", "whiscash", "vigoroth", "meganium", "machamp", "venusaur", "scrafty", "<PERSON><PERSON><PERSON><PERSON>", "castform_rainy", "regirock", "munchlax", "rai<PERSON>u", "shiftry_shadow", "grotle_shadow", "<PERSON>ras", "medicham", "sableye_shadow", "ferrothorn", "wigglytuff", "zapdos", "<PERSON>rserker", "primeape", "zap<PERSON>_shadow", "snorlax", "flygon_shadow", "lap<PERSON>_shadow", "gloom_shadow", "dragonite_shadow", "noctowl", "obstagoon", "venusaur_shadow", "shiftry", "clefable", "hitmontop", "muk_alolan", "quagsire", "sableye", "melmetal", "politoed_shadow", "bellossom", "blastoise", "victree<PERSON>_shadow", "gardevoir_shadow", "lickitung", "snor<PERSON>_shadow", "marowak_alolan", "dragonair_shadow", "grotle", "flygon", "regice", "gliscor", "bellossom_shadow", "electivire_shadow", "lanturn", "politoed", "r<PERSON><PERSON>_alolan", "vileplume_shadow", "zangoose", "escavalier", "ludico<PERSON>", "stunfisk", "lickilicky", "magnezone", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "seaking", "alomomola", "hypno", "magnezone_shadow", "tangtowth", "ivysaur", "r<PERSON><PERSON>", "typhlosion", "ninetales_alolan", "dragonair", "haunter", "toxicroak", "gloom", "to<PERSON><PERSON>_shadow", "victreebel", "drapion", "golbat_shadow", "<PERSON><PERSON><PERSON>", "blastoise_shadow"]}]}, {"name": "mexico", "title": "Mexico Cup", "link": "https://silph.gg/t/rrtb", "include": [{"filterType": "type", "values": ["poison", "fighting", "grass", "ghost", "flying", "ground"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "mega"]}]}, {"name": "kaiser", "title": "Kaiser Invitational", "include": [], "exclude": [{"filterType": "id", "values": ["stunfisk_galarian", "bastiodon", "registeel", "ferrothorn", "deoxys_defense", "<PERSON><PERSON><PERSON><PERSON>", "altaria"]}, {"filterType": "tag", "values": ["regional", "mega"]}]}, {"name": "shadow", "title": "Shadow Cup", "link": "https://silph.gg/t/r4hu/team-rocket-academy-presents-shadow-cup-3-0", "include": [{"filterType": "tag", "values": ["shadow"]}], "exclude": []}, {"name": "flying", "title": "Flying Cup", "include": [{"filterType": "type", "values": ["flying"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}], "partySize": 3}, {"name": "circus", "title": "Circus Cup", "link": "https://silph.gg/players-choice-finalists", "include": [{"filterType": "type", "name": "Type", "values": ["fighting", "fire", "flying", "poison", "psychic"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["legendary", "mythical", "alolan"]}, {"filterType": "id", "name": "Species", "values": ["altaria"]}]}, {"name": "maelstrom", "title": "Maelstrom Cup", "link": "https://silph.gg/players-choice-finalists", "include": [{"filterType": "type", "name": "Type", "values": ["bug", "normal", "psychic", "water"]}], "exclude": [{"filterType": "id", "name": "Species", "values": ["deoxys_defense", "wigglytuff"]}, {"filterType": "tag", "name": "Tag", "values": ["shadow"]}], "overrides": [], "league": 1500}, {"name": "origin", "title": "Origin Cup", "link": "https://silph.gg/players-choice-finalists", "include": [{"filterType": "id", "name": "Species", "values": ["bulbasaur", "ivysaur", "venusaur", "charmander", "charmeleon", "charizard", "squirtle", "wartortle", "blastoise", "eevee", "jolteon", "vaporeon", "flareon", "espeon", "umbreon", "rattata", "raticate", "raticate_alolan", "pidgey", "pidgeotto", "pidgeot", "caterpie", "metapod", "butterfree", "weedle", "kakuna", "beedrill", "ekans", "arbok", "pichu", "pikachu", "r<PERSON><PERSON>", "r<PERSON><PERSON>_alolan", "spearow", "fearow", "nidoran", "<PERSON><PERSON><PERSON>", "nidoking", "nidorina", "nidoqueen", "mankey", "primeape", "chikorita", "bayleef", "meganium", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "totodile", "cro<PERSON><PERSON>", "feraligatr", "sentret", "furret", "hoothoot", "noctowl", "ledyba", "<PERSON><PERSON>", "spinarak", "a<PERSON><PERSON>", "mareep", "flaaffy", "ampha<PERSON>", "hoppip", "skip<PERSON>", "<PERSON><PERSON><PERSON>", "wooper", "quagsire", "bellsprout", "weepinbell", "victreebel", "geodude", "graveler", "golem", "geodude_alolan", "graveler_alolan", "golem_alolan", "dunsparce", "onix", "steelix", "treecko", "g<PERSON><PERSON>", "sceptile", "torchic", "combusken", "blaziken", "mudkip", "marshtomp", "swampert", "zigzagoon", "linoone", "linoone_galarian", "obstagoon", "wur<PERSON>", "silcoon", "beautifly", "cascoon", "dustox", "poochyena", "<PERSON><PERSON>", "seedot", "nuzleaf", "shiftry", "lotad", "lombre", "ludico<PERSON>", "ralts", "kirlia", "gardevoir", "gallade", "surskit", "masquerain", "wingull", "pelliper", "taillow", "swellow", "shroomish", "breloom", "slakoth", "vigoroth", "slaking", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "turtwig", "grotle", "torterra", "chim<PERSON>r", "monferno", "infernape", "pip<PERSON>p", "prinplug", "empoleon", "doduo", "dodrio", "shinx", "luxio", "luxray", "zubat", "golbat", "crobat", "abra", "kadabra", "<PERSON><PERSON><PERSON>", "cubone", "marowak", "marowak_alolan", "budew", "roselia", "roserade", "ponyta", "rapidash", "machop", "machoke", "machamp", "bidoof", "bibarel", "stary", "staravia", "staraptor", "magi<PERSON><PERSON>", "gyarados", "psyduck", "golduck", "snivy", "<PERSON><PERSON>e", "serperior", "tepig", "pignite", "emboar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "samu<PERSON>t", "igglybuff", "jigglypuff", "wigglytuff", "patrat", "watchog", "lillipup", "herdier", "stoutland", "lickitung", "<PERSON><PERSON><PERSON>", "purrloin", "lie<PERSON>", "audino", "pansear", "simisear", "pansage", "simisage", "panpour", "simipour"]}], "exclude": [], "overrides": [], "league": 1500}, {"name": "duet", "title": "Duet Cup", "link": "https://silph.gg/players-choice-finalists", "include": [{"filterType": "id", "name": "Species", "values": ["sneasel", "whimsicott", "em<PERSON>ga", "be<PERSON><PERSON><PERSON>", "bouffalant", "accelgor", "aerodactyl", "aipom", "alomomola", "ambipom", "amoon<PERSON>s", "anorith", "archen", "archeops", "a<PERSON><PERSON>", "arm<PERSON>", "audino", "basculin", "bastiodon", "beartic", "bibarel", "bonsly", "braviary", "breloom", "bronzong", "buneary", "camerupt", "carnivine", "carracosta", "castform", "castform_rainy", "castform_snowy", "castform_sunny", "chatot", "cherrim_overcast", "cherrim_sunny", "chimecho", "chinchou", "cinccino", "clamperl", "claydol", "cloyster", "cofagrigus", "corsola", "cradily", "cranidos", "crawdaunt", "crustle", "cryogonal", "darmanitan_standard", "darum<PERSON>", "delcatty", "dewgong", "dodrio", "<PERSON><PERSON><PERSON>", "drapion", "drifb<PERSON>", "drifloon", "drilbur", "<PERSON><PERSON>o", "dug<PERSON><PERSON>_alolan", "dunsparce", "durant", "dwebble", "electrode", "escavalier", "espeon", "excadrill", "exeggutor_alolan", "farfetchd", "fearow", "ferrothorn", "flareon", "floatzel", "froslass", "furret", "<PERSON><PERSON><PERSON>", "garbodor", "gastrodon", "gastrodon", "girafarig", "glaceon", "glalie", "go<PERSON>", "golurk", "gorebyss", "gran<PERSON>", "grimer_alolan", "grumpig", "<PERSON><PERSON><PERSON>", "heatmor", "heracross", "hippopotas", "hippo<PERSON><PERSON>", "hitmontop", "honch<PERSON><PERSON>", "hunt<PERSON>", "illumise", "jolteon", "jynx", "kabuto", "kabutops", "kangaskhan", "kingler", "krabby", "kricketune", "lanturn", "leafeon", "<PERSON><PERSON>", "lickilicky", "lickitung", "lie<PERSON>", "lileep", "lilligant", "linoone", "lopunny", "lucario", "lumineon", "lunatone", "magcargo", "manectric", "mantine", "mantyke", "maractus", "marowak_alolan", "masquerain", "<PERSON><PERSON>", "milotic", "miltank", "minun", "<PERSON><PERSON>", "mr.mime", "muk_alolan", "munchlax", "murkrow", "ninetales_alolan", "noctowl", "nosepass", "octillery", "onix", "p<PERSON><PERSON><PERSON>", "parasect", "pelipper", "persian_alolan", "phanpy", "pineco", "plusle", "ponyta", "primeape", "probopass", "purugly", "quagsire", "qwilfish", "rampardos", "rapidash", "raticate_alolan", "relicanth", "rotom_wash", "rufflet", "sandshrew_alolan", "sandslash_alolan", "sawk", "scrafty", "scraggy", "seaking", "sealeo", "seviper", "shellos_east_sea", "shellos_west_sea", "sigilyph", "simipour", "simisage", "simisear", "slowbro", "slowking", "slowpoke", "smoochum", "snubbull", "solrock", "spinda", "spiritomb", "spoink", "starmie", "steelix", "stunfisk", "sudowoodo", "sunflora", "swalot", "swanna", "swellow", "swoobat", "tangela", "tangrowth", "tauros", "te<PERSON><PERSON><PERSON>", "tentacruel", "throh", "t<PERSON><PERSON><PERSON>", "torkoal", "toxicroak", "umbreon", "ursaring", "vaporeon", "vespiquen", "volbeat", "wailmer", "wailord", "walrein", "watchog", "whiscash", "wormadam_plant", "wormadam_sandy", "wormadam_trash", "xatu", "yanma", "yanmega", "zangoose", "zebstrika", "raticate", "sandshrew", "sandslash", "arbok", "persian", "ninetales", "venomoth", "golduck", "arcanine", "muk", "grimer", "hypno", "exeggutor", "marowak", "<PERSON><PERSON><PERSON>", "hitmonchan", "weezing", "weezing_galarian", "chansey", "mr_mime", "scyther", "pinsir", "gyarados", "<PERSON>ras", "omanyte", "omastar", "snorlax", "misdreavus", "wobbuffet", "forretress", "gligar", "s<PERSON><PERSON>", "houndoom", "stantler", "miltank", "blissey", "ninjask", "sableye", "ma<PERSON>le", "sharpedo", "cacturne", "banette", "absol", "farfetchd_galarian", "mismagius", "skuntank", "bronzong", "abomasnow", "weavile", "gliscor", "darman<PERSON>n_galarian", "<PERSON>rserker"]}], "exclude": []}, {"name": "sunrise", "title": "sunrise Cup", "include": [{"filterType": "type", "values": ["fire", "flying", "grass", "ground", "normal"]}], "exclude": [{"filterType": "type", "values": ["rock"]}, {"filterType": "tag", "values": ["mega"]}]}, {"name": "marsh", "title": "Marsh Cup", "link": "https://silph.gg/factions/cycle/liga711-torneo3", "include": [{"filterType": "type", "values": ["poison", "ghost", "bug", "grass", "water"]}], "exclude": [{"filterType": "id", "values": ["abomasnow", "abomasnow_shadow", "tropius"]}, {"filterType": "tag", "values": ["mega"]}]}, {"name": "uber", "title": "Uber Tier Cup", "link": "https://twitter.com/PoGoKieng/status/1321532380407103493", "include": [], "exclude": []}, {"name": "little", "title": "Little Cup", "include": [{"filterType": "id", "includeShadows": 1, "values": ["abra", "aipom", "anorith", "archen", "aron", "axew", "azurill", "bagon", "baltoy", "barboach", "beldum", "bellsprout", "bidoof", "blitzle", "bonsly", "bron<PERSON>", "budew", "buizel", "bulbasaur", "buneary", "burmy", "cacnea", "<PERSON>van<PERSON>", "caterpie", "charmander", "cherubi", "chikorita", "chim<PERSON>r", "chinchou", "chingling", "cleffa", "combee", "corphish", "cranidos", "croagunk", "cubchoo", "cubone", "cynda<PERSON><PERSON>", "darum<PERSON>", "darum<PERSON>_galarian", "deerling", "deino", "<PERSON><PERSON>", "diglett_alolan", "doduo", "dratini", "drifloon", "drilbur", "drowzee", "<PERSON><PERSON>", "duskull", "dwebble", "ekans", "electrike", "elekid", "<PERSON><PERSON><PERSON>", "exeggcute", "farfetchd_galarian", "feebas", "ferroseed", "finneon", "foongus", "frillish", "gastly", "geodude", "geodude_alolan", "gible", "glam<PERSON>w", "gligar", "goldeen", "go<PERSON>", "gothita", "grimer", "grimer_alolan", "growlithe", "gulpin", "happiny", "hippopotas", "hoothoot", "hoppip", "<PERSON>a", "houndour", "igglybuff", "<PERSON><PERSON>", "kabuto", "klink", "koffing", "krabby", "kricketot", "larve<PERSON>", "<PERSON>r<PERSON><PERSON>", "ledyba", "lickitung", "lileep", "lillipup", "litwick", "lotad", "machop", "magby", "magi<PERSON><PERSON>", "magnemite", "<PERSON><PERSON><PERSON>", "mankey", "mantyke", "mareep", "meditite", "meowth", "meowth_alolan", "meowth_galarian", "mienfoo", "mime jr.", "misdreavus", "mudkip", "munchlax", "munna", "murkrow", "natu", "nidoran_male", "nidoran_female", "nincada", "nosepass", "numel", "oddish", "omanyte", "<PERSON><PERSON><PERSON>", "pansage", "pansear", "paras", "patrat", "phanpy", "pichu", "pidgey", "pidove", "pineco", "pip<PERSON>p", "poliwag", "ponyta", "ponyta", "poochyena", "psyduck", "purrloin", "ralts", "rattata", "rattata_alolan", "remoraid", "rhyhorn", "r<PERSON><PERSON>", "rog<PERSON><PERSON><PERSON>", "rufflet", "sandile", "sandshrew", "sandshrew_alolan", "scraggy", "seedot", "seel", "sentret", "sewaddle", "shellos_east_sea", "shellos_west_sea", "shieldon", "shinx", "shroomish", "shuppet", "skitty", "s<PERSON><PERSON><PERSON>", "slakoth", "slowpoke", "", "slugma", "smoochum", "sneasel", "snivy", "snorunt", "snover", "snubbull", "solosis", "spearow", "spheal", "spinarak", "spoink", "squirtle", "starly", "stunky", "surskit", "swablu", "swin<PERSON>", "taillow", "tangela", "te<PERSON><PERSON><PERSON>", "tentacool", "tepig", "timburr", "t<PERSON><PERSON><PERSON>", "toge<PERSON>", "torchic", "totodile", "trapinch", "treecko", "trubbish", "turtwig", "tympole", "tynamo", "tyrogue", "vanillite", "venipede", "venonat", "voltorb", "vulpix", "vulpix_alolan", "wailmer", "weedle", "whismur", "wingull", "woobat", "wooper", "wur<PERSON>", "wynaut", "yamask", "yanma", "zigzagoon", "zigzagoon_galarian", "zubat", "meltan", "yamask_galarian", "vulpix_shadow", "dratini_shadow", "stunky_shadow", "cu<PERSON>_shadow", "gligar_shadow", "mare<PERSON>_shadow", "turtwig_shadow", "growlithe_shadow", "psyduck_shadow", "duskull_shadow", "oddish_shadow", "hoppip_shadow", "houndour_shadow", "venonat_shadow", "squirtle_shadow", "drowzee_shadow", "nidoran_male_shadow", "seedot_shadow", "meowth_shadow", "pineco_shadow", "sandshrew_shadow", "mudkip_shadow", "poliwag_shadow", "machop_shadow", "exeggcute_shadow", "slowpoke_shadow", "magnemite_shadow", "grimer_shadow", "zubat_shadow", "teddiu<PERSON>_shadow", "charmander_shadow", "rattata_shadow", "misdreavus_shadow", "snover_shadow", "bulbusaur_shadow", "ralts_shadow", "larvitar_shadow", "omanyte_shadow", "sneasel_shadow", "koffing_shadow", "car<PERSON><PERSON>_shadow", "bagon_shadow", "dig<PERSON>_shadow", "trapinch_shadow", "cacnea_shadow", "weedle_shadow", "nidoran_female_shadow", "shuppet_shadow", "bellsprout_shadow", "ekans_shadow", "abra_shadow", "beldum_shadow", "magikar<PERSON>_shadow", "shellder", "shellder_shadow", "cottonee", "scyther", "scyther_shadow", "onix", "mime_jr", "eevee", "porygon", "sunkern", "clamperl", "burmy", "shellos", "panpour", "petilil", "minccino", "karrablast", "shelmet", "vullaby", "pawniard", "staryu", "chespin", "fennekin", "froakie", "bunnelby", "fletchling", "litleo", "flabebe", "pancha<PERSON>", "espurr", "spritzee", "swirlix", "<PERSON>ay", "binacle", "skrelp", "clauncher", "helioptile", "goomy", "phantump", "pumpkaboo_small", "pumpkaboo_average", "pumpkaboo_large", "pumpkaboo_super", "bergmite", "noibat", "rowlet", "litten", "popp<PERSON>", "pikipek", "yungoos", "rockruff", "fomantis", "saland<PERSON>", "jang<PERSON>_o", "skwovet", "wooloo", "zigzagoon_galarian", "farfetchd_galarian", "mr_mime_galarian", "yamask_galarian"]}], "exclude": [], "partySize": 3}, {"name": "johto", "title": "Johto Cup", "include": [{"filterType": "dex", "values": [152, 251]}], "exclude": [{"filterType": "tag", "values": ["mega"]}], "partySize": 3, "presetOnly": true}, {"name": "sinnoh", "title": "Sinnoh Cup", "include": [{"filterType": "dex", "values": [387, 493]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["bastiodon_xs"]}], "partySize": 3, "presetOnly": true}, {"name": "nightfall", "title": "Silph Nightfall Cup", "include": [{"filterType": "type", "values": ["ice", "fighting", "normal", "poison", "ground"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["mr_rime", "mr_mime_galarian", "lickitung", "chansey", "pidgeot"]}]}, {"name": "cerberus", "title": "Victory Road Cerberus Cup", "link": "https://silph.gg/t/eh4b/the-three-heads-of-cerberus", "restrictedPicks": 1, "restrictedPokemon": ["sableye", "sableye_xl", "sableye_shadow", "sabley<PERSON>_shadow_xl", "<PERSON><PERSON><PERSON>", "umbreon"], "include": [{"filterType": "type", "values": ["dragon", "ice", "fire", "psychic", "fighting"]}, {"filterType": "id", "values": ["sableye", "sableye_xl", "sableye_shadow", "sabley<PERSON>_shadow_xl", "<PERSON><PERSON><PERSON>", "umbreon"]}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "mega"]}, {"filterType": "id", "values": ["marowak_alolan", "gardevoir_shadow"]}, {"filterType": "type", "values": ["dark"]}]}, {"name": "labyrinth", "title": "Silph <PERSON> Cup", "include": [], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "azumarill_xl", "medicham", "medicham_xl", "stunfisk_galarian", "hypno", "hypno_shadow", "deoxys_defense", "cresselia", "umbreon", "machamp", "machamp_shadow", "scrafty", "sir<PERSON><PERSON><PERSON>", "altaria", "bastiodon", "bastiodon_xl", "primeape", "swampert", "swampert_shadow", "mew", "wobbuffet", "wobbuffet_shadow", "wobbuffet_xl"]}]}, {"name": "vortex", "title": "Silph Vortex Cup", "include": [], "exclude": [{"filterType": "tag", "values": ["mega", "legendary"]}, {"filterType": "type", "values": ["fairy"]}, {"filterType": "id", "values": ["medicham", "medicham_xl", "deoxys_defense", "azumarill_xl", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "sableye_shadow", "sableye", "sableye_xl", "stunfisk_galarian", "swampert", "swampert_shadow", "scrafty", "wobbuffet", "wobbuffet_xl", "lickitung", "lickitung_xl", "machamp", "machamp_shadow", "bastiodon", "bastiodon_xl", "cresselia", "wigglytuff", "abomasnow", "abomasnow_shadow", "<PERSON><PERSON><PERSON><PERSON>", "vigoroth", "tropius", "marowak_alolan", "sir<PERSON><PERSON><PERSON>", "obstagoon", "umbreon", "politoed", "politoed_shadow"]}]}, {"name": "<PERSON><PERSON><PERSON>", "title": "Slitzko Memorial Cup", "include": [{"filterType": "id", "values": ["<PERSON>ras", "steelix", "blaziken", "bastiodon", "lucario", "altaria", "graveler_alolan", "beedrill", "vigoroth", "forretress", "noctowl", "heracross", "charizard", "quagsire", "mantine", "qwilfish", "hypno", "<PERSON><PERSON>", "skuntank", "r<PERSON><PERSON>_alolan", "toxicroak", "<PERSON><PERSON><PERSON><PERSON>", "melmetal", "king<PERSON>", "registeel", "marshtomp", "magneton", "tropius", "ninetales_alolan", "houndoom", "muk", "arbok", "absol", "<PERSON><PERSON><PERSON><PERSON>", "toxicroak", "muk_alolan", "noctowl", "venusaur", "swampert", "wigglytuff", "golbat", "sableye", "probopass", "scrafty", "marowak_alolan", "whiscash", "shiftry", "cloyster", "bronzong", "steelix", "dusclops", "drifb<PERSON>", "cresselia", "medicham", "abomasnow", "stunfisk_galarian", "ferrothorn", "pelipper", "dewgong", "whimsicott", "poliwrath", "pidgeot", "forretress", "munchlax", "<PERSON><PERSON><PERSON>", "politoed", "toxicroak", "froslass", "umbreon", "gliscor", "medicham_xl", "azumarill_xl", "sableye_xl", "bastiodon_xl"]}], "exclude": [], "link": "https://silph.gg/t/5wvm"}, {"name": "prismatic", "title": "Silph Prismatic Cup", "include": [{"filterType": "id", "values": ["ivysaur", "weepinbell", "victreebel", "grimer_alolan", "muk_alolan", "scyther", "bayleef", "meganium", "xatu", "bellossom", "tyranitar", "celebi", "g<PERSON><PERSON>", "sceptile", "dustox", "ludico<PERSON>", "breloom", "roselia", "flygon", "cradily", "grotle", "torterra", "roserade", "wormadam_plant", "bronzong", "yanmega", "leafeon", "<PERSON><PERSON>e", "serperior", "simisage", "whimsicott", "maractus", "gard<PERSON><PERSON>", "golurk", "quilladin", "chesnaught", "beedrill", "r<PERSON><PERSON>", "ninetales", "persian", "ponyta", "rapidash", "hypno", "exeggutor", "electabuzz", "jolteon", "quilava", "typhlosion", "ampha<PERSON>", "sunflora", "girafarig", "dunsparce", "pelipper", "ninjask", "manectric", "plusle", "minun", "lunatone", "<PERSON><PERSON>", "vespiquen", "electivire", "leavanny", "archeops", "raticate_alolan", "snorlax", "murkrow", "sneasel", "houndoom", "ma<PERSON>le", "claydol", "banette", "dusclops", "honch<PERSON><PERSON>", "munchlax", "weavile", "zebstrika", "chandelure", "wartortle", "blastoise", "sandslash_alolan", "nidoqueen", "gloom", "persian_alolan", "tangela", "seadra", "gyarados", "vaporeon", "omanyte", "omastar", "cro<PERSON><PERSON>", "feraligatr", "lanturn", "<PERSON><PERSON><PERSON>", "quagsire", "king<PERSON>", "suicune", "marshtomp", "swellow", "masquerain", "wailmer", "wailord", "castform_rainy", "sealeo", "walrein", "hunt<PERSON>", "metang", "metagross", "regice", "prin<PERSON><PERSON><PERSON>", "empoleon", "luxio", "luxray", "rampardos", "gabite", "lucario", "lumineon", "tangrowth", "glaceon", "<PERSON><PERSON><PERSON>", "simipour", "boldore", "gigalith", "swoobat", "palpitoad", "seismitoad", "carracosta", "cryogonal", "<PERSON><PERSON><PERSON>", "gren<PERSON><PERSON>", "charmeleon", "charizard", "vileplume", "parasect", "kingler", "electrode", "seaking", "magmar", "<PERSON><PERSON>", "a<PERSON><PERSON>", "yanma", "s<PERSON><PERSON>", "magcargo", "octillery", "porygon2", "combusken", "blaziken", "camerupt", "solrock", "crawdaunt", "castform_sunny", "kricketune", "wormadam_trash", "magmortar", "porygon_z", "rotom", "pignite", "emboar", "simisear", "scolipede", "krookodile", "darmanitan_standard", "crustle", "bisharp", "braviary", "heatmor", "braixen", "delphox", "fletchinder", "talonflame", "slowbro", "porygon", "slowking", "corsola", "milotic", "cherrim_sunny", "alomomola", "butterfree", "rapidash_galarian", "dewgong", "togetic", "linoone", "gardevoir", "zangoose", "castform_snowy", "shelgon", "p<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "gallade", "swanna", "em<PERSON>ga", "amoon<PERSON>s", "beartic", "floette", "florges", "me<PERSON><PERSON>", "machoke", "graveler_alolan", "golem_alolan", "magneton", "rhydon", "misdreavus", "steelix", "qwilfish", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>on", "aggron", "arm<PERSON>", "glalie", "relicanth", "purugly", "magnezone", "rhyperior", "probopass", "tranquill", "unfezant", "excadrill", "cinccino", "escavalier", "ferrothorn", "klinklang", "durant", "arbok", "nidoking", "golbat", "crobat", "venomoth", "grimer", "muk", "cloyster", "haunter", "gengar", "weezing", "aerodactyl", "espeon", "forretress", "gligar", "gran<PERSON>", "mantine", "delcatty", "swalot", "drifb<PERSON>", "mismagius", "skuntank", "spiritomb", "drapion", "gliscor", "<PERSON><PERSON><PERSON>", "noivern", "pidgeot", "fearow", "r<PERSON><PERSON>_alolan", "<PERSON><PERSON>o", "arcanine", "<PERSON><PERSON><PERSON>", "graveler", "golem", "marowak", "hitmonchan", "kangaskhan", "pinsir", "kabutops", "furret", "noctowl", "sudowoodo", "ursaring", "piloswine", "hitmontop", "shiftry", "<PERSON><PERSON><PERSON>", "monferno", "infernape", "staraptor", "bibarel", "wormadam_sandy", "floatzel", "hippo<PERSON><PERSON>", "ma<PERSON><PERSON>", "litleo_xl", "litleo", "pyroar", "froslass", "dragonite", "zapdos", "garbodor", "salamence", "lie<PERSON>", "meowstic_female"]}], "exclude": [], "slots": [{"pokemon": ["wartortle", "blastoise", "sandslash_alolan", "nidoqueen", "gloom", "persian_alolan", "tangela", "seadra", "gyarados", "vaporeon", "omanyte", "omastar", "cro<PERSON><PERSON>", "feraligatr", "lanturn", "<PERSON><PERSON><PERSON>", "quagsire", "king<PERSON>", "suicune", "marshtomp", "swellow", "masquerain", "wailmer", "wailord", "castform_rainy", "sealeo", "walrein", "hunt<PERSON>", "metang", "metagross", "regice", "prin<PERSON><PERSON><PERSON>", "empoleon", "luxio", "luxray", "rampardos", "gabite", "lucario", "lumineon", "tangrowth", "glaceon", "<PERSON><PERSON><PERSON>", "simipour", "boldore", "gigalith", "swoobat", "palpitoad", "seismitoad", "carracosta", "cryogonal", "<PERSON><PERSON><PERSON>", "gren<PERSON><PERSON>", "salamence"]}, {"pokemon": ["charmeleon", "charizard", "vileplume", "parasect", "kingler", "electrode", "seaking", "magmar", "<PERSON><PERSON>", "a<PERSON><PERSON>", "yanma", "s<PERSON><PERSON>", "magcargo", "octillery", "porygon2", "combusken", "blaziken", "camerupt", "solrock", "crawdaunt", "castform_sunny", "kricketune", "wormadam_trash", "magmortar", "porygon_z", "rotom", "pignite", "emboar", "simisear", "scolipede", "krookodile", "darmanitan_standard", "crustle", "bisharp", "braviary", "heatmor", "braixen", "delphox", "fletchinder", "talonflame"]}, {"pokemon": ["beedrill", "r<PERSON><PERSON>", "ninetales", "persian", "ponyta", "rapidash", "hypno", "exeggutor", "electabuzz", "jolteon", "quilava", "typhlosion", "ampha<PERSON>", "sunflora", "girafarig", "dunsparce", "pelipper", "ninjask", "manectric", "plusle", "minun", "lunatone", "<PERSON><PERSON>", "vespiquen", "electivire", "leavanny", "archeops", "zapdos", "butterfree", "rapidash_galarian", "dewgong", "togetic", "linoone", "gardevoir", "zangoose", "castform_snowy", "shelgon", "p<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "gallade", "swanna", "em<PERSON>ga", "amoon<PERSON>s", "beartic", "floette", "florges", "me<PERSON><PERSON>", "froslass", "meowstic_female"]}, {"pokemon": ["ivysaur", "weepinbell", "victreebel", "grimer_alolan", "muk_alolan", "scyther", "bayleef", "meganium", "xatu", "bellossom", "tyranitar", "celebi", "g<PERSON><PERSON>", "sceptile", "dustox", "ludico<PERSON>", "breloom", "roselia", "flygon", "cradily", "grotle", "torterra", "roserade", "wormadam_plant", "bronzong", "yanmega", "leafeon", "<PERSON><PERSON>e", "serperior", "simisage", "whimsicott", "maractus", "gard<PERSON><PERSON>", "golurk", "quilladin", "chesnaught", "raticate_alolan", "snorlax", "murkrow", "sneasel", "houndoom", "ma<PERSON>le", "claydol", "banette", "dusclops", "honch<PERSON><PERSON>", "munchlax", "weavile", "zebstrika", "chandelure", "garbodor"]}, {"pokemon": ["pidgeot", "fearow", "r<PERSON><PERSON>_alolan", "<PERSON><PERSON>o", "arcanine", "<PERSON><PERSON><PERSON>", "graveler", "golem", "marowak", "hitmonchan", "kangaskhan", "pinsir", "kabutops", "furret", "noctowl", "sudowoodo", "ursaring", "piloswine", "hitmontop", "shiftry", "<PERSON><PERSON><PERSON>", "monferno", "infernape", "staraptor", "bibarel", "wormadam_sandy", "floatzel", "hippo<PERSON><PERSON>", "ma<PERSON><PERSON>", "litleo", "litleo_xl", "pyroar", "dragonite", "slowbro", "porygon", "slowking", "corsola", "milotic", "cherrim_sunny", "alomomola"]}, {"pokemon": ["arbok", "nidoking", "golbat", "crobat", "venomoth", "grimer", "muk", "cloyster", "haunter", "gengar", "weezing", "aerodactyl", "espeon", "forretress", "gligar", "gran<PERSON>", "mantine", "delcatty", "swalot", "drifb<PERSON>", "mismagius", "skuntank", "spiritomb", "drapion", "gliscor", "<PERSON><PERSON><PERSON>", "noivern", "machoke", "graveler_alolan", "golem_alolan", "magneton", "rhydon", "misdreavus", "steelix", "qwilfish", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>on", "aggron", "arm<PERSON>", "glalie", "relicanth", "purugly", "magnezone", "rhyperior", "probopass", "tranquill", "unfezant", "excadrill", "cinccino", "escavalier", "ferrothorn", "klinklang", "durant", "lie<PERSON>"]}]}, {"name": "commander", "title": "<PERSON><PERSON><PERSON>", "restrictedPicks": 1, "restrictedPokemon": ["munchlax", "shiftry", "escavalier", "steelix", "empoleon", "jellicent", "bronzong", "noctowl", "marowak_alolan", "poliwrath"], "include": [{"filterType": "id", "values": ["munchlax", "shiftry", "escavalier", "steelix", "empoleon", "jellicent", "bronzong", "noctowl", "marowak_alolan", "poliwrath"]}, {"filterType": "type", "values": ["rock", "ice", "electric", "poison", "psychic"]}], "exclude": [{"filterType": "tag", "values": ["mega", "legendary", "mythical", "shadow"]}, {"filterType": "type", "values": ["steel"]}, {"filterType": "id", "values": ["abomasnow", "<PERSON><PERSON><PERSON>", "regirock", "stunfisk", "medicham", "wobbuffet", "drapion", "p<PERSON><PERSON><PERSON>"]}]}, {"name": "factions", "title": "Silph Factions", "include": [{"filterType": "type", "values": ["bug", "dark", "dragon", "flying", "rock"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["bastiodon", "probopass", "regirock", "scrafty", "<PERSON><PERSON><PERSON><PERSON>", "heracross", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "mantine", "hakamo_o", "kommo_o", "lycanroc_midnight", "lycanroc_midday", "incineroar", "dart<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trumbeak", "rowlet", "oricorio_baile", "oricorio_pau", "oricorio_pom_pom", "oricorio_sensu"]}]}, {"name": "floatingcity", "title": "Floating City Field", "include": [{"filterType": "type", "values": ["ground", "flying", "steel", "normal"]}], "exclude": [{"filterType": "tag", "values": ["mega", "mythical", "shadow", "regional"]}, {"filterType": "id", "values": ["stunfisk_galarian", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "bastiodon", "registeel", "chansey", "pidgeot", "salazzle"]}], "partySize": 8}, {"name": "dungeon", "title": "Dungeon Field", "include": [{"filterType": "type", "values": ["bug", "dragon", "poison", "steel", "water"]}], "exclude": [{"filterType": "tag", "values": ["mega", "shadow"]}, {"filterType": "id", "values": ["<PERSON><PERSON><PERSON><PERSON>", "stunfisk_galarian", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "bastiodon", "salazzle"]}], "partySize": 8}, {"name": "comet", "title": "Comet Cup", "include": [{"filterType": "type", "values": ["fire", "flying", "ice", "normal", "psychic"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["altaria", "deoxys_defense", "medicham", "wobbuffet", "chansey", "mantine"]}], "link": "https://discord.gg/ybTf7ZYNzt?event=925020319037669387"}, {"name": "cometultra", "title": "Comet Cup", "include": [{"filterType": "type", "values": ["fire", "flying", "ice", "normal", "psychic", "electric"]}], "exclude": [{"filterType": "tag", "values": ["mega", "mythical"]}, {"filterType": "id", "values": ["salazzle"]}], "partySize": 8}, {"name": "venture", "title": "Venture Cup", "include": [], "exclude": [{"filterType": "type", "values": ["dark", "fairy", "steel"]}, {"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["chansey", "pidgeot"]}], "tierRules": {"max": 20, "floor": 1, "tiers": [{"points": 10, "pokemon": ["hypno", "hypno_shadow", "lickitung", "altaria", "froslass", "cresselia", "marowak_alolan"]}, {"points": 6, "pokemon": ["dewgong", "<PERSON>ras", "snorlax", "dragonair", "politoed", "king<PERSON>", "vigoroth", "medicham", "regirock", "deoxys_defense", "munchlax", "lickilicky", "jellicent", "<PERSON><PERSON><PERSON>", "talonflame", "dragalge", "lugia"]}, {"points": 4, "pokemon": ["machamp", "haunter", "gengar", "dragonite", "mew", "lanturn", "wobbuffet", "mantine", "swampert", "pelipper", "zangoose", "whiscash", "cradily", "dusclops", "tropius", "sealeo", "p<PERSON><PERSON><PERSON>", "drifb<PERSON>", "abomasnow", "crustle", "stunfisk", "goodra", "graveler_alolan", "sir<PERSON><PERSON><PERSON>", "nidoqueen", "flygon"]}]}}, {"name": "bidoof", "title": "Bidoof Cup", "include": [{"filterType": "id", "values": ["bidoof", "bidoof_shadow"]}], "exclude": [], "partySize": 3}, {"name": "continentals-3", "title": "Silph Season 3 Continentals", "include": [], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["pidgeot", "chansey"]}], "tierRules": {"max": 20, "floor": 1, "tiers": [{"points": 10, "pokemon": ["lickitung", "mew", "hypno", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "umbreon", "registeel", "altaria", "sableye", "medicham", "deoxys_defense", "abomasnow", "bastiodon", "cresselia", "jellicent", "mandibuzz", "stunfisk_galarian", "marowak_alolan", "talonflame", "ninetales_alolan", "diggersby"]}, {"points": 6, "pokemon": ["clefable", "nidoqueen", "machamp", "venusaur", "dewgong", "wigglytuff", "meganium", "<PERSON><PERSON><PERSON>", "politoed", "gran<PERSON>", "lugia", "wobbuffet", "swampert", "tropius", "vigoroth", "gardevoir", "regirock", "<PERSON><PERSON><PERSON><PERSON>", "drifb<PERSON>", "froslass", "toxicroak", "munchlax", "lickilicky", "ferrothorn", "<PERSON><PERSON><PERSON>", "whimsicott", "stunfisk", "scrafty", "<PERSON><PERSON><PERSON>", "aromatisse", "spritzee", "r<PERSON><PERSON>_alolan", "melmetal", "sylveon", "slurpuff", "muk_alolan"]}, {"points": 4, "pokemon": ["golbat", "articuno", "beedrill", "zapdos", "snorlax", "seaking", "primeape", "<PERSON>ras", "quagsire", "noctowl", "a<PERSON><PERSON>", "lanturn", "bellossom", "rai<PERSON>u", "mantine", "steelix", "castform_rainy", "castform_snowy", "castform", "castform_sunny", "regice", "whiscash", "shiftry", "pelipper", "skuntank", "gliscor", "electivire", "p<PERSON><PERSON><PERSON>", "roserade", "empoleon", "magnezone", "drapion", "zebstrika", "serperior", "fraxure", "alomomola", "excadrill", "escavalier", "victini", "crustle", "chesnaught", "dragalge", "obstagoon", "sir<PERSON><PERSON><PERSON>", "pangoro", "goodra"]}]}}, {"name": "cutie", "title": "Cutie Cup", "link": "https://silph.gg/t/rk55/regicide-charity-tournament", "include": [{"filterType": "type", "name": "Type", "values": ["bug", "dragon", "electric", "fairy", "fire", "flying", "grass", "ice", "normal", "psychic", "water"]}, {"filterType": "id", "name": "Species", "values": ["<PERSON><PERSON><PERSON>", "froslass", "wormadam_trash", "<PERSON>rserker", "magneton", "ma<PERSON>le", "magcargo", "piloswine", "drifb<PERSON>"]}], "exclude": [{"filterType": "id", "name": "Species", "values": ["vigoroth", "deoxys_defense", "hypno", "venusaur", "abomasnow", "haxorus", "<PERSON><PERSON><PERSON><PERSON>", "altaria", "cresselia", "chansey", "lickitung", "pidgeot"]}, {"filterType": "type", "name": "Type", "values": ["dark", "fighting", "ghost", "ground", "rock", "steel"]}, {"filterType": "tag", "name": "Tag", "values": ["shadow", "mega"]}]}, {"name": "worlds", "title": "<PERSON><PERSON><PERSON>", "include": [], "exclude": [{"filterType": "id", "values": ["venusaur", "lickitung", "chansey", "<PERSON><PERSON><PERSON><PERSON>", "umbreon", "wobbuffet", "<PERSON><PERSON><PERSON><PERSON>", "sableye", "medicham", "altaria", "jellicent", "ninetales_alolan", "stunfisk_galarian"]}, {"filterType": "tag", "values": ["mega"]}]}, {"name": "lunar", "title": "Lunar Cup", "include": [{"filterType": "type", "values": ["bug", "dark", "electric", "ghost", "grass"]}], "exclude": [{"filterType": "id", "values": ["scrafty", "p<PERSON><PERSON><PERSON>", "stunfisk", "marowak_alolan", "whimsicott", "vullaby"]}, {"filterType": "tag", "values": ["mega", "shadow"]}]}, {"name": "brawler", "title": "Brawler Cup", "include": [{"filterType": "id", "includeShadows": 1, "values": ["haunter", "uxie", "mesprit", "<PERSON><PERSON><PERSON>", "spritzee", "castform", "castform_sunny", "castform_rainy", "castform_snowy", "misdreavus", "chimecho", "<PERSON><PERSON><PERSON>", "drifloon", "chatot", "roselia", "murkrow", "exeggcute", "<PERSON><PERSON><PERSON>", "klefki", "espurr", "masquerain", "qwilfish", "victini", "plusle", "minun", "mismagius", "corsola", "celebi", "spinda", "em<PERSON>ga", "slurpuff", "bellossom", "magnemite", "greedent", "weepinbell", "wormadam_plant", "wormadam_sand", "wormadam_trash", "whimsicott", "swadloon", "omanyte", "pancha<PERSON>", "meltan", "cinccino", "sunflora", "drilbur", "me<PERSON><PERSON>", "meowstic_female", "gloom", "te<PERSON><PERSON><PERSON>", "hoopa", "cherrim_overcast", "cherrim_sunny", "weezing", "archen", "pawniard", "swoobat", "amoon<PERSON>s", "rufflet", "<PERSON><PERSON><PERSON>", "sableye", "stunfisk", "ma<PERSON>le", "wigglytuff", "ninjask", "venomoth", "banette", "ivysaur", "lampent", "litleo", "dunsparce", "<PERSON><PERSON><PERSON>", "roserade", "dwebble", "braixen", "farfetch’d", "xatu", "drifb<PERSON>", "bonsly", "tranquill", "vibrava", "victreebel", "aromatisse", "bayleef", "weezing_galarian", "<PERSON><PERSON>e", "lilligant", "dragonair", "t<PERSON><PERSON><PERSON>", "palpitoad", "sliggoo", "volbeat", "illumise", "basculin", "raticate", "vileplume", "charmeleon", "quilava", "<PERSON><PERSON><PERSON>", "machop", "combusken", "swellow", "ninetales", "nidorina", "poliwhirl", "reuniclus", "geodude_alolan", "ambipom", "leavanny", "r<PERSON><PERSON>_alolan", "g<PERSON><PERSON>", "monferno", "eelektrik", "wartortle", "lanturn", "gorebyss", "prin<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "relicanth", "sylveon", "whiscash", "lumineon", "swanna", "jolteon", "<PERSON><PERSON><PERSON>", "talonflame", "staraptor", "flareon", "cro<PERSON><PERSON>", "kricketune", "leafeon", "raticate_alolan", "glaceon", "froslass", "honch<PERSON><PERSON>", "marshtomp", "pelipper", "furfrou", "<PERSON>rserker", "vaporeon", "unfezant", "quilladin", "beedrill", "gastrodon", "gastrodon", "r<PERSON><PERSON>", "grimer", "muk", "electabuzz", "dusclops", "bibarel", "dustox", "alomomola", "primeape", "furret", "porygon2", "linoone", "linoone_galarian", "crawdaunt", "escavalier", "durant", "a<PERSON><PERSON>", "politoed", "marowak_alolan", "weavile", "chandelure", "omastar", "houndoom", "<PERSON><PERSON>", "fraxure", "sudowoodo", "skuntank", "<PERSON><PERSON><PERSON><PERSON>", "vespiquen", "seaking", "delphox", "breloom", "pidgeot", "sandshrew_alolan", "clefable", "gren<PERSON><PERSON>", "manectric", "zangoose", "excadrill", "gengar", "noctowl", "grimer_alolan", "luxray", "diggersby", "gliscor", "purugly", "<PERSON><PERSON><PERSON>", "toxicroak", "magmar", "obstagoon", "vigoroth", "malamar", "<PERSON><PERSON><PERSON>", "hitmontop", "gardevoir", "gran<PERSON>", "<PERSON><PERSON><PERSON>", "hitmonchan", "muk_alolan", "blaziken", "gallade", "sceptile", "seviper", "zapdos", "poliwrath", "heracross", "lucario", "sandslash_alolan", "golbat", "tentacruel", "pinsir", "magcargo", "ludico<PERSON>", "infernape", "articuno", "pignite", "piloswine", "heatmor", "aerodactyl", "shiftry", "magneton", "kingler", "cradily", "ampha<PERSON>", "drapion", "nidoking", "seismitoad", "falinks", "serperior", "gligar", "electrode", "magmortar", "bisharp", "machoke", "slowbro_galarian", "crobat", "quagsire", "cofagrigus", "golduck", "slowbro", "typhlosion", "slowking", "zebstrika", "swalot", "rapidash_galarian", "eelektross", "carracosta", "pyroar", "dragalge", "flygon", "empoleon", "blastoise", "sealeo", "feraligatr", "sharpedo", "chesnaught", "charizard", "darmanitan_standard", "samu<PERSON>t", "rapidash", "garcho<PERSON>", "barbara<PERSON>", "krookodile", "grotle", "venusaur", "tropius", "meganium", "boldore", "rampardos", "salamence", "graveler", "munchlax", "haxorus", "dusknoir", "garbodor", "claydol", "spiritomb", "graveler_alolan", "ferrothorn", "shelgon", "rhyhorn", "sir<PERSON><PERSON><PERSON>", "s<PERSON><PERSON>", "dewgong", "exeggutor", "rhydon", "<PERSON><PERSON><PERSON>", "<PERSON>on", "darmanitan_galarian_standard", "mewtwo", "forretress", "ursaring", "tangrowth", "machamp", "wailmer", "slaking", "cloyster", "abomasnow", "pangoro", "electivire", "lickilicky", "cryogonal", "emboar", "goodra", "walrein", "king<PERSON>", "solrock", "arcanine", "hydreigon", "milotic", "lunatone", "regice", "rai<PERSON>u", "magnezone", "suicune", "bronzong", "entei", "ho_oh", "crustle", "scolipede", "tyranitar", "metang", "<PERSON><PERSON><PERSON>", "onix", "dragonite", "lugia", "<PERSON>ras", "mantine", "camerupt", "gyarados", "<PERSON><PERSON><PERSON>", "glalie", "gigalith", "beartic", "rhyperior", "ma<PERSON><PERSON>", "golem", "hippo<PERSON><PERSON>", "torterra", "golem_alolan", "golurk", "probopass", "aggron", "wailord", "steelix", "exeggutor_alolan", "snorlax", "metagross", "melmetal"]}], "exclude": [], "slots": [{"pokemon": ["haunter", "uxie", "mesprit", "<PERSON><PERSON><PERSON>", "spritzee", "castform", "castform_sunny", "castform_rainy", "castform_snowy", "misdreavus", "chimecho", "<PERSON><PERSON><PERSON>", "drifloon", "chatot", "roselia", "murkrow", "exeggcute", "<PERSON><PERSON><PERSON>", "klefki", "espurr", "masquerain", "qwilfish", "victini", "plusle", "minun", "mismagius", "corsola", "celebi", "spinda", "em<PERSON>ga", "slurpuff", "bellossom", "magnemite", "greedent", "weepinbell", "wormadam_plant", "wormadam_sand", "wormadam_trash", "whimsicott", "swadloon", "omanyte", "pancha<PERSON>", "meltan", "cinccino", "sunflora", "drilbur", "me<PERSON><PERSON>", "meowstic_female", "gloom", "te<PERSON><PERSON><PERSON>", "hoopa", "cherrim", "weezing", "archen", "cherrim_overcast", "cherrim_sunny"]}, {"pokemon": ["pawniard", "swoobat", "amoon<PERSON>s", "rufflet", "<PERSON><PERSON><PERSON>", "sableye", "sableye_xs", "stunfisk", "ma<PERSON>le", "wigglytuff", "ninjask", "venomoth", "banette", "ivysaur", "lampent", "litleo", "dunsparce", "<PERSON><PERSON><PERSON>", "roserade", "dwebble", "braixen", "farfetch’d", "xatu", "drifb<PERSON>", "bonsly", "tranquill", "vibrava", "victreebel", "aromatisse", "bayleef", "weezing_galarian", "<PERSON><PERSON>e", "lilligant", "dragonair", "t<PERSON><PERSON><PERSON>", "palpitoad", "sliggoo", "volbeat", "illumise", "basculin", "raticate", "vileplume", "charmeleon", "quilava", "<PERSON><PERSON><PERSON>", "machop", "combusken", "swellow", "ninetales", "nidorina", "poliwhirl", "reuniclus", "geodude_alolan", "ambipom", "leavanny", "r<PERSON><PERSON>_alolan", "g<PERSON><PERSON>", "monferno", "eelektrik", "wartortle", "lanturn", "gorebyss", "prin<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "relicanth", "sylveon", "whiscash", "lumineon", "swanna", "jolteon", "<PERSON><PERSON><PERSON>", "talonflame", "staraptor"]}, {"pokemon": ["flareon", "cro<PERSON><PERSON>", "kricketune", "leafeon", "raticate_alolan", "glaceon", "froslass", "honch<PERSON><PERSON>", "marshtomp", "pelipper", "furfrou", "<PERSON>rserker", "vaporeon", "unfezant", "quilladin", "beedrill", "gastrodon", "gastrodon", "r<PERSON><PERSON>", "grimer", "muk", "electabuzz", "dusclops", "bibarel", "dustox", "alomomola", "primeape", "furret", "porygon2", "linoone", "linoone_galarian", "crawdaunt", "escavalier", "durant", "a<PERSON><PERSON>", "politoed", "marowak_alolan", "weavile", "chandelure", "omastar", "houndoom", "<PERSON><PERSON>", "fraxure", "sudowoodo", "skuntank", "<PERSON><PERSON><PERSON><PERSON>", "vespiquen", "seaking", "delphox", "breloom", "pidgeot", "sandshrew_alolan", "clefable", "gren<PERSON><PERSON>", "manectric", "zangoose", "excadrill", "gengar", "noctowl", "grimer_alolan", "luxray", "diggersby", "gliscor", "purugly", "<PERSON><PERSON><PERSON>", "toxicroak", "magmar", "obstagoon", "vigoroth", "malamar", "<PERSON><PERSON><PERSON>", "hitmontop", "gardevoir", "gran<PERSON>"]}, {"pokemon": ["<PERSON><PERSON><PERSON>", "hitmonchan", "muk_alolan", "blaziken", "gallade", "sceptile", "seviper", "zapdos", "poliwrath", "heracross", "lucario", "sandslash_alolan", "golbat", "tentacruel", "pinsir", "magcargo", "ludico<PERSON>", "infernape", "articuno", "pignite", "piloswine", "heatmor", "aerodactyl", "shiftry", "magneton", "kingler", "cradily", "ampha<PERSON>", "drapion", "nidoking", "seismitoad", "falinks", "serperior", "gligar", "electrode", "magmortar", "bisharp", "machoke", "slowbro_galarian", "crobat", "quagsire", "cofagrigus", "golduck", "slowbro", "typhlosion", "slowking", "zebstrika", "swalot", "rapidash_galarian", "eelektross", "carracosta", "pyroar", "dragalge", "flygon", "empoleon", "blastoise", "sealeo", "feraligatr", "sharpedo", "chesnaught", "charizard", "darmanitan_standard", "samu<PERSON>t", "rapidash", "garcho<PERSON>", "barbara<PERSON>", "krookodile", "grotle"]}, {"pokemon": ["venusaur", "tropius", "meganium", "boldore", "rampardos", "salamence", "graveler", "munchlax", "haxorus", "dusknoir", "garbodor", "claydol", "spiritomb", "graveler_alolan", "ferrothorn", "shelgon", "rhyhorn", "sir<PERSON><PERSON><PERSON>", "s<PERSON><PERSON>", "dewgong", "exeggutor", "rhydon", "<PERSON><PERSON><PERSON>", "<PERSON>on", "darmanitan_galarian_standard", "mewtwo", "forretress", "ursaring", "tangrowth", "machamp", "wailmer", "slaking", "cloyster", "abomasnow", "pangoro", "electivire", "lickilicky", "cryogonal", "emboar", "goodra", "walrein", "king<PERSON>", "solrock", "arcanine", "hydreigon", "milotic", "lunatone", "regice", "rai<PERSON>u", "magnezone", "suicune", "bronzong", "entei", "ho_oh"]}, {"pokemon": ["crustle", "scolipede", "tyranitar", "metang", "<PERSON><PERSON><PERSON>", "onix", "dragonite", "lugia", "<PERSON>ras", "mantine", "camerupt", "gyarados", "<PERSON><PERSON><PERSON>", "glalie", "gigalith", "beartic", "rhyperior", "ma<PERSON><PERSON>", "golem", "hippo<PERSON><PERSON>", "torterra", "golem_alolan", "golurk", "probopass", "aggron", "wailord", "steelix", "exeggutor_alolan", "snorlax", "metagross", "melmetal"]}]}, {"name": "unity", "title": "Unity Cup", "include": [{"filterType": "id", "includeShadows": 0, "values": ["ivysaur", "venusaur", "charizard", "blastoise", "beedrill", "pidgeot", "nidoking", "golbat", "tentacruel", "rapidash", "magneton", "dewgong", "muk", "gyarados", "<PERSON>ras", "snorlax", "dragonair", "dragonite", "meganium", "typhlosion", "feraligatr", "noctowl", "lanturn", "ampha<PERSON>", "sudowoodo", "politoed", "<PERSON><PERSON><PERSON>", "quagsire", "forretress", "magcargo", "piloswine", "mantine", "king<PERSON>", "hitmontop", "tyranitar", "sceptile", "blaziken", "swampert", "ludico<PERSON>", "pelipper", "vigoroth", "flygon", "whiscash", "cradily", "milotic", "sealeo", "walrein", "shelgon", "salamence", "grotle", "torterra", "infernape", "empoleon", "bibarel", "roserade", "garcho<PERSON>", "lucario", "hippo<PERSON><PERSON>", "abomasnow", "magnezone", "lickilicky", "rhyperior", "tangrowth", "electivire", "magmortar", "ma<PERSON><PERSON>", "probopass", "serperior", "emboar", "samu<PERSON>t", "zebstrika", "excadrill", "seismitoad", "scolipede", "crustle", "garbodor", "escavalier", "alomomola", "<PERSON><PERSON><PERSON>", "ferrothorn", "eelektross", "chesnaught", "delphox", "gren<PERSON><PERSON>", "talonflame", "pyroar", "barbara<PERSON>", "clawitzer", "dedenne", "goodra", "noivern", "r<PERSON><PERSON>_alolan", "sandslash_alolan", "melmetal", "<PERSON>rserker", "greedent", "dubwool"]}], "exclude": [], "slots": [{"pokemon": ["ivysaur", "venusaur", "charizard", "blastoise", "beedrill", "pidgeot", "nidoking", "golbat", "tentacruel", "rapidash", "magneton", "dewgong", "muk", "gyarados", "<PERSON>ras", "snorlax", "dragonair", "dragonite"]}, {"pokemon": ["meganium", "typhlosion", "feraligatr", "noctowl", "lanturn", "ampha<PERSON>", "sudowoodo", "politoed", "<PERSON><PERSON><PERSON>", "quagsire", "forretress", "magcargo", "piloswine", "mantine", "king<PERSON>", "hitmontop", "tyranitar"]}, {"pokemon": ["sceptile", "blaziken", "swampert", "ludico<PERSON>", "pelipper", "vigoroth", "flygon", "whiscash", "cradily", "milotic", "sealeo", "walrein", "shelgon", "salamence"]}, {"pokemon": ["grotle", "torterra", "infernape", "empoleon", "bibarel", "roserade", "garcho<PERSON>", "lucario", "hippo<PERSON><PERSON>", "abomasnow", "magnezone", "lickilicky", "rhyperior", "tangrowth", "electivire", "magmortar", "ma<PERSON><PERSON>", "probopass"]}, {"pokemon": ["serperior", "emboar", "samu<PERSON>t", "zebstrika", "excadrill", "seismitoad", "scolipede", "crustle", "garbodor", "escavalier", "alomomola", "<PERSON><PERSON><PERSON>", "ferrothorn", "eelektross"]}, {"pokemon": ["chesnaught", "delphox", "gren<PERSON><PERSON>", "talonflame", "pyroar", "barbara<PERSON>", "clawitzer", "dedenne", "goodra", "noivern", "r<PERSON><PERSON>_alolan", "sandslash_alolan", "melmetal", "<PERSON>rserker", "greedent", "dubwool"]}], "link": "https://twitter.com/SkyDragonsGami1/status/1465726406059569156"}, {"name": "glacial", "title": "Glacial Cup", "include": [{"filterType": "type", "values": ["dragon", "ice", "poison", "psychic", "water"]}], "exclude": [{"filterType": "tag", "values": ["mega"]}, {"filterType": "id", "values": ["altaria", "<PERSON><PERSON><PERSON><PERSON>", "jellicent", "swampert", "deoxys_defense", "wobbuffet", "cresselia", "medicham", "ninetales_alolan", "sandshrew_alolan", "lanturn", "gardevoir", "toxicroak"]}]}, {"name": "guardian", "title": "Guardian Cup", "include": [{"filterType": "moveType", "name": "Move Type", "values": ["electric", "fighting", "flying", "psychic", "fire"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["legendary", "mythical", "shadow", "mega"]}, {"filterType": "type", "name": "Type", "values": ["fairy"]}, {"filterType": "id", "name": "Species", "values": ["lickitung", "medicham", "altaria", "p<PERSON><PERSON><PERSON>", "wobbuffet", "stunfisk", "umbreon", "mandibuzz", "scrafty", "vullaby", "chansey", "hypno", "obstagoon", "cofagrigus", "bastiodon", "diggersby", "tropius", "lanturn"]}]}, {"name": "obsidian", "title": "Obsidian Cup", "include": [], "exclude": [{"filterType": "type", "name": "Type", "values": ["fairy", "fighting", "flying", "grass", "ground"]}, {"filterType": "id", "name": "Species", "values": ["lickitung", "bastiodon", "p<PERSON><PERSON><PERSON>", "vigoroth", "registeel", "hakamo_o", "kommo_o", "lycanroc_midnight", "lycanroc_midday", "incineroar", "dart<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trumbeak", "rowlet", "brionne", "torracat", "popp<PERSON>", "gumshoos", "oricorio_baile", "oricorio_pau", "oricorio_pom_pom", "oricorio_sensu"]}, {"filterType": "tag", "name": "Tag", "values": ["mega", "shadow"]}]}, {"name": "nemesis", "title": "Nemesis Cup", "restrictedPicks": 2, "restrictedPokemon": ["obstagoon", "gallade", "lucario", "poliwrath", "pangoro", "blaziken", "chesnaught", "quagsire", "flygon", "excadrill", "<PERSON><PERSON><PERSON>", "dedenne", "froslass", "gourgeist_small", "gourgeist_average", "gourgeist_large", "gourgeist_super", "noctowl", "gyarados", "charizard", "gengar", "rapidash_galarian", "ma<PERSON>le"], "include": [{"filterType": "id", "includeShadows": true, "values": ["obstagoon", "gallade", "lucario", "poliwrath", "pangoro", "blaziken", "chesnaught", "quagsire", "flygon", "excadrill", "<PERSON><PERSON><PERSON>", "dedenne", "froslass", "gourgeist_small", "gourgeist_average", "gourgeist_large", "gourgeist_super", "noctowl", "gyarados", "charizard", "gengar", "rapidash_galarian", "ma<PERSON>le"]}, {"filterType": "type", "values": ["bug", "dark", "fire", "grass", "ice", "psychic", "water"]}], "exclude": [{"filterType": "id", "values": ["escavalier", "salazzle"]}, {"filterType": "type", "values": ["none", "fairy", "flying", "fighting", "ground", "ghost", "normal"]}, {"filterType": "tag", "values": ["mega"]}]}, {"name": "firefly", "title": "Firefly Cup", "include": [{"filterType": "type", "values": ["normal", "electric", "bug", "poison", "ice"]}], "exclude": [{"filterType": "type", "values": ["ground"]}, {"filterType": "id", "values": ["escavalier", "salazzle", "walrein", "dragalge", "p<PERSON><PERSON><PERSON>"]}, {"filterType": "tag", "values": ["mega"]}]}]