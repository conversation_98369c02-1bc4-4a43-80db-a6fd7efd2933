{"name": "timelessfactions", "title": "<PERSON><PERSON><PERSON> (Timeless)", "restrictedPicks": 1, "restrictedPokemon": ["bulbasaur", "ivysaur", "venusaur", "squirtle", "wartortle", "blastoise", "charmander", "charmeleon", "charizard", "chikorita", "bayleef", "meganium", "totodile", "cro<PERSON><PERSON>", "feraligatr", "cynda<PERSON><PERSON>", "quilava", "typhlosion", "treecko", "g<PERSON><PERSON>", "sceptile", "mudkip", "marshtomp", "swampert", "torchic", "combusken", "blaziken", "turtwig", "grotle", "torterra", "pip<PERSON>p", "prin<PERSON><PERSON><PERSON>", "empoleon", "chim<PERSON>r", "monferno", "infernape", "snivy", "<PERSON><PERSON>e", "serperior", "tepig", "pignite", "emboar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "samu<PERSON>t"], "include": [{"filterType": "dex", "values": [1, 649]}, {"filterType": "id", "values": ["blaziken", "charizard", "infernape", "empoleon", "monferno", "prin<PERSON><PERSON><PERSON>", "combusken", "snivy", "<PERSON><PERSON>e", "serperior", "tepig", "pignite", "emboar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "samu<PERSON>t"], "includeShadows": false}], "exclude": [{"filterType": "tag", "values": ["legendary", "mythical", "shadow", "mega", "alolan", "<PERSON><PERSON><PERSON>", "gal<PERSON>"]}, {"filterType": "type", "values": ["dragon", "fairy", "fighting", "flying", "normal", "psychic", "steel"]}, {"filterType": "id", "values": ["umbreon", "sableye", "drapion", "nidoqueen", "walrein", "p<PERSON><PERSON><PERSON>", "lileep"]}]}