import { useBattleStore } from '@/stores/battleStore';

export function BattleModeSelector() {
  const { battleMode, setBattleMode } = useBattleStore();
  
  return (
    <div className="ranking-categories mode-select">
      {[
        { id: 'single', label: 'Single' },
        { id: 'multi', label: 'Multi' },
        { id: 'matrix', label: 'Matrix' }
      ].map((mode) => (
        <a
          key={mode.id}
          href="#"
          data-mode={mode.id}
          className={battleMode === mode.id ? 'selected' : ''}
          onClick={(e) => {
            e.preventDefault();
            setBattleMode(mode.id as 'single' | 'multi' | 'matrix');
          }}
        >
          {mode.label}
        </a>
      ))}
    </div>
  );
}