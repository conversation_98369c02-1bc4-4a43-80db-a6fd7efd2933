import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { BattlePokemon, League } from '@/types/pokemon';
import type { BattleResult, BattleOptions, SandboxAction } from '@/types/battle';
import type { MultiBattleResult, MatrixBattleResult } from '@/types/multiMatrix';

interface BattleState {
  // State
  pokemon1: BattlePokemon | null;
  pokemon2: BattlePokemon | null;
  league: League;
  battleResult: BattleResult | null;
  isSimulating: boolean;
  battleOptions: BattleOptions;
  error: string | null;
  battleMode: 'single' | 'multi' | 'matrix';
  pokemonTeam1: BattlePokemon[]; // For multi/matrix mode
  pokemonTeam2: BattlePokemon[]; // For multi/matrix mode
  multiBattleResults: MultiBattleResult[] | null;
  matrixBattleResults: MatrixBattleResult | null;
  selectedMatchup: string | null; // For viewing specific matchups in multi/matrix
  
  // Sandbox mode
  sandboxMode: boolean;
  sandboxActions: SandboxAction[];
  
  // Timeline playback
  timelineScale: 'fit' | 'zoom';
  playbackSpeed: 1 | 4 | 8 | 16;
  isPlaying: boolean;
  currentTurn: number;

  // Actions
  setPokemon1: (pokemon: BattlePokemon | null) => void;
  setPokemon2: (pokemon: BattlePokemon | null) => void;
  setLeague: (league: League) => void;
  setBattleOptions: (options: Partial<BattleOptions>) => void;
  swapPokemon: () => void;
  simulateBattle: () => Promise<void>;
  resetBattle: () => void;
  clearError: () => void;
  setBattleMode: (mode: 'single' | 'multi' | 'matrix') => void;
  setPokemonTeam1: (team: BattlePokemon[]) => void;
  setPokemonTeam2: (team: BattlePokemon[]) => void;
  
  // Sandbox actions
  toggleSandboxMode: () => void;
  addSandboxAction: (action: SandboxAction) => void;
  removeSandboxAction: (index: number) => void;
  clearSandboxActions: () => void;
  
  // Timeline playback actions
  setTimelineScale: (scale: 'fit' | 'zoom') => void;
  setPlaybackSpeed: (speed: 1 | 4 | 8 | 16) => void;
  playTimeline: () => void;
  pauseTimeline: () => void;
  seekTimeline: (turn: number) => void;
}

const defaultBattleOptions: BattleOptions = {
  shields: {
    pokemon1: 1,
    pokemon2: 1,
  },
  strategy: 'neutral',
  allowSwitching: false,
};

export const useBattleStore = create<BattleState>()(
  devtools(
    (set, get) => ({
      // Initial state
      pokemon1: null,
      pokemon2: null,
      league: 'great',
      battleResult: null,
      isSimulating: false,
      battleOptions: defaultBattleOptions,
      error: null,
      battleMode: 'single',
      pokemonTeam1: [],
      pokemonTeam2: [],
      multiBattleResults: null,
      matrixBattleResults: null,
      selectedMatchup: null,
      
      // Sandbox mode
      sandboxMode: false,
      sandboxActions: [],
      
      // Timeline playback
      timelineScale: 'fit' as 'fit' | 'zoom',
      playbackSpeed: 8 as 1 | 4 | 8 | 16,
      isPlaying: false,
      currentTurn: 0,

      // Actions
      setPokemon1: (pokemon) => {
        set({ pokemon1: pokemon, battleResult: null, error: null });
      },

      setPokemon2: (pokemon) => {
        set({ pokemon2: pokemon, battleResult: null, error: null });
      },

      setLeague: (league) => {
        set({ league, battleResult: null, error: null });
      },

      setBattleOptions: (options) =>
        set((state) => ({
          battleOptions: { ...state.battleOptions, ...options },
          battleResult: null,
          error: null,
        })),

      swapPokemon: () => {
        const { pokemon1, pokemon2 } = get();
        set({
          pokemon1: pokemon2,
          pokemon2: pokemon1,
          battleResult: null,
          error: null,
        });
      },

      simulateBattle: async () => {
        const { pokemon1, pokemon2, battleOptions, battleMode, sandboxMode, sandboxActions } = get();

        if (battleMode !== 'single') {
          // Handle multi/matrix battle modes
          return;
        }

        if (!pokemon1 || !pokemon2) {
          set({ error: 'Both Pokemon must be selected' });
          return;
        }

        set({ isSimulating: true, error: null });

        try {
          // Format request to match API schema
          const requestBody = {
            pokemon1: {
              speciesId: pokemon1.speciesId,
              level: pokemon1.level,
              ivs: pokemon1.ivs,
              fastMove: pokemon1.selectedMoves.fastMove.moveId,
              chargedMoves: pokemon1.selectedMoves.chargedMoves.map(move => move.moveId),
            },
            pokemon2: {
              speciesId: pokemon2.speciesId,
              level: pokemon2.level,
              ivs: pokemon2.ivs,
              fastMove: pokemon2.selectedMoves.fastMove.moveId,
              chargedMoves: pokemon2.selectedMoves.chargedMoves.map(move => move.moveId),
            },
            options: {
              ...battleOptions,
              sandboxMode,
              sandboxActions: sandboxMode ? sandboxActions : undefined,
            },
          };

          const response = await fetch('/api/battle/simulate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          });

          if (!response.ok) {
            throw new Error(`Battle simulation failed: ${response.statusText}`);
          }

          const apiResponse = await response.json();

          if (!apiResponse.success) {
            throw new Error(apiResponse.error?.message || 'Battle simulation failed');
          }

          set({ 
            battleResult: apiResponse.data, 
            isSimulating: false,
            currentTurn: 0,
            isPlaying: false
          });
        } catch (error) {
          console.error('Battle simulation error:', error);
          set({
            error: error instanceof Error ? error.message : 'Battle simulation failed',
            isSimulating: false,
          });
        }
      },

      resetBattle: () => set({
        pokemon1: null,
        pokemon2: null,
        battleResult: null,
        error: null,
        battleOptions: defaultBattleOptions,
        multiBattleResults: null,
        matrixBattleResults: null,
        selectedMatchup: null,
        sandboxMode: false,
        sandboxActions: [],
        currentTurn: 0,
        isPlaying: false,
      }),

      clearError: () => set({ error: null }),
      
      setBattleMode: (mode) => {
        set({ 
          battleMode: mode,
          battleResult: null,
          multiBattleResults: null,
          matrixBattleResults: null,
          selectedMatchup: null,
          error: null,
          sandboxMode: false,
          sandboxActions: [],
        });
      },
      
      setPokemonTeam1: (team) => {
        set({ 
          pokemonTeam1: team,
          multiBattleResults: null,
          matrixBattleResults: null,
          error: null
        });
      },
      
      setPokemonTeam2: (team) => {
        set({ 
          pokemonTeam2: team,
          matrixBattleResults: null,
          error: null
        });
      },
      
      // Sandbox actions
      toggleSandboxMode: () => {
        set((state) => ({ 
          sandboxMode: !state.sandboxMode,
          sandboxActions: !state.sandboxMode ? [] : state.sandboxActions,
          battleResult: !state.sandboxMode ? null : state.battleResult,
        }));
      },
      
      addSandboxAction: (action) => {
        set((state) => ({
          sandboxActions: [...state.sandboxActions, action],
          battleResult: null, // Clear battle result when adding a sandbox action
        }));
      },
      
      removeSandboxAction: (index) => {
        set((state) => ({
          sandboxActions: state.sandboxActions.filter((_, i) => i !== index),
          battleResult: null, // Clear battle result when removing a sandbox action
        }));
      },
      
      clearSandboxActions: () => {
        set({
          sandboxActions: [],
          battleResult: null, // Clear battle result when clearing sandbox actions
        });
      },
      
      // Timeline playback actions
      setTimelineScale: (scale) => {
        set({ timelineScale: scale });
      },
      
      setPlaybackSpeed: (speed) => {
        set({ playbackSpeed: speed });
      },
      
      playTimeline: () => {
        const { battleResult, currentTurn, playbackSpeed } = get();
        
        if (!battleResult) return;
        
        set({ isPlaying: true });
        
        // Start playback interval
        const intervalId = setInterval(() => {
          const { isPlaying, currentTurn, battleResult } = get();
          
          if (!isPlaying || !battleResult) {
            clearInterval(intervalId);
            return;
          }
          
          if (currentTurn >= battleResult.turns) {
            set({ isPlaying: false });
            clearInterval(intervalId);
            return;
          }
          
          set({ currentTurn: currentTurn + 1 });
        }, 500 / playbackSpeed); // 500ms per turn, adjusted by playback speed
        
        // Store interval ID for cleanup
        // Note: In a real implementation, we'd need to clean this up properly
        // when the component unmounts, but for simplicity we're omitting that here
      },
      
      pauseTimeline: () => {
        set({ isPlaying: false });
      },
      
      seekTimeline: (turn) => {
        const { battleResult } = get();
        
        if (!battleResult) return;
        
        // Ensure turn is within valid range
        const validTurn = Math.max(0, Math.min(turn, battleResult.turns));
        
        set({ currentTurn: validTurn });
      },
    }),
    {
      name: 'battle-store',
    }
  )
);
