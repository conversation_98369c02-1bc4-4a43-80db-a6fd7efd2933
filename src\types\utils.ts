// Utility types for better type safety and developer experience

// Make all properties optional recursively
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Make specific properties required
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Make specific properties optional
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Extract array element type
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

// Create a union of all values in an object
export type ValueOf<T> = T[keyof T];

// Create a type that excludes null and undefined
export type NonNullable<T> = T extends null | undefined ? never : T;

// Create a type for async function return types
export type AsyncReturnType<T extends (...args: any) => Promise<any>> = T extends (
  ...args: any
) => Promise<infer R>
  ? R
  : any;

// Create a type for function parameters
export type Parameters<T extends (...args: any) => any> = T extends (
  ...args: infer P
) => any
  ? P
  : never;

// Create a branded type for better type safety
export type Brand<T, B> = T & { __brand: B };

// Common branded types for PvPoke
export type PokemonId = Brand<string, 'PokemonId'>;
export type MoveId = Brand<string, 'MoveId'>;
export type UserId = Brand<string, 'UserId'>;
export type TeamId = Brand<string, 'TeamId'>;

// Result type for operations that can fail
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

// Option type for values that might not exist
export type Option<T> = T | null | undefined;

// Create a type that represents loading states
export type LoadingState<T> = 
  | { status: 'idle' }
  | { status: 'loading' }
  | { status: 'success'; data: T }
  | { status: 'error'; error: string };

// Create a type for paginated responses
export type Paginated<T> = {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

// Create a type for search results
export type SearchResult<T> = {
  query: string;
  results: T[];
  total: number;
  took: number; // milliseconds
  filters?: Record<string, any>;
};

// Create a type for form field states
export type FieldState<T> = {
  value: T;
  error?: string;
  touched: boolean;
  dirty: boolean;
};

// Create a type for form states
export type FormState<T> = {
  [K in keyof T]: FieldState<T[K]>;
} & {
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
};

// Create a type for API endpoints
export type ApiEndpoint<TRequest = any, TResponse = any> = {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  request?: TRequest;
  response: TResponse;
};

// Create a type for event handlers
export type EventHandler<T = Event> = (event: T) => void;

// Create a type for React component props with children
export type PropsWithChildren<P = {}> = P & {
  children?: React.ReactNode;
};

// Create a type for React component props with className
export type PropsWithClassName<P = {}> = P & {
  className?: string;
};

// Create a type for React component props with common HTML attributes
export type PropsWithHtmlAttributes<P = {}, T = HTMLElement> = P & 
  React.HTMLAttributes<T>;

// Create a type for Zustand store actions
export type StoreActions<T> = {
  [K in keyof T as K extends `set${string}` | `update${string}` | `reset${string}` | `clear${string}` | `add${string}` | `remove${string}` | `toggle${string}`
    ? K
    : never]: T[K];
};

// Create a type for Zustand store state (excluding actions)
export type StoreState<T> = Omit<T, keyof StoreActions<T>>;

// Create a type for component variants
export type ComponentVariant<T extends string> = {
  variant?: T;
  size?: 'sm' | 'md' | 'lg';
};

// Create a type for theme colors
export type ThemeColor = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info';

// Create a type for responsive values
export type ResponsiveValue<T> = T | {
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
};
