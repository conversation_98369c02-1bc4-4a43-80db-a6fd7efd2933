import { useState } from 'react';
import Head from 'next/head';
import { Pokemon, League, BattlePokemon } from '@/types/pokemon';
import { PokemonSearch } from '@/components/common/PokemonSearch';
import { PokemonStats } from '@/components/common/PokemonStats';
import { RankingsList } from '@/components/rankings/RankingsList';

export default function DemoPage() {
  const [selectedPokemon, setSelectedPokemon] = useState<Pokemon | null>(null);
  const [selectedLeague, setSelectedLeague] = useState<League>('great');
  const [battlePokemon, setBattlePokemon] = useState<BattlePokemon | null>(null);

  // Convert Pokemon to BattlePokemon when selected
  const handlePokemonSelect = (pokemon: Pokemon) => {
    setSelectedPokemon(pokemon);
    
    // Create a BattlePokemon with default values
    const defaultBattlePokemon: BattlePokemon = {
      ...pokemon,
      level: 20,
      ivs: { attack: 15, defense: 15, stamina: 15 },
      selectedMoves: {
        fastMove: pokemon.fastMoves[0] || {
          moveId: 'QUICK_ATTACK',
          name: 'Quick Attack',
          type: 'normal',
          power: 8,
          energy: 10,
          cooldown: 1,
          archetype: 'High Energy',
        },
        chargedMoves: pokemon.chargedMoves.slice(0, 2).length > 0 
          ? pokemon.chargedMoves.slice(0, 2)
          : [{
              moveId: 'BODY_SLAM',
              name: 'Body Slam',
              type: 'normal',
              power: 60,
              energy: -35,
              cooldown: 0,
              archetype: 'Low Energy',
            }],
      },
      cp: 1500,
      hp: 150,
    };
    
    setBattlePokemon(defaultBattlePokemon);
  };

  // Handle stats changes
  const handleStatsChange = (updates: Partial<Pick<BattlePokemon, 'level' | 'ivs'>>) => {
    if (battlePokemon) {
      setBattlePokemon({
        ...battlePokemon,
        ...updates,
      });
    }
  };

  return (
    <>
      <Head>
        <title>PvPoke Modern - Component Demo</title>
        <meta name="description" content="Demo of migrated PvPoke components" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-pvpoke-background">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              PvPoke Modern - Component Demo
            </h1>
            <p className="text-xl text-gray-600">
              Showcasing migrated UI components with modern React and TypeScript
            </p>
          </div>

          {/* Demo Sections */}
          <div className="space-y-12">
            {/* Pokemon Search Demo */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Pokemon Search Component
              </h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Search Pokemon
                  </h3>
                  <PokemonSearch
                    onSelect={handlePokemonSelect}
                    selectedPokemon={selectedPokemon}
                    placeholder="Search for a Pokemon..."
                    showStats={true}
                    maxResults={8}
                  />
                </div>
                
                {battlePokemon && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Pokemon Stats
                    </h3>
                    <PokemonStats
                      pokemon={battlePokemon}
                      showAdvanced={true}
                      onStatsChange={handleStatsChange}
                    />
                  </div>
                )}
              </div>
            </section>

            {/* Rankings Demo */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Rankings Component
              </h2>
              
              {/* League Selector */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Select League
                </h3>
                <div className="flex flex-wrap gap-2">
                  {(['great', 'ultra', 'master', 'little', 'premier'] as League[]).map((league) => (
                    <button
                      key={league}
                      onClick={() => setSelectedLeague(league)}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors capitalize ${
                        selectedLeague === league
                          ? 'bg-pvpoke-primary text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {league} League
                    </button>
                  ))}
                </div>
              </div>

              {/* Rankings List */}
              <RankingsList
                league={selectedLeague}
                category="overall"
              />
            </section>

            {/* Component Features */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Modern Features Demonstrated
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    TypeScript Integration
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Full type safety with comprehensive interfaces for Pokemon, moves, and battle data.
                  </p>
                </div>

                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    Zustand State Management
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Modern state management with automatic persistence and optimistic updates.
                  </p>
                </div>

                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    Tailwind CSS Styling
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Pokemon-themed design tokens with responsive layouts and smooth animations.
                  </p>
                </div>

                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    API Integration
                  </h3>
                  <p className="text-gray-600 text-sm">
                    RESTful API endpoints with proper error handling and caching strategies.
                  </p>
                </div>

                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    Accessibility
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Keyboard navigation, screen reader support, and WCAG compliance.
                  </p>
                </div>

                <div className="pokemon-card p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    Performance
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Optimized rendering, lazy loading, and efficient data fetching.
                  </p>
                </div>
              </div>
            </section>

            {/* Migration Progress */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                Migration Progress
              </h2>
              
              <div className="pokemon-card p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Core Infrastructure</span>
                    <span className="text-green-600 font-bold">✓ Complete</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">TypeScript Interfaces</span>
                    <span className="text-green-600 font-bold">✓ Complete</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">GameMaster Data Loading</span>
                    <span className="text-green-600 font-bold">✓ Complete</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">API Routes</span>
                    <span className="text-blue-600 font-bold">🔄 In Progress</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">UI Components</span>
                    <span className="text-blue-600 font-bold">🔄 In Progress</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Battle Engine</span>
                    <span className="text-gray-500">⏳ Planned</span>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>
    </>
  );
}
