# Design Document

## Overview

This design outlines the migration of PvPoke from its current "lasagna code" architecture (PHP views + vanilla JavaScript controllers) to a modern, maintainable stack using Next.js, TypeScript, Zustand, and Tailwind CSS. The migration will preserve all existing functionality while dramatically improving code organization, type safety, and developer experience.

The current architecture follows a loose MVC pattern where PHP files serve as views, GameMaster.js acts as the model layer, and various JavaScript files (Battle.js, TeamRanker.js, etc.) function as controllers. The new architecture will embrace React's component-based approach with proper separation of concerns and modern state management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph Frontend["Frontend (Next.js + TypeScript)"]
        A[Pages/Routes] --> B[React Components]
        B --> C[Zustand Stores]
        B --> D[Hooks & Utils]
        C --> E[API Layer]
    end
    
    subgraph Backend["Backend (Next.js API Routes)"]
        E --> F[API Routes]
        F --> G[Services Layer]
        G --> H[Data Access Layer]
    end
    
    subgraph DataLayer["Data Layer"]
        H --> I[Database/JSON Files]
        H --> J[Game Master Data]
    end
    
    subgraph Deploy["Build & Deploy"]
        K[Static Generation] --> L[CDN/Vercel]
        M[API Routes] --> N[Serverless Functions]
    end
```

### Migration Strategy

The migration will follow a **strangler fig pattern**, gradually replacing legacy components while maintaining functionality:

1. **Phase 1**: Set up Next.js infrastructure and migrate static data
2. **Phase 2**: Migrate core battle engine and Pokemon data models
3. **Phase 3**: Migrate UI components and state management
4. **Phase 4**: Migrate ranking system and team builder
5. **Phase 5**: Performance optimization and cleanup

## Components and Interfaces

### Core Data Models (TypeScript Interfaces)

```typescript
// Core Pokemon data structure
interface Pokemon {
  speciesId: string;
  speciesName: string;
  dex: number;
  types: PokemonType[];
  baseStats: BaseStats;
  fastMoves: Move[];
  chargedMoves: Move[];
  tags: string[];
  buddyDistance: number;
  thirdMoveCost: number;
  released: boolean;
}

interface Move {
  moveId: string;
  name: string;
  type: PokemonType;
  power: number;
  energy: number;
  energyGain?: number;
  cooldown: number;
  buffs?: MoveBuffs[];
  archetype: string;
}

interface BattleResult {
  winner: 'pokemon1' | 'pokemon2' | 'tie';
  pokemon1: BattlePokemonResult;
  pokemon2: BattlePokemonResult;
  timeline: BattleEvent[];
}
```

### Component Architecture

```
src/
├── components/
│   ├── common/           # Reusable UI components
│   │   ├── PokemonCard.tsx
│   │   ├── MoveSelector.tsx
│   │   └── TypeIcon.tsx
│   ├── battle/           # Battle simulator components
│   │   ├── BattleSimulator.tsx
│   │   ├── BattleResults.tsx
│   │   └── PokemonSelector.tsx
│   ├── rankings/         # Rankings components
│   │   ├── RankingsList.tsx
│   │   ├── RankingFilters.tsx
│   │   └── RankingCard.tsx
│   └── team-builder/     # Team building components
│       ├── TeamBuilder.tsx
│       ├── TeamMember.tsx
│       └── TeamAnalysis.tsx
├── stores/               # Zustand state stores
│   ├── battleStore.ts
│   ├── pokemonStore.ts
│   ├── userStore.ts
│   └── rankingsStore.ts
├── services/             # Business logic layer
│   ├── battleEngine.ts
│   ├── rankingService.ts
│   └── pokemonService.ts
├── utils/                # Utility functions
│   ├── typeEffectiveness.ts
│   ├── cpCalculations.ts
│   └── formatters.ts
└── types/                # TypeScript type definitions
    ├── pokemon.ts
    ├── battle.ts
    └── rankings.ts
```

### State Management with Zustand

```typescript
// Battle Store
interface BattleState {
  pokemon1: BattlePokemon | null;
  pokemon2: BattlePokemon | null;
  battleResult: BattleResult | null;
  isSimulating: boolean;
  
  // Actions
  setPokemon1: (pokemon: BattlePokemon) => void;
  setPokemon2: (pokemon: BattlePokemon) => void;
  runSimulation: () => Promise<void>;
  clearBattle: () => void;
}

// Pokemon Store
interface PokemonState {
  allPokemon: Pokemon[];
  filteredPokemon: Pokemon[];
  selectedLeague: League;
  searchQuery: string;
  
  // Actions
  loadPokemon: () => Promise<void>;
  filterPokemon: (filters: PokemonFilters) => void;
  setLeague: (league: League) => void;
}
```

## Data Models

### Pokemon Data Structure

The current GameMaster.js singleton will be replaced with a proper service layer and typed data models:

```typescript
interface GameMasterData {
  pokemon: Pokemon[];
  moves: Move[];
  types: TypeEffectiveness;
  leagues: League[];
  cups: Cup[];
}

class PokemonService {
  private gamemaster: GameMasterData;
  
  async loadGameMaster(): Promise<GameMasterData> {
    // Load and validate game master data
  }
  
  getPokemonBySpecies(speciesId: string): Pokemon | undefined {
    // Type-safe Pokemon lookup
  }
  
  getMoveById(moveId: string): Move | undefined {
    // Type-safe move lookup
  }
}
```

### Battle Engine Migration

The current Battle.js will be refactored into a clean, testable service:

```typescript
class BattleEngine {
  calculateDamage(
    attacker: BattlePokemon,
    defender: BattlePokemon,
    move: Move
  ): DamageResult {
    // Migrated damage calculation logic
  }
  
  simulateBattle(
    pokemon1: BattlePokemon,
    pokemon2: BattlePokemon,
    options: BattleOptions
  ): BattleResult {
    // Core battle simulation logic
  }
  
  private calculateTypeEffectiveness(
    moveType: PokemonType,
    defenderTypes: PokemonType[]
  ): number {
    // Type effectiveness calculations
  }
}
```

## Error Handling

### API Error Handling

```typescript
// Centralized error handling for API routes
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string
  ) {
    super(message);
  }
}

// Error boundary for React components
export class ErrorBoundary extends Component<Props, State> {
  // Handle component errors gracefully
}
```

### Data Validation

```typescript
// Runtime validation using Zod
import { z } from 'zod';

const PokemonSchema = z.object({
  speciesId: z.string(),
  speciesName: z.string(),
  types: z.array(z.enum(['normal', 'fire', 'water', /* ... */])),
  // ... other validations
});

// Validate API responses and user inputs
```

## Testing Strategy

### Unit Testing

- **Battle Engine**: Comprehensive tests for damage calculations, type effectiveness, and battle outcomes
- **Pokemon Service**: Tests for data loading, filtering, and lookup functions
- **Utility Functions**: Tests for CP calculations, formatting, and helper functions
- **Zustand Stores**: Tests for state mutations and side effects

### Component Testing

- **React Testing Library**: Test component rendering, user interactions, and prop handling
- **Mock Service Layer**: Isolate components from business logic during testing
- **Accessibility Testing**: Ensure all components meet WCAG guidelines

### Integration Testing

- **API Routes**: Test data retrieval, error handling, and response formatting
- **Battle Simulation**: End-to-end tests comparing legacy vs new battle results
- **Data Migration**: Validation tests ensuring data integrity during migration

### Performance Testing

- **Battle Simulation Speed**: Benchmark battle calculations against legacy system
- **Ranking Generation**: Test ranking calculation performance and memory usage
- **Bundle Size**: Monitor JavaScript bundle size and loading performance

### Migration Validation

```typescript
// Automated comparison testing
class MigrationValidator {
  async compareBattleResults(
    pokemon1: Pokemon,
    pokemon2: Pokemon
  ): Promise<ValidationResult> {
    const legacyResult = await this.runLegacyBattle(pokemon1, pokemon2);
    const newResult = await this.runNewBattle(pokemon1, pokemon2);
    
    return this.compareResults(legacyResult, newResult);
  }
}
```

## Implementation Approach

### Development Environment Setup

1. **Next.js Configuration**: Set up with TypeScript, ESLint, and Prettier
2. **Tailwind CSS**: Configure with custom Pokemon-themed design tokens
3. **Testing Setup**: Jest, React Testing Library, and Playwright for E2E
4. **Development Tools**: Storybook for component development, TypeScript strict mode

### Data Migration Strategy

1. **Schema Mapping**: Create TypeScript interfaces matching current data structure
2. **Validation Scripts**: Build tools to validate data integrity during migration
3. **Incremental Migration**: Migrate data in phases to minimize risk
4. **Rollback Plan**: Maintain ability to revert to legacy system if needed

### Deployment Strategy

1. **Staging Environment**: Deploy to Vercel preview for testing
2. **Feature Flags**: Use feature toggles to gradually roll out new components
3. **Performance Monitoring**: Track Core Web Vitals and user experience metrics
4. **Gradual Rollout**: Implement blue-green deployment for zero-downtime migration

This design provides a solid foundation for migrating PvPoke to a modern, maintainable architecture while preserving all existing functionality and ensuring a smooth transition for both users and developers.