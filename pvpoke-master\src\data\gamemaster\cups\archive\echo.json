{"name": "echo", "title": "<PERSON><PERSON><PERSON> (Echo)", "include": [{"filterType": "id", "name": "Species", "values": ["articuno", "stunfisk", "swampert", "ferrothorn", "vigoroth", "wigglytuff", "drifb<PERSON>", "talonflame", "zapdos", "obstagoon", "quagsire", "mandibuzz", "empoleon", "magnezone", "probopass", "wormadam_trash", "ninetales_alolan", "whimsicott", "nidoqueen", "scrafty", "whiscash", "pelipper", "mantine", "jellicent", "politoed", "<PERSON><PERSON><PERSON>", "venusaur", "golbat", "ma<PERSON>le"]}, {"filterType": "id", "name": "Species", "values": ["steelix", "escavalier", "jellicent", "dragonite", "<PERSON><PERSON><PERSON>", "flygon", "kommo_o", "dragalge", "haxorus", "roserade", "toxicroak", "heracross", "gyarados", "<PERSON><PERSON><PERSON>", "ma<PERSON>le", "vigoroth", "mandibuzz", "cresselia", "mew", "r<PERSON><PERSON>_alolan", "obstagoon", "diggersby", "<PERSON><PERSON><PERSON><PERSON>", "ninetales", "magcargo", "marowak_alolan", "talonflame", "<PERSON>ras", "malamar", "vespiquen", "articuno", "umbreon", "sableye", "drapion", "graveler_alolan", "golem_alolan", "crustle", "barbara<PERSON>", "sudowoodo", "relicanth", "togetic", "s<PERSON><PERSON>", "forretress"]}, {"filterType": "id", "name": "Species", "values": ["stunfisk", "cofagrigus", "ninetales", "sandslash_alolan", "dragonite", "gliscor", "gligar", "deoxys_defense", "sandslash", "froslass", "marowak_alolan", "magcargo", "flygon", "trevenant", "<PERSON>rserker", "oranguru", "samu<PERSON>t", "<PERSON><PERSON><PERSON>", "politoed", "cresselia", "excadrill", "quagsire", "castform_rainy", "mew", "<PERSON><PERSON><PERSON>", "leavanny", "torterra", "regice", "pinsir", "hypno", "dragonair", "goodra", "cradily", "tapu_fini", "greedent", "munchlax", "snorlax", "sceptile", "aurorus", "<PERSON>ras", "sealeo", "skuntank", "muk_alolan", "grimer_alolan", "qwilfish_his<PERSON>an", "dedenne", "sudowoodo", "barbara<PERSON>", "d<PERSON><PERSON><PERSON>"]}, {"filterType": "id", "name": "Species", "values": ["charizard", "poliwrath", "piloswine", "qwilfish_his<PERSON>an", "gligar", "gliscor", "sir<PERSON><PERSON><PERSON>", "primeape", "machamp", "moltres", "electivire", "zapdos", "ampha<PERSON>", "minun", "<PERSON><PERSON><PERSON>", "victini", "pidgeot", "cofagrigus", "walrein", "sealeo", "dragonair", "regirock", "gallade", "milotic", "muk", "relicanth", "pelipper", "<PERSON><PERSON><PERSON>", "crustle", "shiftry", "tyrunt", "malamar", "deoxys_defense", "aggron", "gengar", "haunter", "dusclops", "run<PERSON><PERSON>", "serperior", "ferrothorn"]}], "exclude": [{"filterType": "tag", "name": "Tag", "values": ["mega", "shadow"]}, {"filterType": "type", "name": "Type", "values": ["bug", "dark", "dragon", "electric", "fairy", "fighting", "fire", "flying", "ghost", "grass", "ground", "ice", "normal", "poison", "psychic", "rock", "steel", "water", "none"]}], "allowSameSpecies": true, "partySize": 8}