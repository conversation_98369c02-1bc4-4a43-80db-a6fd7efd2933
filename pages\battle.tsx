import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { League, BattlePokemon } from '@/types/pokemon';
import { BattleResult } from '@/types/battle';
import { PokemonSelector } from '@/components/battle/PokemonSelector';
import { PokemonMultiSelector } from '@/components/battle/PokemonMultiSelector';
import { BattleOptions } from '@/components/battle/BattleOptions';
import { BattleResults } from '@/components/battle/BattleResults';
import { BattleTimeline } from '@/components/battle/BattleTimeline';
import { BattleError } from '@/components/battle/BattleError';
import { BattleModeSelector } from '@/components/battle/BattleModeSelector';
import { useBattleStore } from '@/stores/battleStore';
import clsx from 'clsx';

export default function BattlePage() {
  const router = useRouter();
  const {
    pokemon1,
    pokemon2,
    league,
    battleOptions,
    battleResult,
    isSimulating,
    battleMode,
    setPokemon1,
    setPokemon2,
    setLeague,
    setBattleOptions,
    simulateBattle,
    swapPokemon,
    resetBattle,
  } = useBattleStore();

  const [showTimeline, setShowTimeline] = useState(false);

  // Load battle configuration from URL on mount
  useEffect(() => {
    const { p1, p2, league: urlLeague, shields1, shields2 } = router.query;

    if (urlLeague && typeof urlLeague === 'string') {
      setLeague(urlLeague as League);
    }

    // TODO: Load Pokemon from URL parameters
    // This would involve parsing Pokemon IDs and configurations from URL
  }, [router.query, setLeague]);

  // Update URL when battle configuration changes
  useEffect(() => {
    if (pokemon1 && pokemon2) {
      const params = new URLSearchParams();
      params.set('p1', pokemon1.speciesId);
      params.set('p2', pokemon2.speciesId);
      params.set('league', league);
      params.set('shields1', battleOptions.shields.pokemon1.toString());
      params.set('shields2', battleOptions.shields.pokemon2.toString());

      const newUrl = `/battle?${params.toString()}`;
      router.replace(newUrl, undefined, { shallow: true });
    }
  }, [pokemon1, pokemon2, league, battleOptions, router]);

  const handleBattle = async () => {
    if (pokemon1 && pokemon2) {
      await simulateBattle();
    }
  };

  const handleSwap = () => {
    swapPokemon();
  };

  const canBattle = pokemon1 && pokemon2 && !isSimulating;

  return (
    <>
      <Head>
        <title>Battle Simulator - PvPoke Modern</title>
        <meta name="description" content="Simulate Pokemon GO PvP battles" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main>
        <h1>Battle</h1>

        <div className="section league-select-container white">
          <BattleOptions />
          <BattleModeSelector />
          <p className={clsx("description single", battleMode !== 'single' && "hide")}>
            Select two Pokemon from any league to fight a simulated battle. Customize movesets, levels, IV's, and shields.
          </p>
          <p className={clsx("description multi", battleMode !== 'multi' && "hide")}>
            Battle one Pokemon against an entire league or cup. Explore overall performance or individual matchups against a group of Pokemon.
          </p>
          <p className={clsx("description matrix", battleMode !== 'matrix' && "hide")}>
            Battle two groups of Pokemon against each other and see a matrix of the results. Use this to explore mass matchups or compare different Pokemon, movesets, or IV's.
          </p>
        </div>

        <div className={clsx("section poke-select-container single", battleMode !== 'single' && "hide")}>
          <PokemonSelector
            pokemon={pokemon1}
            onPokemonChange={setPokemon1}
            league={league}
            side="left"
          />
          <PokemonSelector
            pokemon={pokemon2}
            onPokemonChange={setPokemon2}
            league={league}
            side="right"
          />
        </div>
        {/* Pokemon Multi Selection Layout - Multi Mode */}
        <div className={clsx("section poke-select-container multi", battleMode !== 'multi' && "hide")}>
          <PokemonSelector
            pokemon={pokemon1}
            onPokemonChange={setPokemon1}
            league={league}
            side="left"
          />
          <PokemonMultiSelector
            selectedPokemon={[]}
            onPokemonAdd={() => {}}
            onPokemonRemove={() => {}}
            onPokemonClear={() => {}}
            title="Opponents"
            maxSelections={100}
          />
        </div>
        {/* Pokemon Matrix Selection Layout - Matrix Mode */}
        <div className={clsx("section poke-select-container matrix", battleMode !== 'matrix' && "hide")}>
          <PokemonMultiSelector
            selectedPokemon={[]}
            onPokemonAdd={() => {}}
            onPokemonRemove={() => {}}
            onPokemonClear={() => {}}
            title="Team 1"
            maxSelections={20}
          />
          <PokemonMultiSelector
            selectedPokemon={[]}
            onPokemonAdd={() => {}}
            onPokemonRemove={() => {}}
            onPokemonClear={() => {}}
            title="Team 2"
            maxSelections={20}
          />
        </div>

        <div className="section battle">
          <button
            className="battle-btn button"
            onClick={handleBattle}
            disabled={!canBattle}
          >
            <span className="btn-content-wrap">
              <span className="btn-icon btn-icon-battle"></span>
              <span className="btn-label">
                {isSimulating ? 'Battling...' : 'Battle'}
              </span>
            </span>
          </button>
          <button className="update-btn button">
            <span className="btn-content-wrap">
              <span className="btn-icon btn-icon-battle"></span>
              <span className="btn-label">Update</span>
            </span>
          </button>
          <div className="tooltip">
            <h3 className="name"></h3>
            <div className="details"></div>
          </div>

          <div className="battle-results single">
            <div className="sandbox-btn-container">
              <div className="sandbox-btn" title="Manually edit the timeline">
                <span>Sandbox Mode</span>
                <div className="btn-background"></div>
              </div>
              <div className="sandbox clear-btn" title="Clear Timeline"></div>
            </div>
            <div className="clear"></div>

            <div className="timeline-container scale">
              <div className="timeline"></div>
              <div className="timeline"></div>
              <div className="tracker"></div>
              {battleResult && <BattleTimeline timeline={battleResult.timeline} />}
            </div>

            <div className="playback section white">
              <div className="flex">
                <div className="playback-btn replay"></div>
                <div className="playback-btn play"></div>
                <select className="playback-speed" defaultValue="8">
                  <option value="1">1x speed*</option>
                  <option value="4">4x speed</option>
                  <option value="8">8x speed</option>
                  <option value="16">16x speed</option>
                </select>
                <select className="playback-scale" defaultValue="fit">
                  <option value="fit">Scale to fit</option>
                  <option value="zoom">Zoom in</option>
                </select>
              </div>
              <div className="disclaimer">* Results may differ from actual gameplay depending on connectivity, device, player decisions, or other factors.</div>
            </div>

            <div className="summary section white">
              {battleResult && <BattleResults result={battleResult} />}
            </div>

            <div className="tip automated">
              Hover over or tap the timeline for details.
              <a href="/articles/strategy/guide-to-fast-move-registration/">Read more</a> about the timeline.
            </div>
            <div className="tip sandbox">Click the circles to edit actions.</div>
          </div>

          <div className="battle-results single">
            <div className="share-link-container">
              <p>Share this battle:</p>
              <div className="share-link">
                <input
                  type="text"
                  value={typeof window !== 'undefined' ? window.location.href : ''}
                  readOnly
                />
                <div
                  className="copy"
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      navigator.clipboard.writeText(window.location.href);
                      // TODO: Add toast notification
                    }
                  }}
                >
                  Copy
                </div>
              </div>
            </div>

            <div className="continue-container">
              <p>Continue with <span className="name">{pokemon1?.speciesName || 'Pokemon'}</span> against another opponent:</p>
              <div
                className="button"
                onClick={() => {
                  setPokemon2(null);
                  // TODO: Implement continue battle functionality
                }}
              >
                Continue Battling
              </div>
            </div>
          </div>

          
          
        </div>
      </main>
    </>
  );
}
