import type { NextApiRequest, NextApiResponse } from 'next';
import { gameMasterLoader } from '@/services/gameMasterLoader';
import type { ApiResponse } from '@/types/api';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    // Try to load compiled gamemaster first, fallback to compilation if needed
    let gamemaster;

    try {
      gamemaster = await gameMasterLoader.loadCompiledGameMaster();
    } catch (error) {
      console.warn('Failed to load compiled gamemaster, attempting compilation:', error);
      gamemaster = await gameMasterLoader.compileGameMaster();
    }

    // Validate the data
    if (!gameMasterLoader.validateGameMasterData(gamemaster)) {
      throw new Error('Invalid gamemaster data structure');
    }

    const stats = gameMasterLoader.getDataStats(gamemaster);

    res.status(200).json({
      success: true,
      data: gamemaster,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
        cache: {
          cached: false,
          lastModified: gamemaster.timestamp,
        },
        stats,
      },
    });
  } catch (error) {
    console.error('GameMaster API error:', error);

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to load GameMaster data',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
