import { BattlePokemon, BattleResult } from './battle';

// Multi-battle result
export interface MultiBattleResult {
  id: string;
  opponent: BattlePokemon;
  battleRating: number;
  win: boolean;
  result: BattleResult;
}

// Matrix battle result
export interface MatrixBattleResult {
  grid: {
    [team1PokemonId: string]: {
      [team2PokemonId: string]: {
        battleRating: number;
        win: boolean;
      }
    }
  };
  team1Pokemon: BattlePokemon[];
  team2Pokemon: BattlePokemon[];
  results: {
    [matchupId: string]: BattleResult;
  };
}