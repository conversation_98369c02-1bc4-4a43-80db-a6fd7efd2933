import type { NextApiRequest, NextApiResponse } from 'next';
import { pokemonService } from '@/services/pokemonService';
import type { ApiResponse, PokemonSearchParams } from '@/types/api';
import type { Pokemon } from '@/types/pokemon';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Pokemon[]>>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    const {
      q: query = '',
      types,
      tags,
      released,
      page = '1',
      limit = '50',
    } = req.query as Partial<PokemonSearchParams>;

    // Parse query parameters
    const searchOptions = {
      limit: Math.min(parseInt(limit as string) || 50, 100), // Max 100 results
      includeUnreleased: String(released) !== 'false',
      types: types ? (Array.isArray(types) ? types as any[] : [types]) as any[] : undefined,
      tags: tags ? (Array.isArray(tags) ? tags : [tags]) : undefined,
    };

    let pokemon: Pokemon[];

    if (query) {
      // Search Pokemon
      pokemon = await pokemonService.searchPokemon(query as string, searchOptions);
    } else {
      // Get all Pokemon with filters
      pokemon = await pokemonService.getAllPokemon();
      
      // Apply filters
      if (!searchOptions.includeUnreleased) {
        pokemon = pokemon.filter(p => p.released);
      }
      
      if (searchOptions.types) {
        pokemon = pokemon.filter(p =>
          searchOptions.types!.some(type => p.types.includes(type as any))
        );
      }
      
      if (searchOptions.tags) {
        pokemon = pokemon.filter(p =>
          searchOptions.tags!.some(tag => p.tags.includes(tag))
        );
      }
      
      // Apply pagination
      const pageNum = parseInt(page as string) || 1;
      const startIndex = (pageNum - 1) * searchOptions.limit;
      pokemon = pokemon.slice(startIndex, startIndex + searchOptions.limit);
    }

    res.status(200).json({
      success: true,
      data: pokemon,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
        pagination: {
          page: parseInt(page as string) || 1,
          limit: searchOptions.limit,
          total: pokemon.length,
          totalPages: Math.ceil(pokemon.length / searchOptions.limit),
          hasNext: pokemon.length === searchOptions.limit,
          hasPrev: (parseInt(page as string) || 1) > 1,
        },
      },
    });
  } catch (error) {
    console.error('Pokemon API error:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch Pokemon data',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
