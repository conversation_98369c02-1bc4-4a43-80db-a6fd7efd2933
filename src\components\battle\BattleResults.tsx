import { BattleResult } from '@/types/battle';
import clsx from 'clsx';

interface BattleResultsProps {
  result: BattleResult;
}

export function BattleResults({ result }: BattleResultsProps) {
  const { winner, pokemon1, pokemon2, duration, turns } = result;

  const getWinnerDisplay = () => {
    if (winner === 'tie') {
      return {
        text: 'Tie',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
      };
    }
    
    const winnerPokemon = winner === 'pokemon1' ? pokemon1 : pokemon2;
    return {
      text: `${winnerPokemon.pokemon.speciesName} wins!`,
      color: winner === 'pokemon1' ? 'text-blue-600' : 'text-red-600',
      bgColor: winner === 'pokemon1' ? 'bg-blue-50' : 'bg-red-50',
    };
  };

  const winnerDisplay = getWinnerDisplay();

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getHealthPercentage = (remaining: number, total: number) => {
    return Math.round((remaining / total) * 100);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Winner Banner */}
      <div className={clsx('p-6 text-center', winnerDisplay.bgColor)}>
        <h2 className={clsx('text-2xl font-bold', winnerDisplay.color)}>
          {winnerDisplay.text}
        </h2>
        <p className="text-gray-600 mt-2">
          Battle completed in {formatTime(duration)} ({turns} turns)
        </p>
      </div>

      {/* Battle Statistics */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Pokemon 1 Stats */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src={`/images/pokemon/${pokemon1.pokemon.speciesId}.png`}
                alt={pokemon1.pokemon.speciesName}
                className="w-16 h-16"
                onError={(e) => {
                  e.currentTarget.src = '/images/pokemon/0.png';
                }}
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {pokemon1.pokemon.speciesName}
                </h3>
                <p className="text-sm text-gray-600">
                  CP {pokemon1.pokemon.cp} • Level {pokemon1.pokemon.level}
                </p>
              </div>
            </div>

            {/* Health Bar */}
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Health</span>
                <span>{pokemon1.remainingHp}/{pokemon1.pokemon.hp} HP</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={clsx(
                    'h-3 rounded-full transition-all duration-300',
                    pokemon1.remainingHp > pokemon1.pokemon.hp * 0.5 ? 'bg-green-500' :
                    pokemon1.remainingHp > pokemon1.pokemon.hp * 0.25 ? 'bg-yellow-500' : 'bg-red-500'
                  )}
                  style={{
                    width: `${getHealthPercentage(pokemon1.remainingHp, pokemon1.pokemon.hp)}%`
                  }}
                />
              </div>
            </div>

            {/* Battle Stats */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Damage Dealt</div>
                <div className="font-semibold text-lg">{pokemon1.damageDealt}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Damage Taken</div>
                <div className="font-semibold text-lg">{pokemon1.damageTaken}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Energy Used</div>
                <div className="font-semibold text-lg">{pokemon1.energyUsed}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Moves Used</div>
                <div className="font-semibold text-lg">
                  {pokemon1.movesUsed.fast + pokemon1.movesUsed.charged}
                </div>
              </div>
            </div>

            {/* Move Breakdown */}
            <div className="text-sm">
              <div className="text-gray-600 mb-2">Move Usage</div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Fast Moves:</span>
                  <span className="font-medium">{pokemon1.movesUsed.fast}</span>
                </div>
                <div className="flex justify-between">
                  <span>Charged Moves:</span>
                  <span className="font-medium">{pokemon1.movesUsed.charged}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Pokemon 2 Stats */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src={`/images/pokemon/${pokemon2.pokemon.speciesId}.png`}
                alt={pokemon2.pokemon.speciesName}
                className="w-16 h-16"
                onError={(e) => {
                  e.currentTarget.src = '/images/pokemon/0.png';
                }}
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {pokemon2.pokemon.speciesName}
                </h3>
                <p className="text-sm text-gray-600">
                  CP {pokemon2.pokemon.cp} • Level {pokemon2.pokemon.level}
                </p>
              </div>
            </div>

            {/* Health Bar */}
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Health</span>
                <span>{pokemon2.remainingHp}/{pokemon2.pokemon.hp} HP</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={clsx(
                    'h-3 rounded-full transition-all duration-300',
                    pokemon2.remainingHp > pokemon2.pokemon.hp * 0.5 ? 'bg-green-500' :
                    pokemon2.remainingHp > pokemon2.pokemon.hp * 0.25 ? 'bg-yellow-500' : 'bg-red-500'
                  )}
                  style={{
                    width: `${getHealthPercentage(pokemon2.remainingHp, pokemon2.pokemon.hp)}%`
                  }}
                />
              </div>
            </div>

            {/* Battle Stats */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Damage Dealt</div>
                <div className="font-semibold text-lg">{pokemon2.damageDealt}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Damage Taken</div>
                <div className="font-semibold text-lg">{pokemon2.damageTaken}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Energy Used</div>
                <div className="font-semibold text-lg">{pokemon2.energyUsed}</div>
              </div>
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-gray-600">Moves Used</div>
                <div className="font-semibold text-lg">
                  {pokemon2.movesUsed.fast + pokemon2.movesUsed.charged}
                </div>
              </div>
            </div>

            {/* Move Breakdown */}
            <div className="text-sm">
              <div className="text-gray-600 mb-2">Move Usage</div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Fast Moves:</span>
                  <span className="font-medium">{pokemon2.movesUsed.fast}</span>
                </div>
                <div className="flex justify-between">
                  <span>Charged Moves:</span>
                  <span className="font-medium">{pokemon2.movesUsed.charged}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Battle Summary */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Battle Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-gray-50 p-4 rounded">
              <div className="text-gray-600">Total Duration</div>
              <div className="font-semibold text-xl">{formatTime(duration)}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <div className="text-gray-600">Total Turns</div>
              <div className="font-semibold text-xl">{turns}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <div className="text-gray-600">Average Turn Time</div>
              <div className="font-semibold text-xl">
                {turns > 0 ? (duration / turns).toFixed(1) : '0'}s
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
