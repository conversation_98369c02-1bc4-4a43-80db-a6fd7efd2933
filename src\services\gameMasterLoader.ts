import fs from 'fs';
import path from 'path';

/**
 * GameMaster data loader that can compile data from separate JSON files
 * or load from the compiled gamemaster.json file
 */
export class GameMasterLoader {
  private basePath: string;

  constructor(basePath: string = 'pvpoke-master/src/data') {
    this.basePath = basePath;
  }

  /**
   * Load the compiled gamemaster.json file
   */
  async loadCompiledGameMaster(): Promise<any> {
    try {
      const gameMasterPath = path.join(this.basePath, 'gamemaster.json');
      
      if (typeof window !== 'undefined') {
        // Client-side: fetch from public directory
        const response = await fetch('/data/gamemaster.json');
        if (!response.ok) {
          throw new Error(`Failed to fetch gamemaster.json: ${response.statusText}`);
        }
        return await response.json();
      } else {
        // Server-side: read from file system
        const data = fs.readFileSync(gameMasterPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load compiled gamemaster:', error);
      throw error;
    }
  }

  /**
   * Compile gamemaster data from separate JSON files
   * This mimics the functionality of the PHP compile script
   */
  async compileGameMaster(): Promise<any> {
    try {
      // Load base data
      const base = await this.loadJsonFile('gamemaster/base.json');
      
      // Load Pokemon data
      const pokemon = await this.loadJsonFile('gamemaster/pokemon.json');
      
      // Load moves data
      const moves = await this.loadJsonFile('gamemaster/moves.json');
      
      // Load formats data
      const formats = await this.loadJsonFile('gamemaster/formats.json');
      
      // Load cup data
      const cups = await this.loadCupData();
      
      // Compile everything together
      const compiled = {
        ...base,
        timestamp: new Date().toISOString(),
        pokemon,
        moves,
        formats,
        cups,
      };

      return compiled;
    } catch (error) {
      console.error('Failed to compile gamemaster:', error);
      throw error;
    }
  }

  /**
   * Load a JSON file from the data directory
   */
  private async loadJsonFile(relativePath: string): Promise<any> {
    try {
      const fullPath = path.join(this.basePath, relativePath);
      
      if (typeof window !== 'undefined') {
        // Client-side: not supported for compilation
        throw new Error('GameMaster compilation not supported on client-side');
      } else {
        // Server-side: read from file system
        const data = fs.readFileSync(fullPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error(`Failed to load JSON file ${relativePath}:`, error);
      throw error;
    }
  }

  /**
   * Load all cup data from the cups directory
   */
  private async loadCupData(): Promise<any[]> {
    try {
      const cupsDir = path.join(this.basePath, 'gamemaster/cups');
      
      if (typeof window !== 'undefined') {
        // Client-side: not supported
        return [];
      }

      const cupFiles = fs.readdirSync(cupsDir)
        .filter(file => file.endsWith('.json') && !file.startsWith('archive'))
        .filter(file => file !== 'archive'); // Exclude archive directory

      const cups = [];
      for (const file of cupFiles) {
        try {
          const cupData = await this.loadJsonFile(`gamemaster/cups/${file}`);
          cups.push(cupData);
        } catch (error) {
          console.warn(`Failed to load cup file ${file}:`, error);
        }
      }

      return cups;
    } catch (error) {
      console.error('Failed to load cup data:', error);
      return [];
    }
  }

  /**
   * Validate the structure of gamemaster data
   */
  validateGameMasterData(data: any): boolean {
    const requiredFields = ['timestamp', 'settings', 'pokemon', 'moves'];
    
    for (const field of requiredFields) {
      if (!(field in data)) {
        console.error(`Missing required field: ${field}`);
        return false;
      }
    }

    if (!Array.isArray(data.pokemon)) {
      console.error('Pokemon data must be an array');
      return false;
    }

    if (!Array.isArray(data.moves)) {
      console.error('Moves data must be an array');
      return false;
    }

    // Validate a sample of Pokemon entries
    if (data.pokemon.length > 0) {
      const samplePokemon = data.pokemon[0];
      const requiredPokemonFields = ['speciesId', 'speciesName', 'dex', 'types', 'baseStats'];
      
      for (const field of requiredPokemonFields) {
        if (!(field in samplePokemon)) {
          console.error(`Missing required Pokemon field: ${field}`);
          return false;
        }
      }
    }

    // Validate a sample of move entries
    if (data.moves.length > 0) {
      const sampleMove = data.moves[0];
      const requiredMoveFields = ['moveId', 'name', 'type', 'power', 'energy'];
      
      for (const field of requiredMoveFields) {
        if (!(field in sampleMove)) {
          console.error(`Missing required move field: ${field}`);
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Get statistics about the gamemaster data
   */
  getDataStats(data: any): {
    pokemonCount: number;
    moveCount: number;
    shadowPokemonCount: number;
    cupCount: number;
    timestamp: string;
  } {
    return {
      pokemonCount: data.pokemon?.length || 0,
      moveCount: data.moves?.length || 0,
      shadowPokemonCount: data.shadowPokemon?.length || 0,
      cupCount: data.cups?.length || 0,
      timestamp: data.timestamp || 'unknown',
    };
  }

  /**
   * Copy gamemaster files to public directory for client access
   */
  async copyToPublic(publicDir: string = 'public/data'): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        throw new Error('File copying not supported on client-side');
      }

      // Ensure public data directory exists
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }

      // Copy gamemaster.json
      const sourcePath = path.join(this.basePath, 'gamemaster.json');
      const destPath = path.join(publicDir, 'gamemaster.json');
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, destPath);
        console.log('Copied gamemaster.json to public directory');
      } else {
        console.warn('gamemaster.json not found, compiling from source files...');
        const compiled = await this.compileGameMaster();
        fs.writeFileSync(destPath, JSON.stringify(compiled, null, 2));
        console.log('Compiled and saved gamemaster.json to public directory');
      }

      // Also copy the minified version if it exists
      const sourceMinPath = path.join(this.basePath, 'gamemaster.min.json');
      const destMinPath = path.join(publicDir, 'gamemaster.min.json');
      
      if (fs.existsSync(sourceMinPath)) {
        fs.copyFileSync(sourceMinPath, destMinPath);
        console.log('Copied gamemaster.min.json to public directory');
      }

    } catch (error) {
      console.error('Failed to copy gamemaster files to public directory:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const gameMasterLoader = new GameMasterLoader();
