import { useState } from 'react';
import { BattlePokemon, IVs } from '@/types/pokemon';
import { TypeIcon } from './TypeIcon';
import { calculateCP, calculateHP } from '@/utils/cpCalculations';
import clsx from 'clsx';

interface PokemonStatsProps {
  pokemon: BattlePokemon;
  showAdvanced?: boolean;
  onStatsChange?: (updates: Partial<Pick<BattlePokemon, 'level' | 'ivs'>>) => void;
  className?: string;
}

export function PokemonStats({
  pokemon,
  showAdvanced = false,
  onStatsChange,
  className = '',
}: PokemonStatsProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(showAdvanced);
  const [tempLevel, setTempLevel] = useState(pokemon.level.toString());
  const [tempIVs, setTempIVs] = useState(pokemon.ivs);

  // Calculate current stats
  const currentCP = calculateCP(pokemon.baseStats, pokemon.ivs, pokemon.level);
  const currentHP = calculateHP(pokemon.baseStats.stamina, pokemon.ivs.stamina, pokemon.level);

  // Calculate stat percentages for bars (relative to max possible at level 50)
  const maxStats = {
    attack: (pokemon.baseStats.attack + 15) * 0.8403, // Level 50 multiplier
    defense: (pokemon.baseStats.defense + 15) * 0.8403,
    stamina: (pokemon.baseStats.stamina + 15) * 0.8403,
  };

  const currentStats = {
    attack: (pokemon.baseStats.attack + pokemon.ivs.attack) * 0.8403,
    defense: (pokemon.baseStats.defense + pokemon.ivs.defense) * 0.8403,
    stamina: (pokemon.baseStats.stamina + pokemon.ivs.stamina) * 0.8403,
  };

  const statPercentages = {
    attack: (currentStats.attack / maxStats.attack) * 100,
    defense: (currentStats.defense / maxStats.defense) * 100,
    stamina: (currentStats.stamina / maxStats.stamina) * 100,
  };

  // Handle level change
  const handleLevelChange = (value: string) => {
    setTempLevel(value);
    const level = parseFloat(value);
    if (!isNaN(level) && level >= 1 && level <= 50) {
      onStatsChange?.({ level });
    }
  };

  // Handle IV change
  const handleIVChange = (stat: keyof IVs, value: string) => {
    const iv = parseInt(value);
    if (!isNaN(iv) && iv >= 0 && iv <= 15) {
      const newIVs = { ...tempIVs, [stat]: iv };
      setTempIVs(newIVs);
      onStatsChange?.({ ivs: newIVs });
    }
  };

  return (
    <div className={clsx('bg-white rounded-xl shadow-pokemon border border-gray-200 p-6', className)}>
      {/* Pokemon Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-2xl font-bold text-gray-900 flex items-center">
            <span className="text-pvpoke-primary mr-2">CP</span>
            {currentCP}
          </h3>
          <div className="flex space-x-1 mt-2">
            {pokemon.types.map((type) => (
              <TypeIcon key={type} type={type} size="md" />
            ))}
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">#{pokemon.dex}</div>
          <div className="text-lg font-semibold text-gray-900">{pokemon.speciesName}</div>
        </div>
      </div>

      {/* Base Stats */}
      <div className="space-y-4 mb-6">
        {/* Attack */}
        <div className="stat-container">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Attack</span>
            <span className="text-sm font-bold text-gray-900">
              {Math.floor(currentStats.attack * 10) / 10}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-red-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(statPercentages.attack, 100)}%` }}
            />
          </div>
        </div>

        {/* Defense */}
        <div className="stat-container">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Defense</span>
            <span className="text-sm font-bold text-gray-900">
              {Math.floor(currentStats.defense * 10) / 10}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(statPercentages.defense, 100)}%` }}
            />
          </div>
        </div>

        {/* Stamina/HP */}
        <div className="stat-container">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Stamina</span>
            <span className="text-sm font-bold text-gray-900">
              {currentHP}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(statPercentages.stamina, 100)}%` }}
            />
          </div>
        </div>

        {/* Overall Rating */}
        <div className="stat-container">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Overall</span>
            <span className="text-sm font-bold text-gray-900">
              {Math.floor(((statPercentages.attack + statPercentages.defense + statPercentages.stamina) / 3) * 10) / 10}%
            </span>
          </div>
        </div>
      </div>

      {/* Advanced Section */}
      <div className="border-t border-gray-200 pt-4">
        <button
          onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
          className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-pvpoke-primary transition-colors"
        >
          <span>Edit/Check IVs</span>
          <svg
            className={clsx('w-4 h-4 transition-transform', isAdvancedOpen && 'rotate-180')}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        {isAdvancedOpen && (
          <div className="mt-4 space-y-4">
            {/* Level Input */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Level
              </label>
              <input
                type="number"
                value={tempLevel}
                onChange={(e) => handleLevelChange(e.target.value)}
                min="1"
                max="50"
                step="0.5"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-pvpoke-primary focus:border-transparent"
              />
            </div>

            {/* IV Inputs */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-2">
                Individual Values (IVs)
              </label>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Attack</label>
                  <input
                    type="number"
                    value={tempIVs.attack}
                    onChange={(e) => handleIVChange('attack', e.target.value)}
                    min="0"
                    max="15"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-pvpoke-primary focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Defense</label>
                  <input
                    type="number"
                    value={tempIVs.defense}
                    onChange={(e) => handleIVChange('defense', e.target.value)}
                    min="0"
                    max="15"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-pvpoke-primary focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Stamina</label>
                  <input
                    type="number"
                    value={tempIVs.stamina}
                    onChange={(e) => handleIVChange('stamina', e.target.value)}
                    min="0"
                    max="15"
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-pvpoke-primary focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* IV Summary */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>IV Combination:</span>
                  <span className="font-medium">
                    {tempIVs.attack}/{tempIVs.defense}/{tempIVs.stamina}
                  </span>
                </div>
                <div className="flex justify-between mt-1">
                  <span>IV Percentage:</span>
                  <span className="font-medium">
                    {Math.round(((tempIVs.attack + tempIVs.defense + tempIVs.stamina) / 45) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
