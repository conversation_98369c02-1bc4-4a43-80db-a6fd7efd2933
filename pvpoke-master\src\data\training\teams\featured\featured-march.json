[{"name": "aerosol2505", "slug": "aerosol2505", "img": "aerosol2505", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "A Battler from Northern Germany, who spends alot of time scrimming and recently won the Battle4theWorld Tournament. <PERSON><PERSON> fan also.", "link": "https://twitter.com/aerosol25051", "pokemon": [{"speciesId": "hypno", "fastMove": "CONFUSION", "chargedMoves": ["THUNDER_PUNCH", "SHADOW_BALL"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "altaria", "fastMove": "DRAGON_BREATH", "chargedMoves": ["SKY_ATTACK", "DRAGON_PULSE"]}, {"speciesId": "bastiodon", "fastMove": "SMACK_DOWN", "chargedMoves": ["STONE_EDGE", "FLAMETHROWER"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["BONE_CLUB", "SHADOW_BALL"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "img": "<PERSON><PERSON><PERSON><PERSON>", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Pokémon GO PvP:er, nr3 in Sweden", "link": "https://www.twitch.tv/reinberglund", "pokemon": [{"speciesId": "haunter", "fastMove": "SHADOW_CLAW", "chargedMoves": ["SHADOW_PUNCH", "SHADOW_BALL"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "registeel", "fastMove": "LOCK_ON", "chargedMoves": ["FLASH_CANNON", "FOCUS_BLAST"]}, {"speciesId": "gallade", "fastMove": "CONFUSION", "chargedMoves": ["LEAF_BLADE", "CLOSE_COMBAT"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["BONE_CLUB", "SHADOW_BALL"]}, {"speciesId": "shiftry", "fastMove": "SNARL", "chargedMoves": ["LEAF_BLADE", "FOUL_PLAY"]}]}, {"name": "SimplyMoxie", "slug": "simply<PERSON>xie", "img": "simply<PERSON>xie", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "600M+ XP. Ottawa, Canada. TL40 Date: 2016-12-15. Pokemon GO PvP player.", "link": "https://twitter.com/simplymoxie_", "pokemon": [{"speciesId": "clefable", "fastMove": "CHARM", "chargedMoves": ["METEOR_MASH", "PSYCHIC"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "registeel", "fastMove": "LOCK_ON", "chargedMoves": ["FLASH_CANNON", "FOCUS_BLAST"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "r<PERSON><PERSON>_alolan", "fastMove": "VOLT_SWITCH", "chargedMoves": ["THUNDER_PUNCH", "WILD_CHARGE"]}, {"speciesId": "sableye", "fastMove": "SHADOW_CLAW", "chargedMoves": ["FOUL_PLAY", "RETURN"]}]}, {"name": "Champion Blue", "slug": "championblue", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the Champion of the Kanto region.", "link": "#", "pokemon": [{"speciesId": "<PERSON>ras", "fastMove": "ICE_SHARD", "chargedMoves": ["SURF", "SKULL_BASH"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["ICE_BEAM", "HYDRO_PUMP"]}, {"speciesId": "tropius", "fastMove": "AIR_SLASH", "chargedMoves": ["LEAF_BLADE", "AERIAL_ACE"]}, {"speciesId": "probopass", "fastMove": "SPARK", "chargedMoves": ["ROCK_SLIDE", "THUNDERBOLT"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["BONE_CLUB", "SHADOW_BALL"]}, {"speciesId": "machamp", "fastMove": "COUNTER", "chargedMoves": ["CROSS_CHOP", "ROCK_SLIDE"]}]}, {"name": "Champion Lance", "slug": "championlance", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the Champion of the Johto region.", "link": "#", "pokemon": [{"speciesId": "dragonite_shadow", "fastMove": "DRAGON_BREATH", "chargedMoves": ["DRAGON_CLAW", "OUTRAGE"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "BUBBLE", "chargedMoves": ["PLAY_ROUGH", "HYDRO_PUMP"]}, {"speciesId": "registeel", "fastMove": "LOCK_ON", "chargedMoves": ["FLASH_CANNON", "FOCUS_BLAST"]}, {"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["BONE_CLUB", "SHADOW_BALL"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}]}, {"name": "Champion Steven", "slug": "championsteven", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the Champion of the Hoenn region.", "link": "#", "pokemon": [{"speciesId": "venusaur", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "SLUDGE_BOMB"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "AIR_SLASH", "chargedMoves": ["SKY_ATTACK", "FLASH_CANNON"]}, {"speciesId": "altaria", "fastMove": "DRAGON_BREATH", "chargedMoves": ["SKY_ATTACK", "DRAGON_PULSE"]}, {"speciesId": "gastrodon_east_sea", "fastMove": "MUD_SLAP", "chargedMoves": ["BODY_SLAM", "EARTH_POWER"]}, {"speciesId": "melmetal", "fastMove": "THUNDER_SHOCK", "chargedMoves": ["ROCK_SLIDE", "SUPER_POWER"]}, {"speciesId": "castform_rainy", "fastMove": "WATER_GUN", "chargedMoves": ["THUNDERBOLT", "WEATHER_BALL_WATER"]}]}, {"name": "Champion <PERSON>", "slug": "championcynthia", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the Champion of the Sinnoh region.", "link": "#", "pokemon": [{"speciesId": "dewgong", "fastMove": "ICE_SHARD", "chargedMoves": ["ICY_WIND", "WATER_PULSE"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "registeel", "fastMove": "LOCK_ON", "chargedMoves": ["FLASH_CANNON", "FOCUS_BLAST"]}, {"speciesId": "<PERSON><PERSON><PERSON><PERSON>", "fastMove": "CHARM", "chargedMoves": ["ANCIENT_POWER", "FLAMETHROWER"]}, {"speciesId": "marowak_alolan", "fastMove": "FIRE_SPIN", "chargedMoves": ["BONE_CLUB", "SHADOW_BALL"]}, {"speciesId": "garcho<PERSON>", "fastMove": "MUD_SHOT", "chargedMoves": ["SAND_TOMB", "OUTRAGE"]}]}, {"name": "Champion Alder", "slug": "<PERSON><PERSON><PERSON>", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the Champion of the Unova region.", "link": "#", "pokemon": [{"speciesId": "venusaur", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "SLUDGE_BOMB"]}, {"speciesId": "umbreon", "fastMove": "SNARL", "chargedMoves": ["FOUL_PLAY", "LAST_RESORT"]}, {"speciesId": "deoxys_defense", "fastMove": "COUNTER", "chargedMoves": ["ROCK_SLIDE", "THUNDERBOLT"]}, {"speciesId": "froslass", "fastMove": "POWDER_SNOW", "chargedMoves": ["AVALANCHE", "SHADOW_BALL"]}, {"speciesId": "alomomola", "fastMove": "WATERFALL", "chargedMoves": ["PSYCHIC", "HYDRO_PUMP"]}, {"speciesId": "mantine", "fastMove": "BUBBLE", "chargedMoves": ["BUBBLE_BEAM", "ICE_BEAM"]}]}, {"name": "Champion Leon", "slug": "championleon", "img": "pvpoke", "cup": "voyager", "cupName": "Voyager", "league": 1500, "description": "Train against this Voyager Cup team inspired by the undefeated Champion of the Galar region.", "link": "#", "pokemon": [{"speciesId": "charizard", "fastMove": "FIRE_SPIN", "chargedMoves": ["BLAST_BURN", "DRAGON_CLAW"]}, {"speciesId": "meganium", "fastMove": "VINE_WHIP", "chargedMoves": ["FRENZY_PLANT", "EARTHQUAKE"]}, {"speciesId": "swampert", "fastMove": "MUD_SHOT", "chargedMoves": ["HYDRO_CANNON", "SLUDGE_WAVE"]}, {"speciesId": "skuntank", "fastMove": "POISON_JAB", "chargedMoves": ["CRUNCH", "FLAMETHROWER"]}, {"speciesId": "melmetal", "fastMove": "THUNDER_SHOCK", "chargedMoves": ["ROCK_SLIDE", "SUPER_POWER"]}, {"speciesId": "clefable", "fastMove": "CHARM", "chargedMoves": ["METEOR_MASH", "PSYCHIC"]}]}, {"name": "<PERSON><PERSON><PERSON>", "slug": "arrohh", "img": "arrohh", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "Season 1 ranked #38 in the world Ace Trainer Elite 4 Taco Truck GoStadium Coach just keep tapping", "link": "https://twitter.com/TheArrohh", "pokemon": [{"speciesId": "toxicroak", "fastMove": "COUNTER", "chargedMoves": ["MUD_BOMB", "SLUDGE_BOMB"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["SHADOW_BALL", "POISON_FANG"]}, {"speciesId": "bibarel", "fastMove": "WATER_GUN", "chargedMoves": ["SURF", "HYPER_FANG"]}, {"speciesId": "muk_alolan", "fastMove": "SNARL", "chargedMoves": ["DARK_PULSE", "SLUDGE_WAVE"]}, {"speciesId": "claydol", "fastMove": "CONFUSION", "chargedMoves": ["EARTH_POWER", "PSYCHIC"]}, {"speciesId": "pidgeot", "fastMove": "WING_ATTACK", "chargedMoves": ["AERIAL_ACE", "HURRICANE"]}]}, {"name": "Gucc1G4ng69", "slug": "gucc1G4ng69", "img": "gucc1G4ng69", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "PoGo PvP fanatic striving to be the best #Battler I can be • Season 1 Challenger aiming for Ace and beyond in Season 2 • Valor", "link": "https://twitter.com/Gucc1G4ng69", "pokemon": [{"speciesId": "escavalier", "fastMove": "COUNTER", "chargedMoves": ["AERIAL_ACE", "DRILL_RUN"]}, {"speciesId": "drapion", "fastMove": "ICE_FANG", "chargedMoves": ["AQUA_TAIL", "CRUNCH"]}, {"speciesId": "<PERSON><PERSON><PERSON>", "fastMove": "VOLT_SWITCH", "chargedMoves": ["DISCHARGE", "BUG_BUZZ"]}, {"speciesId": "qwilfish", "fastMove": "WATER_GUN", "chargedMoves": ["AQUA_TAIL", "SLUDGE_WAVE"]}, {"speciesId": "wormadam_trash", "fastMove": "CONFUSION", "chargedMoves": ["IRON_HEAD", "BUG_BUZZ"]}, {"speciesId": "flygon", "fastMove": "MUD_SHOT", "chargedMoves": ["DRAGON_CLAW", "EARTH_POWER"]}]}, {"name": "HouseStark93", "slug": "housestark93", "img": "housestark93", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "Man. Looking to be ranked #1 globally for Pogo pvp and to continue to grow the pvp community. Season 1 rank: #11", "link": "https://twitter.com/HouseStark_93", "pokemon": [{"speciesId": "escavalier", "fastMove": "COUNTER", "chargedMoves": ["AERIAL_ACE", "MEGAHORN"]}, {"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["SHADOW_BALL", "POISON_FANG"]}, {"speciesId": "qwilfish", "fastMove": "WATER_GUN", "chargedMoves": ["FELL_STINGER", "AQUA_TAIL"]}, {"speciesId": "bibarel", "fastMove": "WATER_GUN", "chargedMoves": ["SURF", "HYPER_FANG"]}, {"speciesId": "wormadam_trash", "fastMove": "CONFUSION", "chargedMoves": ["IRON_HEAD", "BUG_BUZZ"]}, {"speciesId": "flygon", "fastMove": "MUD_SHOT", "chargedMoves": ["DRAGON_CLAW", "EARTH_POWER"]}]}, {"name": "RamblingRabbit", "slug": "ramblingrabbit", "img": "ramblingrabbit", "cup": "toxic", "cupName": "Toxic", "league": 1500, "description": "I'm the organizer for the AlabamaBattlers! Remote server & GO Stadium Community Outreach/Social Media Contributor.<br>I can be found here on Twitter or at http://twitch.tv/codymiles_ramblingrabbit.", "link": "https://twitter.com/RambRabbitPoGo", "pokemon": [{"speciesId": "golbat", "fastMove": "WING_ATTACK", "chargedMoves": ["POISON_FANG", "SHADOW_BALL"]}, {"speciesId": "wigglytuff", "fastMove": "CHARM", "chargedMoves": ["ICE_BEAM", "PLAY_ROUGH"]}, {"speciesId": "celebi", "fastMove": "CONFUSION", "chargedMoves": ["SEED_BOMB", "PSYCHIC"]}, {"speciesId": "bibarel", "fastMove": "WATER_GUN", "chargedMoves": ["SURF", "HYPER_FANG"]}, {"speciesId": "steelix", "fastMove": "DRAGON_TAIL", "chargedMoves": ["CRUNCH", "EARTHQUAKE"]}, {"speciesId": "heracross", "fastMove": "COUNTER", "chargedMoves": ["CLOSE_COMBAT", "MEGAHORN"]}]}]