RewriteEngine On

RewriteCond %{SCRIPT_FILENAME} !-d
RewriteCond %{SCRIPT_FILENAME} !-f

# RewriteCond %{HTTPS} !=on
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# RewriteCond %{HTTP_HOST} ^www\.pvpoke\.com [NC]
# RewriteRule ^(.*)$ https://pvpoke.com/$1 [L,R=301]

RewriteRule ^season-13/(.*)$ /$1 [R,L]
RewriteRule ^season-15/(.*)$ /$1 [R,L]
RewriteRule ^world-of-wonders/(.*)$ /$1 [R,L]
RewriteRule ^shared-skies/(.*)$ /$1 [R,L]
RewriteRule ^new-season/(.*)$ /$1 [R,L]
RewriteRule ^might-and-mastery/(.*)$ /$1 [R,L]
RewriteRule ^delightful-days/(.*)$ /$1 [R,L]

Options -Indexes

RewriteRule ^unite(.*)$ https://unite.pvpoke.com [R=301,L]

RewriteRule ^contribute(.*)$ https://pvpoke.com/contact [R=301,L]

RewriteRule battle/multi/([\d-]+)/([a-zA-Z0-9-]+)/([a-zA-Z_\d\.-]+)/([\d-]+)/([\da-zA-Z_-]+)/([a-z\d-]+)/(\d+)/(\d+)/([a-zA-Z_]+).*$ battle.php?mode=multi&cp=$1&cup=$2&p1=$3&s=$4&m1=$5&cms=$6&h=$7&e=$8&g1=$9 [L,QSA]
RewriteRule battle/multi/([\d-]+)/([a-zA-Z0-9-]+)/([a-zA-Z_\d\.-]+)/([\d-]+)/([\da-zA-Z_-]+)/([a-z\d-]+)/(\d+)/(\d+).*$ battle.php?mode=multi&cp=$1&cup=$2&p1=$3&s=$4&m1=$5&cms=$6&h=$7&e=$8 [L,QSA]
RewriteRule battle/multi/([\d-]+)/([a-zA-Z0-9-]+)/([a-zA-Z_\d\.-]+)/([\d-]+)/([\da-zA-Z_-]+)/([a-z\d-]+)/([a-zA-Z_]+).*$ battle.php?mode=multi&cp=$1&cup=$2&p1=$3&s=$4&m1=$5&cms=$6&g1=$7 [L,QSA]
RewriteRule battle/multi/([\d-]+)/([a-zA-Z0-9-]+)/([a-zA-Z_\d\.-]+)/([\d-]+)/([\da-zA-Z_-]+)/([a-z\d-]+).*$ battle.php?mode=multi&cp=$1&cup=$2&p1=$3&s=$4&m1=$5&cms=$6 [L,QSA]
RewriteRule battle/multi/([\d-]+)/([a-zA-Z0-9-]+)/([a-zA-Z_\d\.-]+)/([\d-]+)/([a-z\d-]+).*$ battle.php?mode=multi&cp=$1&cup=$2&p1=$3&s=$4&cms=$5 [L,QSA]

RewriteRule battle/multi.*$ battle.php?mode=multi [L,QSA]

RewriteRule battle/matrix.*$ battle.php?mode=matrix [L,QSA]

RewriteRule battle/sandbox/(\d+)/([a-zA-Z_\d\.-]+)/([a-zA-Z_\d\.-]+)/(\d+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\d-]+)/([\d-]+)/([\d\.-]+).*$ battle.php?cp=$1&p1=$2&p2=$3&s=$4&m1=$5&m2=$6&h=$7&e=$8&sandbox=1&a=$9 [L,QSA]
RewriteRule battle/sandbox/(\d+)/([a-zA-Z_\d\.-]+)/([a-zA-Z_\d\.-]+)/(\d+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\d\.-]+).*$ battle.php?cp=$1&p1=$2&p2=$3&s=$4&m1=$5&m2=$6&sandbox=1&a=$7 [L,QSA]

RewriteRule battle/([\d-]+)/([a-zA-Z_\d\.-]+)/([a-zA-Z_\d\.-]+)/(\d+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\d-]+)/([\d-]+).*$ battle.php?cp=$1&p1=$2&p2=$3&s=$4&m1=$5&m2=$6&h=$7&e=$8 [L,QSA]
RewriteRule battle/([\d-]+)/([a-zA-Z_\d\.-]+)/([a-zA-Z_\d\.-]+)/(\d+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ battle.php?cp=$1&p1=$2&p2=$3&s=$4&m1=$5&m2=$6 [L,QSA]
RewriteRule battle/([\d-]+)/([a-zA-Z_]+)/([a-zA-Z_]+)/(\d+).*$ battle.php?cp=$1&p1=$2&p2=$3&s=$4 [L,QSA]
RewriteRule battle.?$ battle.php [L,QSA]

RewriteRule ^rankings/([a-zA-Z0-9-]+)/(\d+)/([a-zA-Z]+)/([a-zA-Z_]+).*$ rankings.php?cup=$1&cp=$2&cat=$3&p=$4 [L,QSA]
RewriteRule ^rankings/(\d+)/([a-zA-Z0-9-]+)/([a-zA-Z_]+).*$ rankings.php?cp=$1&cat=$2&p=$3 [L,QSA]
RewriteRule ^rankings/([a-zA-Z0-9-]+)/(\d+)/([a-zA-Z]+).*$ rankings.php?cup=$1&cp=$2&cat=$3 [L,QSA]
RewriteRule ^rankings/(\d+)/([a-zA-Z0-9-]+).*$ rankings.php?cp=$1&cat=$2 [L,QSA]
RewriteRule ^rankings/(\d+).*$ rankings.php?cp=$1 [L,QSA]
RewriteRule ^rankings.?$ rankings.php [L,QSA]

RewriteRule team-builder/([a-zA-Z0-9-]+)/([\d-]+)/([a-zA-Z_]+)/([a-zA-Z_]+)/([a-zA-Z_]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ team-builder.php?cup=$1&cp=$2&p1=$3&p2=$4&p3=$5&m1=$6&m2=$7&m3=$8 [L,QSA]
RewriteRule team-builder/([a-zA-Z0-9-]+)/([\d-]+)/([a-zA-Z_]+)/([a-zA-Z_]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ team-builder.php?cup=$1&cp=$2&p1=$3&p2=$4&m1=$5&m2=$6 [L,QSA]
RewriteRule team-builder/([a-zA-Z0-9-]+)/([\d-]+)/([a-zA-Z_]+)/([\da-zA-Z_-]+).*$ team-builder.php?cup=$1&cp=$2&p1=$3&m1=$4 [L,QSA]
RewriteRule team-builder/([a-zA-Z0-9-]+)/([\d-]+)/([a-zA-Z_\-0-9,\.]+).*$ team-builder.php?cup=$1&cp=$2&t=$3 [L,QSA]
RewriteRule team-builder.?$ team-builder.php [L,QSA]

RewriteRule train/analysis.?$ train/analysis.php [L,QSA]
RewriteRule ^train/analysis/([a-zA-Z0-9-]+)/(\d+).*$ train/analysis.php?cup=$1&cp=$2 [L,QSA]

RewriteRule train/editor.?$ train/editor.php [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule tera/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ tera/index.php?p=$1&t=$2&a=$3&tr=$4 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule tera/([\da-zA-Z_-]+)/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ tera/index.php?p=$1&t=$2&a=$3 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule tera/([\da-zA-Z_-]+)/([\da-zA-Z_-]+).*$ tera/index.php?p=$1&t=$2 [L,QSA]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule tera/([\da-zA-Z_-]+).*$ tera/index.php?p=$1 [L,QSA]

RewriteRule moves/([a-zA-Z_]+).*$ moves.php?mode=$1 [L,QSA]
RewriteRule moves.?$ moves.php [L,QSA]

RewriteRule contact.?$ contact.php [L,QSA]

RewriteRule privacy.?$ privacy.php [L,QSA]

RewriteRule settings.?$ settings.php [L,QSA]

RewriteRule custom-rankings.?$ custom-rankings.php [L,QSA]

RewriteRule articles/(.*)/$ articles/$1.php [L,QSA]

RewriteRule rss/$ rss/feed.xml [L,QSA]


# Redirect index.php to /

RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}\ /(.*)index\.php($|\ |\?)
RewriteRule ^ /%1 [R=301,L]

# Disable pagespeed module

<IfModule pagespeed_module>
    ModPagespeed off
</IfModule>
