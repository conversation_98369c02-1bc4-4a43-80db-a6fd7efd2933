import Head from 'next/head';
import Link from 'next/link';

export default function Home() {
  return (
    <>
      <Head>
        <title>PvPoke - Pokemon Go PvP Rankings and Battle Simulator</title>
        <meta name="description" content="Pokemon Go PvP rankings and battle simulator" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      
      <main className="min-h-screen bg-pvpoke-background">
        <div className="container mx-auto px-4 py-8">
          <header className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              PvPoke
            </h1>
            <p className="text-xl text-gray-600">
              Pokemon Go PvP Rankings and Battle Simulator
            </p>
          </header>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="pokemon-card p-6 text-center">
              <h2 className="text-xl font-semibold mb-3">Battle Simulator</h2>
              <p className="text-gray-600 mb-4">
                Simulate battles between Pokemon with different movesets and strategies.
              </p>
              <Link href="/battle" className="btn-primary inline-block">
                Start Battle
              </Link>
            </div>
            
            <div className="pokemon-card p-6 text-center">
              <h2 className="text-xl font-semibold mb-3">Rankings</h2>
              <p className="text-gray-600 mb-4">
                View Pokemon rankings for Great League, Ultra League, and Master League.
              </p>
              <button className="btn-primary">
                View Rankings
              </button>
            </div>
            
            <div className="pokemon-card p-6 text-center">
              <h2 className="text-xl font-semibold mb-3">Team Builder</h2>
              <p className="text-gray-600 mb-4">
                Build and analyze teams for Pokemon Go PvP battles.
              </p>
              <button className="btn-primary">
                Build Team
              </button>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <div className="flex justify-center space-x-2 mb-4">
              <span className="type-badge type-fire">Fire</span>
              <span className="type-badge type-water">Water</span>
              <span className="type-badge type-grass">Grass</span>
              <span className="type-badge type-electric">Electric</span>
              <span className="type-badge type-psychic">Psychic</span>
            </div>
            <p className="text-sm text-gray-500">
              Modern PvPoke - Built with Next.js, TypeScript, and Tailwind CSS
            </p>
          </div>
        </div>
      </main>
    </>
  );
}
