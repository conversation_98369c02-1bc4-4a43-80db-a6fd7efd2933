# Design Document

## Overview

This design outlines the enhancement of the battle page in the PvPoke Next.js migration. The current implementation has basic functionality but lacks many features from the original PHP version. We will implement the missing features while maintaining the modern architecture using Next.js, TypeScript, Zustand for state management, and Tailwind CSS for styling.

The battle page is a core feature of PvPoke, allowing users to simulate battles between Pokemon with various configurations. The enhanced implementation will support all three battle modes (Single, Multi, Matrix) from the original PvPoke, along with advanced features like sandbox mode, breakpoint/bulkpoint analysis, and shareable battle links.

## Architecture

### Component Architecture

```mermaid
graph TD
    BattlePage --> BattleOptions
    BattlePage --> BattleModeSelector
    BattlePage --> PokemonSelectionArea
    BattlePage --> BattleControls
    BattlePage --> BattleResults
    
    BattleModeSelector --> SingleMode
    BattleModeSelector --> MultiMode
    BattleModeSelector --> MatrixMode
    
    PokemonSelectionArea --> PokemonSelector
    PokemonSelectionArea --> PokemonMultiSelector
    
    BattleResults --> BattleSummary
    BattleResults --> BattleTimeline
    BattleResults --> BattleDetails
    BattleResults --> BreakpointAnalysis
    BattleResults --> BulkpointAnalysis
    BattleResults --> ChargedMoveTies
    BattleResults --> BattleStats
    BattleResults --> ShareLink
    
    BattleTimeline --> TimelineEvent
    BattleTimeline --> SandboxControls
    
    MultiMode --> BattleHistogram
    MultiMode --> MatchupList
    
    MatrixMode --> MatrixGrid
    MatrixMode --> MatrixControls
```
### S
tate Management

We'll enhance the existing `battleStore` to support all battle modes and features:

```typescript
interface BattleState {
  // Existing state
  pokemon1: BattlePokemon | null;
  pokemon2: BattlePokemon | null;
  league: League;
  battleResult: BattleResult | null;
  isSimulating: boolean;
  battleOptions: BattleOptions;
  error: string | null;

  // New state for enhanced features
  battleMode: 'single' | 'multi' | 'matrix';
  sandboxMode: boolean;
  pokemonTeam1: BattlePokemon[]; // For multi/matrix mode
  pokemonTeam2: BattlePokemon[]; // For multi/matrix mode
  multiBattleResults: MultiBattleResult[] | null;
  matrixBattleResults: MatrixBattleResult | null;
  selectedMatchup: string | null; // For viewing specific matchups in multi/matrix
  timelineScale: 'fit' | 'zoom';
  playbackSpeed: 1 | 4 | 8 | 16;
  isPlaying: boolean;
  currentTurn: number;
  sandboxActions: SandboxAction[];
  
  // Actions
  // Existing actions
  setPokemon1: (pokemon: BattlePokemon | null) => void;
  setPokemon2: (pokemon: BattlePokemon | null) => void;
  setLeague: (league: League) => void;
  setBattleOptions: (options: Partial<BattleOptions>) => void;
  swapPokemon: () => void;
  simulateBattle: () => Promise<void>;
  resetBattle: () => void;
  clearError: () => void;
  
  // New actions
  setBattleMode: (mode: 'single' | 'multi' | 'matrix') => void;
  toggleSandboxMode: () => void;
  setPokemonTeam1: (team: BattlePokemon[]) => void;
  setPokemonTeam2: (team: BattlePokemon[]) => void;
  simulateMultiBattle: () => Promise<void>;
  simulateMatrixBattle: () => Promise<void>;
  selectMatchup: (matchupId: string) => void;
  setTimelineScale: (scale: 'fit' | 'zoom') => void;
  setPlaybackSpeed: (speed: 1 | 4 | 8 | 16) => void;
  playTimeline: () => void;
  pauseTimeline: () => void;
  seekTimeline: (turn: number) => void;
  addSandboxAction: (action: SandboxAction) => void;
  removeSandboxAction: (index: number) => void;
  clearSandboxActions: () => void;
  generateShareLink: () => string;
  loadFromShareLink: (params: URLSearchParams) => Promise<void>;
  exportToCsv: () => void;
}
```#
## Data Models

We'll extend the existing battle-related types to support all features:

```typescript
// Enhanced BattleOptions
interface BattleOptions {
  shields: {
    pokemon1: number;
    pokemon2: number;
  };
  strategy: 'neutral' | 'aggressive' | 'defensive';
  allowSwitching: boolean;
  timeLimit?: number;
  battleFormat: 'single' | 'multi' | 'matrix';
  energyAdvantage?: number;
  moveTimingAdvantage?: number;
  scenario?: 'lead' | 'switch' | 'closer';
  sandboxMode: boolean;
}

// Multi-battle result
interface MultiBattleResult {
  id: string;
  opponent: BattlePokemon;
  battleRating: number;
  win: boolean;
  result: BattleResult;
}

// Matrix battle result
interface MatrixBattleResult {
  grid: {
    [team1PokemonId: string]: {
      [team2PokemonId: string]: {
        battleRating: number;
        win: boolean;
      }
    }
  };
  team1Pokemon: BattlePokemon[];
  team2Pokemon: BattlePokemon[];
  results: {
    [matchupId: string]: BattleResult;
  };
}

// Sandbox action
interface SandboxAction {
  turn: number;
  actor: 'pokemon1' | 'pokemon2';
  actionType: 'fast' | 'charged' | 'shield' | 'wait' | 'switch';
  moveId?: string;
  chargeLevel?: 0 | 1 | 2 | 3 | 4; // 0 = 100%, 1 = 95%, 2 = 75%, 3 = 50%, 4 = 25%
  shielded?: boolean;
  applyBuffs?: boolean;
  switchPokemonIndex?: number;
}

// Breakpoint analysis
interface Breakpoint {
  damage: number;
  minimumAttack: number;
  guaranteedAttack: number;
  levelIvCombinations: {
    level: number;
    ivs: IVs;
    attack: number;
  }[];
}

// Bulkpoint analysis
interface Bulkpoint {
  damage: number;
  minimumDefense: number;
  guaranteedDefense: number;
  levelIvCombinations: {
    level: number;
    ivs: IVs;
    defense: number;
  }[];
}

// Charged Move Tie analysis
interface ChargedMoveTie {
  minimumAttack: number;
  guaranteedAttack: number;
  levelIvCombinations: {
    level: number;
    ivs: IVs;
    attack: number;
  }[];
}
```## Compo
nents and Interfaces

### Battle Mode Selector

The Battle Mode Selector will allow users to switch between Single, Multi, and Matrix battle modes:

```tsx
function BattleModeSelector() {
  const { battleMode, setBattleMode } = useBattleStore();
  
  return (
    <div className="flex rounded-lg overflow-hidden">
      {['single', 'multi', 'matrix'].map((mode) => (
        <button
          key={mode}
          className={clsx(
            'px-4 py-2 text-sm font-medium transition-colors',
            battleMode === mode
              ? 'bg-pvpoke-primary text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
          onClick={() => setBattleMode(mode as 'single' | 'multi' | 'matrix')}
        >
          {mode.charAt(0).toUpperCase() + mode.slice(1)}
        </button>
      ))}
    </div>
  );
}
```

### Enhanced Battle Timeline

The Battle Timeline will be enhanced to support playback controls and sandbox mode:

```tsx
function BattleTimeline() {
  const {
    battleResult,
    timelineScale,
    playbackSpeed,
    isPlaying,
    currentTurn,
    sandboxMode,
    sandboxActions,
    setTimelineScale,
    setPlaybackSpeed,
    playTimeline,
    pauseTimeline,
    seekTimeline,
    addSandboxAction,
    removeSandboxAction,
  } = useBattleStore();
  
  // Timeline rendering logic
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Battle Timeline</h3>
        
        {sandboxMode && (
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm font-medium"
              onClick={clearSandboxActions}
            >
              Clear Timeline
            </button>
          </div>
        )}
      </div>
      
      <div className={`timeline-container ${timelineScale}`}>
        {/* Timeline visualization */}
        <div className="relative h-24 bg-gray-50 rounded-lg overflow-hidden">
          {/* Timeline events */}
          {renderTimelineEvents()}
          
          {/* Current position tracker */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-red-500"
            style={{ left: `${(currentTurn / battleResult.turns) * 100}%` }}
          />
        </div>
      </div>
      
      {/* Playback controls */}
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center space-x-3">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            onClick={() => seekTimeline(0)}
          >
            {/* Replay icon */}
          </button>
          
          <button
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
            onClick={isPlaying ? pauseTimeline : playTimeline}
          >
            {/* Play/Pause icon */}
          </button>
          
          <select
            value={playbackSpeed}
            onChange={(e) => setPlaybackSpeed(Number(e.target.value) as 1 | 4 | 8 | 16)}
            className="px-2 py-1 text-sm border border-gray-300 rounded"
          >
            <option value={1}>1x speed*</option>
            <option value={4}>4x speed</option>
            <option value={8}>8x speed</option>
            <option value={16}>16x speed</option>
          </select>
          
          <select
            value={timelineScale}
            onChange={(e) => setTimelineScale(e.target.value as 'fit' | 'zoom')}
            className="px-2 py-1 text-sm border border-gray-300 rounded"
          >
            <option value="fit">Scale to fit</option>
            <option value="zoom">Zoom in</option>
          </select>
        </div>
        
        {/* Sandbox mode toggle */}
        <div
          className={clsx(
            'px-3 py-1 rounded-full text-sm font-medium cursor-pointer transition-colors',
            sandboxMode
              ? 'bg-pvpoke-primary text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
          onClick={toggleSandboxMode}
        >
          Sandbox Mode
        </div>
      </div>
      
      <div className="text-xs text-gray-500 mt-2">
        * Results may differ from actual gameplay depending on connectivity, device, player decisions, or other factors.
      </div>
    </div>
  );
}
```#
## Breakpoint and Bulkpoint Analysis

```tsx
function BreakpointAnalysis() {
  const { pokemon1, pokemon2, breakpoints, selectedBreakpointMove, setSelectedBreakpointMove, applyBreakpointIVs } = useBattleStore();
  
  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Breakpoints</h3>
      
      <p className="text-sm text-gray-600 mb-4">
        {pokemon1?.speciesName} can reach the breakpoints below against this {pokemon2?.speciesName}.
      </p>
      
      <div className="mb-4">
        <select
          value={selectedBreakpointMove}
          onChange={(e) => setSelectedBreakpointMove(e.target.value)}
          className="w-full px-3 py-2 text-sm border border-gray-300 rounded"
        >
          {pokemon1?.selectedMoves.fastMove && (
            <option value={pokemon1.selectedMoves.fastMove.moveId}>
              {pokemon1.selectedMoves.fastMove.name} ({pokemon1.selectedMoves.fastMove.type})
            </option>
          )}
          {pokemon1?.selectedMoves.chargedMoves.map((move) => (
            <option key={move.moveId} value={move.moveId}>
              {move.name} ({move.type})
            </option>
          ))}
        </select>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Damage</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Minimum Attack</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attack to Guarantee</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Top Level & IV's</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {breakpoints.map((breakpoint, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-4 py-2 text-sm text-gray-900">{breakpoint.damage}</td>
                <td className="px-4 py-2 text-sm text-gray-900">{breakpoint.minimumAttack.toFixed(2)}</td>
                <td className="px-4 py-2 text-sm text-gray-900">{breakpoint.guaranteedAttack.toFixed(2)}</td>
                <td className="px-4 py-2">
                  <div className="flex flex-wrap gap-2">
                    {breakpoint.levelIvCombinations.slice(0, 3).map((combo, i) => (
                      <button
                        key={i}
                        className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100"
                        onClick={() => applyBreakpointIVs(combo)}
                      >
                        L{combo.level} ({combo.ivs.attack}/{combo.ivs.defense}/{combo.ivs.stamina})
                      </button>
                    ))}
                    {breakpoint.levelIvCombinations.length > 3 && (
                      <span className="text-xs text-gray-500">+{breakpoint.levelIvCombinations.length - 3} more</span>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
```

### Matrix Battle Grid

```tsx
function MatrixBattleGrid() {
  const { matrixBattleResults, selectMatchup } = useBattleStore();
  
  if (!matrixBattleResults) return null;
  
  const { grid, team1Pokemon, team2Pokemon } = matrixBattleResults;
  
  const getBattleRatingColor = (rating: number) => {
    if (rating >= 750) return 'bg-green-500 text-white';
    if (rating >= 600) return 'bg-green-300';
    if (rating >= 500) return 'bg-green-100';
    if (rating >= 400) return 'bg-red-100';
    if (rating >= 250) return 'bg-red-300';
    return 'bg-red-500 text-white';
  };
  
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border border-gray-200">
        <thead>
          <tr>
            <th className="border border-gray-200 p-2"></th>
            {team2Pokemon.map((pokemon) => (
              <th key={pokemon.speciesId} className="border border-gray-200 p-2 text-xs">
                <div className="w-16 flex flex-col items-center">
                  <img
                    src={`/images/pokemon/${pokemon.speciesId}.png`}
                    alt={pokemon.speciesName}
                    className="w-10 h-10"
                    onError={(e) => {
                      e.currentTarget.src = '/images/pokemon/0.png';
                    }}
                  />
                  <span className="truncate w-full text-center">{pokemon.speciesName}</span>
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {team1Pokemon.map((pokemon1) => (
            <tr key={pokemon1.speciesId}>
              <td className="border border-gray-200 p-2 text-xs">
                <div className="w-16 flex flex-col items-center">
                  <img
                    src={`/images/pokemon/${pokemon1.speciesId}.png`}
                    alt={pokemon1.speciesName}
                    className="w-10 h-10"
                    onError={(e) => {
                      e.currentTarget.src = '/images/pokemon/0.png';
                    }}
                  />
                  <span className="truncate w-full text-center">{pokemon1.speciesName}</span>
                </div>
              </td>
              {team2Pokemon.map((pokemon2) => {
                const matchupResult = grid[pokemon1.speciesId]?.[pokemon2.speciesId];
                if (!matchupResult) return <td key={pokemon2.speciesId} className="border border-gray-200 p-2 bg-gray-100"></td>;
                
                const matchupId = `${pokemon1.speciesId}-${pokemon2.speciesId}`;
                
                return (
                  <td
                    key={pokemon2.speciesId}
                    className={`border border-gray-200 p-2 text-center cursor-pointer ${getBattleRatingColor(matchupResult.battleRating)}`}
                    onClick={() => selectMatchup(matchupId)}
                  >
                    {Math.round(matchupResult.battleRating)}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```## 
API Design

We'll enhance the existing battle API to support all battle modes:

### Single Battle API

```typescript
// pages/api/battle/simulate.ts (existing)
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<BattleResult>>
) {
  // Existing implementation
}
```

### Multi Battle API

```typescript
// pages/api/battle/simulate-multi.ts
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<MultiBattleResult[]>>
) {
  // Validate request body
  const validationResult = MultiBattleSimulationRequestSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    // Handle validation error
    return;
  }
  
  const { pokemon, opponents, options } = validationResult.data;
  
  // Simulate battles against all opponents
  const results: MultiBattleResult[] = [];
  
  for (const opponent of opponents) {
    // Simulate individual battle
    const battleResult = await simulateBattle(pokemon, opponent, options);
    
    results.push({
      id: `${pokemon.speciesId}-${opponent.speciesId}`,
      opponent,
      battleRating: battleResult.pokemon1.battleRating,
      win: battleResult.winner === 'pokemon1',
      result: battleResult,
    });
  }
  
  // Return results
  res.status(200).json({
    success: true,
    data: results,
    meta: {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      requestId: Math.random().toString(36).substr(2, 9),
    },
  });
}
```

### Matrix Battle API

```typescript
// pages/api/battle/simulate-matrix.ts
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<MatrixBattleResult>>
) {
  // Validate request body
  const validationResult = MatrixBattleSimulationRequestSchema.safeParse(req.body);
  
  if (!validationResult.success) {
    // Handle validation error
    return;
  }
  
  const { team1, team2, options } = validationResult.data;
  
  // Simulate all matchups
  const grid: MatrixBattleResult['grid'] = {};
  const results: MatrixBattleResult['results'] = {};
  
  for (const pokemon1 of team1) {
    grid[pokemon1.speciesId] = {};
    
    for (const pokemon2 of team2) {
      // Simulate individual battle
      const battleResult = await simulateBattle(pokemon1, pokemon2, options);
      
      const matchupId = `${pokemon1.speciesId}-${pokemon2.speciesId}`;
      
      grid[pokemon1.speciesId][pokemon2.speciesId] = {
        battleRating: battleResult.pokemon1.battleRating,
        win: battleResult.winner === 'pokemon1',
      };
      
      results[matchupId] = battleResult;
    }
  }
  
  // Return results
  res.status(200).json({
    success: true,
    data: {
      grid,
      team1Pokemon: team1,
      team2Pokemon: team2,
      results,
    },
    meta: {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      requestId: Math.random().toString(36).substr(2, 9),
    },
  });
}
```#
# URL State Management

We'll implement a comprehensive URL state management system to support shareable battle links:

```typescript
// URL parameter handling for single battle
function getSingleBattleUrlParams(state: BattleState): URLSearchParams {
  const params = new URLSearchParams();
  
  params.set('mode', 'single');
  params.set('league', state.league);
  
  if (state.pokemon1) {
    params.set('p1', state.pokemon1.speciesId);
    params.set('cp1', state.pokemon1.cp.toString());
    params.set('f1', state.pokemon1.selectedMoves.fastMove.moveId);
    state.pokemon1.selectedMoves.chargedMoves.forEach((move, i) => {
      params.set(`c${i+1}1`, move.moveId);
    });
    params.set('a1', state.pokemon1.ivs.attack.toString());
    params.set('d1', state.pokemon1.ivs.defense.toString());
    params.set('s1', state.pokemon1.ivs.stamina.toString());
    params.set('l1', state.pokemon1.level.toString());
  }
  
  if (state.pokemon2) {
    params.set('p2', state.pokemon2.speciesId);
    params.set('cp2', state.pokemon2.cp.toString());
    params.set('f2', state.pokemon2.selectedMoves.fastMove.moveId);
    state.pokemon2.selectedMoves.chargedMoves.forEach((move, i) => {
      params.set(`c${i+1}2`, move.moveId);
    });
    params.set('a2', state.pokemon2.ivs.attack.toString());
    params.set('d2', state.pokemon2.ivs.defense.toString());
    params.set('s2', state.pokemon2.ivs.stamina.toString());
    params.set('l2', state.pokemon2.level.toString());
  }
  
  params.set('sh1', state.battleOptions.shields.pokemon1.toString());
  params.set('sh2', state.battleOptions.shields.pokemon2.toString());
  params.set('strat', state.battleOptions.strategy);
  params.set('switch', state.battleOptions.allowSwitching ? '1' : '0');
  
  if (state.battleOptions.timeLimit) {
    params.set('time', state.battleOptions.timeLimit.toString());
  }
  
  if (state.battleOptions.energyAdvantage) {
    params.set('energy', state.battleOptions.energyAdvantage.toString());
  }
  
  if (state.battleOptions.moveTimingAdvantage) {
    params.set('timing', state.battleOptions.moveTimingAdvantage.toString());
  }
  
  if (state.battleOptions.scenario) {
    params.set('scenario', state.battleOptions.scenario);
  }
  
  if (state.sandboxMode) {
    params.set('sandbox', '1');
    
    // Encode sandbox actions
    if (state.sandboxActions.length > 0) {
      params.set('actions', JSON.stringify(state.sandboxActions));
    }
  }
  
  return params;
}

// Generate shareable URL
function generateShareableUrl(state: BattleState): string {
  let params: URLSearchParams;
  
  switch (state.battleMode) {
    case 'single':
      params = getSingleBattleUrlParams(state);
      break;
    case 'multi':
      params = getMultiBattleUrlParams(state);
      break;
    case 'matrix':
      params = getMatrixBattleUrlParams(state);
      break;
  }
  
  return `${window.location.origin}/battle?${params.toString()}`;
}
```

## Error Handling

We'll implement comprehensive error handling for all battle operations:

```typescript
// Battle error types
type BattleErrorType = 
  | 'POKEMON_NOT_FOUND'
  | 'MOVE_NOT_FOUND'
  | 'INVALID_CP'
  | 'SIMULATION_FAILED'
  | 'NETWORK_ERROR'
  | 'INVALID_PARAMETERS';

// Battle error class
class BattleError extends Error {
  constructor(
    public type: BattleErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'BattleError';
  }
}

// Error handling in battle store
const simulateBattle = async () => {
  const { pokemon1, pokemon2, battleOptions } = get();
  
  if (!pokemon1 || !pokemon2) {
    set({ error: 'Both Pokemon must be selected' });
    return;
  }
  
  set({ isSimulating: true, error: null });
  
  try {
    // API call
    // ...
  } catch (error) {
    console.error('Battle simulation error:', error);
    
    if (error instanceof BattleError) {
      switch (error.type) {
        case 'POKEMON_NOT_FOUND':
          set({ error: 'One or both Pokemon could not be found' });
          break;
        case 'MOVE_NOT_FOUND':
          set({ error: 'One or more moves could not be found' });
          break;
        case 'INVALID_CP':
          set({ error: 'Pokemon CP exceeds league limit' });
          break;
        case 'SIMULATION_FAILED':
          set({ error: 'Battle simulation failed: ' + error.message });
          break;
        case 'NETWORK_ERROR':
          set({ error: 'Network error: Could not connect to battle service' });
          break;
        default:
          set({ error: error.message || 'Battle simulation failed' });
      }
    } else {
      set({
        error: error instanceof Error ? error.message : 'Battle simulation failed',
      });
    }
    
    set({ isSimulating: false });
  }
};
```## Te
sting Strategy

We'll implement comprehensive testing for all battle page components and functionality:

1. **Unit Tests**:
   - Test battle calculations and algorithms
   - Test state management logic in the battle store
   - Test URL parameter encoding/decoding

2. **Component Tests**:
   - Test rendering of battle components
   - Test user interactions with battle controls
   - Test responsive layout

3. **Integration Tests**:
   - Test API endpoints for battle simulation
   - Test end-to-end battle workflow
   - Test URL state persistence

4. **Visual Regression Tests**:
   - Compare battle page layout with original PvPoke
   - Ensure consistent styling across different battle modes

## Implementation Approach

We'll implement the enhanced battle page in phases:

1. **Phase 1: Core Battle Page Structure**
   - Implement battle mode selector
   - Update battle options component
   - Enhance battle results display

2. **Phase 2: Single Battle Enhancements**
   - Implement enhanced battle timeline
   - Add sandbox mode
   - Add breakpoint/bulkpoint analysis
   - Add charged move tie analysis

3. **Phase 3: Multi-Battle Mode**
   - Implement multi-battle selector
   - Add battle histogram
   - Add sortable matchup list

4. **Phase 4: Matrix Battle Mode**
   - Implement matrix battle selector
   - Add matrix grid display
   - Add matrix export functionality

5. **Phase 5: URL State Management**
   - Implement comprehensive URL parameter handling
   - Add shareable link generation
   - Add state loading from URL

6. **Phase 6: Polish and Optimization**
   - Optimize performance for large battle simulations
   - Add loading states and error handling
   - Ensure responsive design for all screen sizes

This phased approach will allow us to incrementally enhance the battle page while maintaining a functional application throughout the development process.