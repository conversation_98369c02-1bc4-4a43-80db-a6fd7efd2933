import { League } from '@/types/pokemon';

export const LEAGUE_CONFIGS = {
  great: {
    name: 'Great League',
    cp: 1500,
    description: 'Pokemon with CP up to 1500',
  },
  ultra: {
    name: 'Ultra League',
    cp: 2500,
    description: 'Pokemon with CP up to 2500',
  },
  master: {
    name: 'Master League',
    cp: 10000,
    description: 'No CP limit',
  },
  little: {
    name: 'Little League',
    cp: 500,
    description: 'Pokemon with CP up to 500',
  },
  premier: {
    name: 'Premier League',
    cp: 2500,
    description: 'Premier Cup format',
  },
} as const;

/**
 * Get the CP limit for a given league
 */
export function getLeagueCP(league: League): number {
  return LEAGUE_CONFIGS[league].cp;
}

/**
 * Get the display name for a given league
 */
export function getLeagueName(league: League): string {
  return LEAGUE_CONFIGS[league].name;
}

/**
 * Get the description for a given league
 */
export function getLeagueDescription(league: League): string {
  return LEAGUE_CONFIGS[league].description;
}

/**
 * Check if a CP value is valid for a given league
 */
export function isValidForLeague(cp: number, league: League): boolean {
  return cp <= getLeagueCP(league);
}

/**
 * Get all available leagues
 */
export function getAllLeagues(): League[] {
  return Object.keys(LEAGUE_CONFIGS) as League[];
}
