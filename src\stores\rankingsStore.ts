import { create } from 'zustand';
import { 
  RankingEntry, 
  RankingFilters, 
  RankingCategory, 
  League, 
  LoadingState,
  PokemonType 
} from '@/types';

interface RankingsState {
  // State
  rankings: RankingEntry[];
  filteredRankings: RankingEntry[];
  filters: RankingFilters;
  loadingState: LoadingState<RankingEntry[]>;
  lastUpdated: string | null;
  
  // Pagination
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  
  // Actions
  loadRankings: (league: League, cup?: string) => Promise<void>;
  setFilters: (filters: Partial<RankingFilters>) => void;
  setCategory: (category: RankingCategory) => void;
  setLeague: (league: League) => void;
  setCup: (cup: string | undefined) => void;
  setSearchQuery: (query: string) => void;
  setPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  clearFilters: () => void;
  refreshRankings: () => Promise<void>;
}

const defaultFilters: RankingFilters = {
  league: 'great',
  cup: undefined,
  category: 'overall',
  types: undefined,
  tags: undefined,
  searchQuery: '',
  minRating: undefined,
  maxRating: undefined,
};

export const useRankingsStore = create<RankingsState>((set, get) => ({
  // Initial state
  rankings: [],
  filteredRankings: [],
  filters: defaultFilters,
  loadingState: { status: 'idle' },
  lastUpdated: null,
  currentPage: 1,
  itemsPerPage: 50,
  totalItems: 0,
  
  // Actions
  loadRankings: async (league, cup) => {
    set({ loadingState: { status: 'loading' } });
    
    try {
      // TODO: Implement actual rankings API call
      // For now, create mock rankings data
      const mockRankings: RankingEntry[] = [
        {
          pokemon: {
            speciesId: 'registeel',
            speciesName: 'Registeel',
            dex: 379,
            types: ['steel'],
            baseStats: { attack: 143, defense: 285, stamina: 190 },
            fastMoves: [],
            chargedMoves: [],
            tags: ['legendary'],
            buddyDistance: 20,
            thirdMoveCost: 100000,
            released: true,
          },
          score: 95.2,
          rank: 1,
          wins: 847,
          losses: 153,
          rating: 952,
          lead: 85.3,
          closer: 78.9,
          switch: 92.1,
          charger: 67.4,
          attacker: 45.2,
          consistency: 88.7,
          threats: [],
          counters: [],
        },
        {
          pokemon: {
            speciesId: 'cresselia',
            speciesName: 'Cresselia',
            dex: 488,
            types: ['psychic'],
            baseStats: { attack: 152, defense: 258, stamina: 260 },
            fastMoves: [],
            chargedMoves: [],
            tags: ['legendary'],
            buddyDistance: 20,
            thirdMoveCost: 100000,
            released: true,
          },
          score: 92.8,
          rank: 2,
          wins: 823,
          losses: 177,
          rating: 928,
          lead: 78.2,
          closer: 89.3,
          switch: 87.6,
          charger: 72.1,
          attacker: 52.8,
          consistency: 85.4,
          threats: [],
          counters: [],
        },
      ];
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      set({
        rankings: mockRankings,
        filteredRankings: mockRankings,
        loadingState: { status: 'success', data: mockRankings },
        lastUpdated: new Date().toISOString(),
        filters: { ...get().filters, league, cup },
        totalItems: mockRankings.length,
      });
      
      // Apply current filters
      get().setFilters({});
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load rankings';
      set({ loadingState: { status: 'error', error: errorMessage } });
    }
  },
  
  setFilters: (newFilters) => {
    const { rankings, filters } = get();
    const updatedFilters = { ...filters, ...newFilters };
    
    let filtered = [...rankings];
    
    // Apply search query
    if (updatedFilters.searchQuery && updatedFilters.searchQuery.trim()) {
      const query = updatedFilters.searchQuery.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.pokemon.speciesName.toLowerCase().includes(query) ||
        entry.pokemon.speciesId.toLowerCase().includes(query) ||
        entry.pokemon.types.some(type => type.toLowerCase().includes(query))
      );
    }
    
    // Apply type filters
    if (updatedFilters.types && updatedFilters.types.length > 0) {
      filtered = filtered.filter(entry =>
        updatedFilters.types!.some(type => entry.pokemon.types.includes(type))
      );
    }
    
    // Apply tag filters
    if (updatedFilters.tags && updatedFilters.tags.length > 0) {
      filtered = filtered.filter(entry =>
        updatedFilters.tags!.some(tag => entry.pokemon.tags.includes(tag))
      );
    }
    
    // Apply rating filters
    if (updatedFilters.minRating !== undefined) {
      filtered = filtered.filter(entry => entry.rating >= updatedFilters.minRating!);
    }
    
    if (updatedFilters.maxRating !== undefined) {
      filtered = filtered.filter(entry => entry.rating <= updatedFilters.maxRating!);
    }
    
    // Sort by category
    if (updatedFilters.category) {
      filtered.sort((a, b) => {
        switch (updatedFilters.category) {
          case 'leads':
            return b.lead - a.lead;
          case 'closers':
            return b.closer - a.closer;
          case 'switches':
            return b.switch - a.switch;
          case 'chargers':
            return b.charger - a.charger;
          case 'attackers':
            return b.attacker - a.attacker;
          case 'overall':
          default:
            return b.score - a.score;
        }
      });
    }
    
    set({
      filteredRankings: filtered,
      filters: updatedFilters,
      totalItems: filtered.length,
      currentPage: 1, // Reset to first page when filters change
    });
  },
  
  setCategory: (category) => {
    get().setFilters({ category });
  },
  
  setLeague: (league) => {
    get().loadRankings(league, get().filters.cup);
  },
  
  setCup: (cup) => {
    get().loadRankings(get().filters.league, cup);
  },
  
  setSearchQuery: (searchQuery) => {
    get().setFilters({ searchQuery });
  },
  
  setPage: (page) => {
    set({ currentPage: page });
  },
  
  setItemsPerPage: (itemsPerPage) => {
    set({ itemsPerPage, currentPage: 1 });
  },
  
  clearFilters: () => {
    const { rankings } = get();
    set({
      filters: { ...defaultFilters, league: get().filters.league },
      filteredRankings: rankings,
      totalItems: rankings.length,
      currentPage: 1,
    });
  },
  
  refreshRankings: async () => {
    const { filters } = get();
    await get().loadRankings(filters.league, filters.cup);
  },
}));
