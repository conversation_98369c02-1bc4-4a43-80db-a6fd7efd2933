import { Pokemon, Move, PokemonType, League } from '@/types/pokemon';
import { GameMasterDataSchema, PokemonSchema, MoveSchema } from '@/schemas/pokemon';
import { z } from 'zod';

// Raw GameMaster data structure (matches the actual JSON format)
interface RawGameMasterData {
  timestamp: string;
  settings: {
    partySize: number;
    maxBuffStages: number;
    buffDivisor: number;
  };
  pokemon: RawPokemon[];
  moves: RawMove[];
  types?: Record<string, Record<string, number>>;
  shadowPokemon: string[];
  cups: any[];
  rankingScenarios: any[];
  pokemonTags: string[];
  pokemonTraits: {
    pros: string[];
    cons: string[];
  };
  pokemonRegions: any[];
  formats: any[];
}

interface RawPokemon {
  dex: number;
  speciesName: string;
  speciesId: string;
  baseStats: {
    atk: number;
    def: number;
    hp: number;
  };
  types: string[];
  fastMoves: string[];
  chargedMoves: string[];
  tags: string[];
  defaultIVs?: {
    cp500?: number[];
    cp1500?: number[];
    cp2500?: number[];
  };
  level25CP?: number;
  buddyDistance: number;
  thirdMoveCost: number;
  released: boolean;
  family?: {
    id: string;
    evolutions?: string[];
  };
}

interface RawMove {
  moveId: string;
  name: string;
  abbreviation?: string;
  type: string;
  power: number;
  energy: number;
  energyGain: number;
  cooldown: number;
  archetype: string;
  turns?: number;
  buffs?: number[];
  buffTarget?: string;
  buffApplyChance?: string;
}

export interface GameMasterData {
  pokemon: Pokemon[];
  moves: Move[];
  types: Record<string, Record<string, number>>;
  shadowPokemon: string[];
  timestamp: string;
  settings: {
    partySize: number;
    maxBuffStages: number;
    buffDivisor: number;
  };
}

export interface PokemonServiceConfig {
  gameMasterUrl?: string;
  enableCaching?: boolean;
  cacheExpiry?: number; // milliseconds
  validateData?: boolean;
}

class PokemonService {
  private gamemaster: GameMasterData | null = null;
  private isLoading = false;
  private loadPromise: Promise<GameMasterData> | null = null;
  private config: Required<PokemonServiceConfig>;
  private cache = new Map<string, { data: any; timestamp: number }>();

  constructor(config: PokemonServiceConfig = {}) {
    this.config = {
      gameMasterUrl: '/api/gamemaster',
      enableCaching: true,
      cacheExpiry: 5 * 60 * 1000, // 5 minutes
      validateData: true,
      ...config,
    };
  }

  async loadGameMaster(): Promise<GameMasterData> {
    // Return cached data if available
    if (this.gamemaster) {
      return this.gamemaster;
    }

    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.isLoading = true;
    this.loadPromise = this.fetchGameMasterData();

    try {
      const data = await this.loadPromise;
      this.gamemaster = data;
      return data;
    } finally {
      this.isLoading = false;
      this.loadPromise = null;
    }
  }

  private async fetchGameMasterData(): Promise<GameMasterData> {
    try {
      // Try to load from cache first
      if (this.config.enableCaching) {
        const cached = this.getFromCache<GameMasterData>('gamemaster');
        if (cached) {
          return cached;
        }
      }

      // For now, return comprehensive mock data that matches the real structure
      // In production, this would fetch from the actual API
      const mockData = await this.createMockGameMasterData();

      if (this.config.validateData) {
        // Validate the data structure
        this.validateGameMasterData(mockData);
      }

      // Cache the data
      if (this.config.enableCaching) {
        this.setCache('gamemaster', mockData);
      }

      return mockData;
    } catch (error) {
      console.error('Failed to load GameMaster data:', error);
      throw new Error(`Failed to load Pokemon data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async createMockGameMasterData(): Promise<GameMasterData> {
    // In production, this would load from the actual gamemaster.json file
    // For now, we'll create a realistic mock that matches the actual structure

    try {
      // Try to load actual data first (this would be the real implementation)
      const response = await fetch('/data/gamemaster.json');
      if (response.ok) {
        const rawData: RawGameMasterData = await response.json();
        return this.transformRawGameMasterData(rawData);
      }
    } catch (error) {
      console.warn('Could not load actual gamemaster data, using mock data:', error);
    }

    // Fallback to mock data
    const mockMoves: Move[] = [
      {
        moveId: 'FIRE_FANG',
        name: 'Fire Fang',
        type: 'fire',
        power: 12,
        energy: 8,
        cooldown: 2,
        archetype: 'High Energy',
      },
      {
        moveId: 'DRAGON_CLAW',
        name: 'Dragon Claw',
        type: 'dragon',
        power: 50,
        energy: -35,
        cooldown: 0,
        archetype: 'Low Energy',
      },
      {
        moveId: 'WATER_GUN',
        name: 'Water Gun',
        type: 'water',
        power: 5,
        energy: 5,
        cooldown: 1,
        archetype: 'High Energy',
      },
      {
        moveId: 'HYDRO_CANNON',
        name: 'Hydro Cannon',
        type: 'water',
        power: 80,
        energy: -40,
        cooldown: 0,
        archetype: 'Nuke',
      },
    ];

    const mockPokemon: Pokemon[] = [
      {
        speciesId: 'charizard',
        speciesName: 'Charizard',
        dex: 6,
        types: ['fire', 'flying'],
        baseStats: {
          attack: 223,
          defense: 173,
          stamina: 186,
        },
        fastMoves: [mockMoves[0]], // Fire Fang
        chargedMoves: [mockMoves[1]], // Dragon Claw
        tags: ['starter'],
        buddyDistance: 3,
        thirdMoveCost: 75000,
        released: true,
      },
      {
        speciesId: 'blastoise',
        speciesName: 'Blastoise',
        dex: 9,
        types: ['water'],
        baseStats: {
          attack: 171,
          defense: 207,
          stamina: 188,
        },
        fastMoves: [mockMoves[2]], // Water Gun
        chargedMoves: [mockMoves[3]], // Hydro Cannon
        tags: ['starter'],
        buddyDistance: 3,
        thirdMoveCost: 75000,
        released: true,
      },
      {
        speciesId: 'registeel',
        speciesName: 'Registeel',
        dex: 379,
        types: ['steel'],
        baseStats: {
          attack: 143,
          defense: 285,
          stamina: 190,
        },
        fastMoves: [mockMoves[0]], // Fire Fang (can learn via Elite TM)
        chargedMoves: [mockMoves[1]], // Dragon Claw (can learn via Elite TM)
        tags: ['legendary'],
        buddyDistance: 20,
        thirdMoveCost: 100000,
        released: true,
      },
    ];

    return {
      pokemon: mockPokemon,
      moves: mockMoves,
      types: this.getTypeEffectivenessChart(),
      shadowPokemon: ['charizard', 'blastoise'], // Mock shadow Pokemon list
      timestamp: new Date().toISOString(),
      settings: {
        partySize: 3,
        maxBuffStages: 4,
        buffDivisor: 4,
      },
    };
  }

  private transformRawGameMasterData(rawData: RawGameMasterData): GameMasterData {
    // Transform raw Pokemon data to our typed format
    const pokemon: Pokemon[] = rawData.pokemon.map(rawPokemon => this.transformRawPokemon(rawPokemon, rawData.moves));

    // Transform raw moves data to our typed format
    const moves: Move[] = rawData.moves.map(rawMove => this.transformRawMove(rawMove));

    return {
      pokemon,
      moves,
      types: this.getTypeEffectivenessChart(),
      shadowPokemon: rawData.shadowPokemon || [],
      timestamp: rawData.timestamp || new Date().toISOString(),
      settings: rawData.settings,
    };
  }

  private transformRawPokemon(rawPokemon: RawPokemon, rawMoves: RawMove[]): Pokemon {
    // Create a lookup map for moves
    const moveMap = new Map(rawMoves.map(move => [move.moveId, this.transformRawMove(move)]));

    // Get the actual move objects for fast and charged moves
    const fastMoves = rawPokemon.fastMoves
      .map(moveId => moveMap.get(moveId))
      .filter((move): move is Move => move !== undefined);

    const chargedMoves = rawPokemon.chargedMoves
      .map(moveId => moveMap.get(moveId))
      .filter((move): move is Move => move !== undefined);

    return {
      speciesId: rawPokemon.speciesId,
      speciesName: rawPokemon.speciesName,
      dex: rawPokemon.dex,
      types: rawPokemon.types as PokemonType[],
      baseStats: {
        attack: rawPokemon.baseStats.atk,
        defense: rawPokemon.baseStats.def,
        stamina: rawPokemon.baseStats.hp,
      },
      fastMoves,
      chargedMoves,
      tags: rawPokemon.tags,
      buddyDistance: rawPokemon.buddyDistance,
      thirdMoveCost: rawPokemon.thirdMoveCost,
      released: rawPokemon.released,
    };
  }

  private transformRawMove(rawMove: RawMove): Move {
    const move: Move = {
      moveId: rawMove.moveId,
      name: rawMove.name,
      type: rawMove.type as PokemonType,
      power: rawMove.power,
      energy: rawMove.energy,
      cooldown: rawMove.cooldown,
      archetype: rawMove.archetype,
    };

    // Add energy gain for fast moves
    if (rawMove.energyGain) {
      move.energyGain = rawMove.energyGain;
    }

    // Add buffs if present
    if (rawMove.buffs && rawMove.buffTarget && rawMove.buffApplyChance) {
      move.buffs = [{
        target: rawMove.buffTarget as 'self' | 'opponent',
        stat: rawMove.buffs[0] !== 0 ? 'attack' : 'defense',
        stages: rawMove.buffs[0] || rawMove.buffs[1] || 0,
        chance: parseFloat(rawMove.buffApplyChance),
      }];
    }

    return move;
  }

  private getTypeEffectivenessChart(): Record<string, Record<string, number>> {
    // Complete Pokemon GO type effectiveness chart
    return {
      normal: {
        rock: 0.5, ghost: 0, steel: 0.5
      },
      fire: {
        fire: 0.5, water: 0.5, grass: 2, ice: 2, bug: 2, rock: 0.5, dragon: 0.5, steel: 2
      },
      water: {
        fire: 2, water: 0.5, grass: 0.5, ground: 2, rock: 2, dragon: 0.5
      },
      electric: {
        water: 2, electric: 0.5, grass: 0.5, ground: 0, flying: 2, dragon: 0.5
      },
      grass: {
        fire: 0.5, water: 2, grass: 0.5, poison: 0.5, ground: 2, flying: 0.5, bug: 0.5, rock: 2, dragon: 0.5, steel: 0.5
      },
      ice: {
        fire: 0.5, water: 0.5, grass: 2, ice: 0.5, ground: 2, flying: 2, dragon: 2, steel: 0.5
      },
      fighting: {
        normal: 2, ice: 2, poison: 0.5, psychic: 0.5, bug: 0.5, rock: 2, ghost: 0, dark: 2, steel: 2, fairy: 0.5, flying: 0.5
      },
      poison: {
        grass: 2, poison: 0.5, ground: 0.5, rock: 0.5, ghost: 0.5, steel: 0, fairy: 2
      },
      ground: {
        fire: 2, electric: 2, grass: 0.5, poison: 2, flying: 0, bug: 0.5, rock: 2, steel: 2
      },
      flying: {
        electric: 0.5, grass: 2, ice: 0.5, fighting: 2, bug: 2, rock: 0.5, steel: 0.5
      },
      psychic: {
        fighting: 2, poison: 2, psychic: 0.5, dark: 0, steel: 0.5
      },
      bug: {
        fire: 0.5, grass: 2, fighting: 0.5, poison: 0.5, psychic: 2, flying: 0.5, ghost: 0.5, dark: 2, steel: 0.5, fairy: 0.5
      },
      rock: {
        fire: 2, ice: 2, fighting: 0.5, ground: 0.5, flying: 2, bug: 2, steel: 0.5
      },
      ghost: {
        normal: 0, psychic: 2, ghost: 2, dark: 0.5
      },
      dragon: {
        dragon: 2, steel: 0.5, fairy: 0
      },
      dark: {
        fighting: 0.5, psychic: 2, ghost: 2, dark: 0.5, fairy: 0.5
      },
      steel: {
        fire: 0.5, water: 0.5, electric: 0.5, ice: 2, rock: 2, steel: 0.5, fairy: 2
      },
      fairy: {
        fire: 0.5, fighting: 2, poison: 0.5, dragon: 2, dark: 2, steel: 0.5
      }
    };
  }

  private validateGameMasterData(data: GameMasterData): void {
    try {
      // Validate the overall structure
      if (!data.pokemon || !Array.isArray(data.pokemon)) {
        throw new Error('Invalid pokemon data structure');
      }

      if (!data.moves || !Array.isArray(data.moves)) {
        throw new Error('Invalid moves data structure');
      }

      // Validate a sample of Pokemon and moves using Zod schemas
      if (data.pokemon.length > 0) {
        const samplePokemon = data.pokemon.slice(0, 3);
        samplePokemon.forEach((pokemon, index) => {
          try {
            PokemonSchema.parse(pokemon);
          } catch (error) {
            console.warn(`Pokemon validation failed for index ${index}:`, error);
          }
        });
      }

      if (data.moves.length > 0) {
        const sampleMoves = data.moves.slice(0, 3);
        sampleMoves.forEach((move, index) => {
          try {
            MoveSchema.parse(move);
          } catch (error) {
            console.warn(`Move validation failed for index ${index}:`, error);
          }
        });
      }
    } catch (error) {
      console.error('GameMaster data validation failed:', error);
      throw error;
    }
  }

  private isDataFresh(): boolean {
    if (!this.gamemaster) return false;

    const cached = this.getFromCache('gamemaster');
    return cached !== null;
  }

  private getFromCache<T>(key: string): T | null {
    if (!this.config.enableCaching) return null;

    const cached = this.cache.get(key);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > this.config.cacheExpiry;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  private setCache<T>(key: string, data: T): void {
    if (!this.config.enableCaching) return;

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  // Public API methods with comprehensive error handling and caching

  async getPokemonBySpecies(speciesId: string): Promise<Pokemon | undefined> {
    try {
      const cacheKey = `pokemon:${speciesId}`;
      const cached = this.getFromCache<Pokemon>(cacheKey);
      if (cached) return cached;

      const gamemaster = await this.loadGameMaster();
      const pokemon = gamemaster.pokemon.find(p => p.speciesId === speciesId);

      if (pokemon && this.config.enableCaching) {
        this.setCache(cacheKey, pokemon);
      }

      return pokemon;
    } catch (error) {
      console.error(`Failed to get Pokemon by species ${speciesId}:`, error);
      return undefined;
    }
  }

  async getMoveById(moveId: string): Promise<Move | undefined> {
    try {
      const cacheKey = `move:${moveId}`;
      const cached = this.getFromCache<Move>(cacheKey);
      if (cached) return cached;

      const gamemaster = await this.loadGameMaster();
      const move = gamemaster.moves.find(m => m.moveId === moveId);

      if (move && this.config.enableCaching) {
        this.setCache(cacheKey, move);
      }

      return move;
    } catch (error) {
      console.error(`Failed to get move by ID ${moveId}:`, error);
      return undefined;
    }
  }

  async getAllPokemon(): Promise<Pokemon[]> {
    try {
      const gamemaster = await this.loadGameMaster();
      return [...gamemaster.pokemon]; // Return a copy to prevent mutations
    } catch (error) {
      console.error('Failed to get all Pokemon:', error);
      return [];
    }
  }

  async getAllMoves(): Promise<Move[]> {
    try {
      const gamemaster = await this.loadGameMaster();
      return [...gamemaster.moves]; // Return a copy to prevent mutations
    } catch (error) {
      console.error('Failed to get all moves:', error);
      return [];
    }
  }

  async getTypeEffectiveness(
    attackingType: PokemonType,
    defendingTypes: PokemonType[]
  ): Promise<number> {
    try {
      const gamemaster = await this.loadGameMaster();

      let effectiveness = 1.0;

      for (const defendingType of defendingTypes) {
        const typeChart = gamemaster.types[attackingType];
        if (typeChart && typeChart[defendingType] !== undefined) {
          effectiveness *= typeChart[defendingType];
        }
      }

      return effectiveness;
    } catch (error) {
      console.error(`Failed to get type effectiveness for ${attackingType} vs ${defendingTypes}:`, error);
      return 1.0; // Return neutral effectiveness on error
    }
  }

  async searchPokemon(query: string, options: {
    limit?: number;
    includeUnreleased?: boolean;
    types?: PokemonType[];
    tags?: string[];
  } = {}): Promise<Pokemon[]> {
    try {
      const { limit = 50, includeUnreleased = false, types, tags } = options;
      const allPokemon = await this.getAllPokemon();
      const lowercaseQuery = query.toLowerCase().trim();

      let filtered = allPokemon;

      // Filter by release status
      if (!includeUnreleased) {
        filtered = filtered.filter(pokemon => pokemon.released);
      }

      // Filter by types if specified
      if (types && types.length > 0) {
        filtered = filtered.filter(pokemon =>
          types.some(type => pokemon.types.includes(type))
        );
      }

      // Filter by tags if specified
      if (tags && tags.length > 0) {
        filtered = filtered.filter(pokemon =>
          tags.some(tag => pokemon.tags.includes(tag))
        );
      }

      // Apply search query
      if (lowercaseQuery) {
        filtered = filtered.filter(pokemon =>
          pokemon.speciesName.toLowerCase().includes(lowercaseQuery) ||
          pokemon.speciesId.toLowerCase().includes(lowercaseQuery) ||
          pokemon.types.some(type => type.toLowerCase().includes(lowercaseQuery)) ||
          pokemon.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
        );
      }

      // Sort by relevance (exact matches first, then partial matches)
      if (lowercaseQuery) {
        filtered.sort((a, b) => {
          const aExact = a.speciesName.toLowerCase() === lowercaseQuery ? 1 : 0;
          const bExact = b.speciesName.toLowerCase() === lowercaseQuery ? 1 : 0;
          if (aExact !== bExact) return bExact - aExact;

          const aStarts = a.speciesName.toLowerCase().startsWith(lowercaseQuery) ? 1 : 0;
          const bStarts = b.speciesName.toLowerCase().startsWith(lowercaseQuery) ? 1 : 0;
          if (aStarts !== bStarts) return bStarts - aStarts;

          return a.speciesName.localeCompare(b.speciesName);
        });
      } else {
        // Sort alphabetically if no query
        filtered.sort((a, b) => a.speciesName.localeCompare(b.speciesName));
      }

      return filtered.slice(0, limit);
    } catch (error) {
      console.error(`Failed to search Pokemon with query "${query}":`, error);
      return [];
    }
  }

  async getPokemonByFamily(familyId: string): Promise<Pokemon[]> {
    try {
      const allPokemon = await this.getAllPokemon();
      return allPokemon.filter(pokemon =>
        pokemon.tags.includes(`family:${familyId}`)
      );
    } catch (error) {
      console.error(`Failed to get Pokemon by family ${familyId}:`, error);
      return [];
    }
  }

  async getPokemonByDex(dex: number): Promise<Pokemon[]> {
    try {
      const allPokemon = await this.getAllPokemon();
      return allPokemon.filter(pokemon => pokemon.dex === dex);
    } catch (error) {
      console.error(`Failed to get Pokemon by dex ${dex}:`, error);
      return [];
    }
  }

  async getPokemonByType(type: PokemonType): Promise<Pokemon[]> {
    try {
      const allPokemon = await this.getAllPokemon();
      return allPokemon.filter(pokemon => pokemon.types.includes(type));
    } catch (error) {
      console.error(`Failed to get Pokemon by type ${type}:`, error);
      return [];
    }
  }

  async getMovesByType(type: PokemonType): Promise<Move[]> {
    try {
      const allMoves = await this.getAllMoves();
      return allMoves.filter(move => move.type === type);
    } catch (error) {
      console.error(`Failed to get moves by type ${type}:`, error);
      return [];
    }
  }

  async getShadowPokemon(): Promise<string[]> {
    try {
      const gamemaster = await this.loadGameMaster();
      return [...gamemaster.shadowPokemon];
    } catch (error) {
      console.error('Failed to get shadow Pokemon list:', error);
      return [];
    }
  }

  async isPokemonShadowEligible(speciesId: string): Promise<boolean> {
    try {
      const shadowPokemon = await this.getShadowPokemon();
      return shadowPokemon.includes(speciesId.replace('_shadow', ''));
    } catch (error) {
      console.error(`Failed to check shadow eligibility for ${speciesId}:`, error);
      return false;
    }
  }

  // Utility methods for data management
  async refreshData(): Promise<void> {
    this.gamemaster = null;
    this.cache.clear();
    await this.loadGameMaster();
  }

  clearCache(): void {
    this.cache.clear();
  }

  getDataTimestamp(): string | null {
    return this.gamemaster?.timestamp || null;
  }

  isLoaded(): boolean {
    return this.gamemaster !== null;
  }

  getStats(): {
    pokemonCount: number;
    moveCount: number;
    shadowPokemonCount: number;
    cacheSize: number;
    lastUpdated: string | null;
  } {
    return {
      pokemonCount: this.gamemaster?.pokemon.length || 0,
      moveCount: this.gamemaster?.moves.length || 0,
      shadowPokemonCount: this.gamemaster?.shadowPokemon.length || 0,
      cacheSize: this.cache.size,
      lastUpdated: this.gamemaster?.timestamp || null,
    };
  }
}

// Export singleton instance with default configuration
export const pokemonService = new PokemonService();

// Export the class for custom configurations
export { PokemonService };
