import type { NextApiRequest, NextApiResponse } from 'next';
import { pokemonService } from '@/services/pokemonService';
import type { ApiResponse } from '@/types/api';
import type { RankingEntry, League, RankingCategory } from '@/types/rankings';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<RankingEntry[]>>
) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'Only GET requests are allowed',
        timestamp: new Date().toISOString(),
      },
    });
    return;
  }

  try {
    const { league } = req.query;
    const {
      category = 'overall',
      cup,
      page = '1',
      limit = '50',
      types,
      tags,
      minRating,
      maxRating,
    } = req.query;

    // Validate league parameter
    const validLeagues: League[] = ['great', 'ultra', 'master', 'little', 'premier'];
    if (!league || typeof league !== 'string' || !validLeagues.includes(league as League)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_LEAGUE',
          message: `League must be one of: ${validLeagues.join(', ')}`,
          timestamp: new Date().toISOString(),
        },
      });
      return;
    }

    // Parse query parameters
    const pageNum = parseInt(page as string) || 1;
    const limitNum = Math.min(parseInt(limit as string) || 50, 100);
    const categoryFilter = category as RankingCategory;
    const typeFilters = types ? (Array.isArray(types) ? types : [types]) : undefined;
    const tagFilters = tags ? (Array.isArray(tags) ? tags : [tags]) : undefined;
    const minRatingNum = minRating ? parseFloat(minRating as string) : undefined;
    const maxRatingNum = maxRating ? parseFloat(maxRating as string) : undefined;

    // Get all Pokemon for rankings
    const allPokemon = await pokemonService.getAllPokemon();
    
    // Create mock rankings data
    const rankings: RankingEntry[] = allPokemon
      .filter(pokemon => pokemon.released) // Only released Pokemon
      .map((pokemon, index) => {
        // Generate mock ranking data
        const baseScore = Math.max(0, 100 - (index * 2) + (Math.random() * 10 - 5));
        const wins = Math.floor(800 + Math.random() * 200);
        const losses = Math.floor(200 + Math.random() * 100);
        const rating = Math.floor(baseScore * 10);
        
        return {
          pokemon,
          score: Math.round(baseScore * 10) / 10,
          rank: index + 1,
          wins,
          losses,
          rating,
          lead: Math.round((baseScore + Math.random() * 20 - 10) * 10) / 10,
          closer: Math.round((baseScore + Math.random() * 20 - 10) * 10) / 10,
          switch: Math.round((baseScore + Math.random() * 20 - 10) * 10) / 10,
          charger: Math.round((baseScore + Math.random() * 20 - 10) * 10) / 10,
          attacker: Math.round((baseScore + Math.random() * 20 - 10) * 10) / 10,
          consistency: Math.round((baseScore + Math.random() * 10 - 5) * 10) / 10,
          threats: [], // Mock empty for now
          counters: [], // Mock empty for now
        };
      });

    // Apply filters
    let filteredRankings = rankings;

    // Filter by types
    if (typeFilters && typeFilters.length > 0) {
      filteredRankings = filteredRankings.filter(entry =>
        typeFilters.some(type => entry.pokemon.types.includes(type as any))
      );
    }

    // Filter by tags
    if (tagFilters && tagFilters.length > 0) {
      filteredRankings = filteredRankings.filter(entry =>
        tagFilters.some(tag => entry.pokemon.tags.includes(tag))
      );
    }

    // Filter by rating range
    if (minRatingNum !== undefined) {
      filteredRankings = filteredRankings.filter(entry => entry.rating >= minRatingNum);
    }

    if (maxRatingNum !== undefined) {
      filteredRankings = filteredRankings.filter(entry => entry.rating <= maxRatingNum);
    }

    // Sort by category
    switch (categoryFilter) {
      case 'leads':
        filteredRankings.sort((a, b) => b.lead - a.lead);
        break;
      case 'closers':
        filteredRankings.sort((a, b) => b.closer - a.closer);
        break;
      case 'switches':
        filteredRankings.sort((a, b) => b.switch - a.switch);
        break;
      case 'chargers':
        filteredRankings.sort((a, b) => b.charger - a.charger);
        break;
      case 'attackers':
        filteredRankings.sort((a, b) => b.attacker - a.attacker);
        break;
      case 'overall':
      default:
        filteredRankings.sort((a, b) => b.score - a.score);
        break;
    }

    // Update ranks after sorting
    filteredRankings.forEach((entry, index) => {
      entry.rank = index + 1;
    });

    // Apply pagination
    const startIndex = (pageNum - 1) * limitNum;
    const paginatedRankings = filteredRankings.slice(startIndex, startIndex + limitNum);

    res.status(200).json({
      success: true,
      data: paginatedRankings,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        requestId: Math.random().toString(36).substr(2, 9),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: filteredRankings.length,
          totalPages: Math.ceil(filteredRankings.length / limitNum),
          hasNext: startIndex + limitNum < filteredRankings.length,
          hasPrev: pageNum > 1,
        },
      },
    });

  } catch (error) {
    console.error(`Rankings API error for league ${req.query.league}:`, error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch rankings data',
        details: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : 'Unknown error',
        } : undefined,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
