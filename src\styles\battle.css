/* Battle page styles - exact copy from original PvPoke */

/* Base body styling from original */
body {
  background-color: #000 !important;
  background: url("/img/themes/sunflower/sunflower-bg.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  background-position: center bottom;
  min-height: 100%;
  margin: 0;
  font-family: Arial, sans-serif;
  color: #000;
}

/* Main container */
main {
  max-width: 800px;
  margin: 0 auto;
  padding: 50px 10px 100px 10px;
}

/* Header styling */
h1 {
  text-align: center;
  font-size: 2.5em;
  font-weight: bold;
  margin: 20px 0;
  color: #000;
}

/* Section styling - exact from original */
.section {
  margin: 10px 0;
  height: auto !important;
}

.section.white {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 8px;
}

/* League select container */
.league-select-container {
  text-align: center;
}

/* Mode selector - exact from original */
.ranking-categories {
  display: flex;
  justify-content: center;
  margin: 15px 0;
  flex-wrap: wrap;
}

.ranking-categories a {
  text-decoration: none;
  color: #003462;
  font-size: 14px;
  padding: 8px 10px;
  margin: 0 2px;
  border-radius: 30px;
  display: inline-block;
  -webkit-transition: background-color 0.2s ease-out 0s;
  -moz-transition: background-color 0.2s ease-out 0s;
  -o-transition: background-color 0.2s ease-out 0s;
  transition: background-color 0.2s ease-out 0s;
  cursor: pointer;
}

.ranking-categories a.selected,
.ranking-categories a:hover {
  background: #c4def5;
  font-weight: bold;
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}

/* Description text */
.description {
  text-align: center;
  margin: 10px 0;
  color: #666;
  line-height: 1.5;
  font-size: 14px;
}

.hide {
  display: none;
}

.clear {
  clear: both;
}

/* Pokemon selection container - exact from original */
.poke-select-container {
  display: flex;
  justify-content: space-between;
  max-width: 800px;
  margin: 0 auto;
}

/* Pokemon container */
.poke {
  position: relative;
  width: 48%;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.poke.single {
  display: block;
}

.poke.multi {
  display: none;
  width: 100%;
}

/* Random and Swap buttons */
.poke a.random,
.poke a.swap {
  display: inline-block;
  font-size: 12px;
  color: #003462;
  text-decoration: none;
  margin: 5px 0;
}

.poke a.random:hover,
.poke a.swap:hover {
  text-decoration: underline;
}

/* Pokemon search container */
.poke-search-container {
  position: relative;
  margin-bottom: 5px;
}

.poke-search {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-info {
  position: absolute;
  right: 8px;
  top: 8px;
  color: #003462;
  text-decoration: none;
  font-weight: bold;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background: #c4def5;
  border-radius: 50%;
}

/* Pokemon select dropdown */
.poke-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 5px;
}

/* Form select container */
.form-select-container {
  position: relative;
  margin-bottom: 10px;
  display: none;
}

.form-select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Single mode Pokemon selection */
.poke-select-container.single .poke:nth-of-type(1) a.random {
  float: left;
}

.poke-select-container.single .poke:nth-of-type(1) a.swap {
  float: right;
}

.poke-select-container.single .poke:nth-of-type(2) {
  display: block;
}

.poke-select-container.single .poke:nth-of-type(2) a.random {
  float: right;
}

.poke-select-container.single .poke:nth-of-type(2) a.swap {
  float: left;
}

.poke-select-container.single .poke:nth-of-type(3) {
  display: none;
}

/* Multi mode Pokemon selection */
.poke-select-container.multi .poke:nth-of-type(2),
.poke-select-container.multi .hp.bar-back,
.poke-select-container.multi .move-bars {
  display: none;
}

.poke-select-container.multi .poke:nth-of-type(3) {
  display: block;
}

.poke-select-container.multi a.swap {
  display: none;
}

/* Matrix mode Pokemon selection */
.poke-select-container.matrix .poke.single,
.poke-select-container.matrix .hp.bar-back,
.poke-select-container.matrix .move-bars {
  display: none;
}

.poke-select-container.matrix .poke.multi {
  display: block;
  float: left;
  width: 48%;
}

.poke-select-container.matrix .poke.multi:nth-of-type(2) {
  float: right;
}

.poke-select-container.matrix .poke.multi .format-select,
.poke-select-container.matrix .poke.multi .cup-select {
  display: none;
}

.poke-select-container.matrix .poke.multi .custom-options {
  display: block;
}

.poke-select-container.matrix .poke.multi .multi-battle-options {
  display: none;
}

/* Pokemon stats container - exact from original */
.poke .poke-stats {
  box-sizing: border-box;
  border-radius: 8px;
  padding: 5px;
  max-width: 200px;
  background: rgba(255, 255, 255, 0.9);
  display: block;
}

.poke .poke-stats .stat-container {
  font-size: 10px;
  color: #111;
}

.poke .poke-stats .stat-container .stat-label {
  width: 80px;
  float: left;
}

.poke .poke-stats .stat-container .stat-label .label-name {
  display: inline-block;
  width: 35px;
}

.poke .poke-stats .stat-container .stat-label .stat {
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
}

.poke .poke-stats .stat-container .stat-label .stat.buff {
  color: #f1841c;
}

.poke .poke-stats .stat-container .stat-label .stat.debuff {
  color: #0158d0;
}

.poke .poke-stats .stat-container.overall .stat-label {
  width: auto;
  margin-top: 5px;
  border-top: 1px solid #666;
  padding-top: 5px;
}

.poke .poke-stats .stat-container .bar-back {
  width: 100px;
  height: 4px;
  border-radius: 8px;
  float: left;
}

.poke .poke-stats .stat-container .bar-back .bar {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  margin-top: 7px;
  background: rgba(0, 0, 0, 0.6);
  -webkit-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  -moz-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  -o-transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
  transition: width 150ms cubic-bezier(0.47, 0, 0.745, 0.715) 0s;
}

.poke .poke-stats .types {
  margin-bottom: 3px;
  text-align: center;
}

.poke .poke-stats .types .type {
  display: inline-block;
  border-radius: 4px;
  padding: 2px 8px;
  margin: 0 2px;
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.poke .poke-stats h3 {
  margin: 0;
  font-weight: normal;
}

.poke .poke-stats h3.cp {
  text-align: center;
}

.poke .poke-stats h3.cp .identifier {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 20px;
  background: #9e34ef;
}

.poke .poke-stats h3.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #111;
  margin-top: 20px;
}

.poke .poke-stats a {
  font-size: 14px;
  text-decoration: none;
  color: #003462;
  display: block;
  margin: 5px 0;
}

.poke .poke-stats a.clear-selection {
  margin-top: 10px;
}

.poke .poke-stats a:hover {
  text-decoration: underline;
}

/* Pokemon type colors - exact from original */
.normal {
  background: #a3a49e; /* Old browsers */
  background: -moz-linear-gradient(top, #909aa3 30%, #a3a49e 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #909aa3 30%, #a3a49e 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #909aa3 30%, #a3a49e 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fighting {
  background: #e12c2c; /* Old browsers */
  background: -moz-linear-gradient(top, #cf3030 30%, #e12c2c 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #cf3030 30%, #e12c2c 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #cf3030 30%, #e12c2c 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.flying {
  background: #9180c4; /* Old browsers */
  background: -moz-linear-gradient(top, #9785d5 30%, #9180c4 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #9785d5 30%, #9180c4 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #9785d5 30%, #9180c4 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.poison {
  background: #9e69c9; /* Old browsers */
  background: -moz-linear-gradient(top, #a55ebd 30%, #9e69c9 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #a55ebd 30%, #9e69c9 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #a55ebd 30%, #9e69c9 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ground {
  background: #d97746; /* Old browsers */
  background: -moz-linear-gradient(top, #ca8d52 30%, #d97746 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ca8d52 30%, #d97746 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ca8d52 30%, #d97746 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.rock {
  background: #b7a66e; /* Old browsers */
  background: -moz-linear-gradient(top, #b7985a 30%, #b7a66e 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #b7985a 30%, #b7a66e 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #b7985a 30%, #b7a66e 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.bug {
  background: #9bbc0f; /* Old browsers */
  background: -moz-linear-gradient(top, #a9c520 30%, #9bbc0f 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #a9c520 30%, #9bbc0f 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #a9c520 30%, #9bbc0f 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ghost {
  background: #644e88; /* Old browsers */
  background: -moz-linear-gradient(top, #554374 30%, #644e88 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #554374 30%, #644e88 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #554374 30%, #644e88 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.steel {
  background: #a7a8ce; /* Old browsers */
  background: -moz-linear-gradient(top, #8a8ac0 30%, #a7a8ce 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #8a8ac0 30%, #a7a8ce 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #8a8ac0 30%, #a7a8ce 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fire {
  background: #feb04b; /* Old browsers */
  background: -moz-linear-gradient(top, #fe9d59 30%, #feb04b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #fe9d59 30%, #feb04b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #fe9d59 30%, #feb04b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.water {
  background: #6ac7e9; /* Old browsers */
  background: -moz-linear-gradient(top, #4f91db 30%, #6ac7e9 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #4f91db 30%, #6ac7e9 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #4f91db 30%, #6ac7e9 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.grass {
  background: #49d0b0; /* Old browsers */
  background: -moz-linear-gradient(top, #5ac178 30%, #49d0b0 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #5ac178 30%, #49d0b0 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #5ac178 30%, #49d0b0 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.electric {
  background: #ffdf00; /* Old browsers */
  background: -moz-linear-gradient(top, #fbd200 30%, #ffdf00 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #fbd200 30%, #ffdf00 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #fbd200 30%, #ffdf00 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.psychic {
  background: #ff6da6; /* Old browsers */
  background: -moz-linear-gradient(top, #f55792 30%, #ff6da6 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #f55792 30%, #ff6da6 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f55792 30%, #ff6da6 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.ice {
  background: #86d1f3; /* Old browsers */
  background: -moz-linear-gradient(top, #67c6eb 30%, #86d1f3 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #67c6eb 30%, #86d1f3 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #67c6eb 30%, #86d1f3 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.dragon {
  background: #7a62ea; /* Old browsers */
  background: -moz-linear-gradient(top, #7a62ea 30%, #7a62ea 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #7a62ea 30%, #7a62ea 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #7a62ea 30%, #7a62ea 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.dark {
  background: #5a5979; /* Old browsers */
  background: -moz-linear-gradient(top, #5a5366 30%, #5a5979 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #5a5366 30%, #5a5979 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #5a5366 30%, #5a5979 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.fairy {
  background: #fc9de7; /* Old browsers */
  background: -moz-linear-gradient(top, #f984e6 30%, #fc9de7 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #f984e6 30%, #fc9de7 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #f984e6 30%, #fc9de7 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

/* Battle section styling */
.section.battle {
  text-align: center;
}

/* Button styling - exact from original */
.button {
  position: relative;
  display: block;
  font-size: 18px;
  margin: 10px auto;
  border-radius: 30px;
  border: 2px solid #000;
  padding: 10px 18px;
  cursor: pointer;
  text-decoration: none;
  color: #000;
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
  -webkit-transition: box-shadow 0.25s ease-out 0s;
  -moz-transition: box-shadow 0.25s ease-out 0s;
  -o-transition: box-shadow 0.25s ease-out 0s;
  transition: box-shadow 0.25s ease-out 0s;
}

.button.update-btn {
  display: none;
}

.button span.btn-content-wrap {
  display: flex;
  align-items: center;
}

.button span.btn-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: 100%;
  margin-right: 8px;
}

.button span.btn-icon.btn-icon-battle {
  background-image: url("/img/themes/sunflower/nav-battle-blue.png");
}

.button span.btn-label {
  flex: 1;
}

.button:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  pointer-events: none;
  border-radius: 50px;
  -webkit-transition: background-color 0.25s ease-out 0s;
  -moz-transition: background-color 0.25s ease-out 0s;
  -o-transition: background-color 0.25s ease-out 0s;
  transition: background-color 0.25s ease-out 0s;
}

.button:hover {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
  -webkit-transition: box-shadow 0s ease-out 0s;
  -moz-transition: box-shadow 0s ease-out 0s;
  -o-transition: box-shadow 0s ease-out 0s;
  transition: box-shadow 0s ease-out 0s;
}

.button:hover:after {
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-transition: background-color 0s ease-out 0s;
  -moz-transition: background-color 0s ease-out 0s;
  -o-transition: background-color 0s ease-out 0s;
  transition: background-color 0s ease-out 0s;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Tooltip */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  display: none;
}

/* Battle Results - exact from original */
.battle-results {
  display: none;
  text-align: center;
}

.battle-results.single {
  display: block;
}

.battle-results .timeline-container {
  max-width: 95%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.5);
  height: 80px;
  border-radius: 8px;
  border: 1px solid #000;
  position: relative;
  overflow: hidden;
}

.battle-results .timeline-container .tracker {
  position: absolute;
  width: 1px;
  height: 100%;
  border-left: 1px solid #000;
  top: 0;
  z-index: 10;
}

.battle-results .timeline-container.sandbox-mode {
  background-color: rgba(255, 240, 200, 0.75);
  border: 1px solid #ffcf01;
}

.battle-results .timeline-container.zoom {
  height: 90px;
  overflow-x: scroll;
}

.battle-results .sandbox-btn-container {
  max-width: 95%;
  margin: 0 auto;
}

.battle-results .sandbox-btn-container .sandbox-btn {
  -webkit-transition: background 0.25s 0s ease-out;
  -moz-transition: background 0.25s 0s ease-out;
  -o-transition: background 0.25s 0s ease-out;
  transition: background 0.25s 0s ease-out;
  position: relative;
  background: #888;
  color: #f9fdff;
  border-radius: 12px;
  font-size: 17px;
  line-height: 16px;
  text-decoration: none;
  cursor: pointer;
  padding: 1px 8px;
  margin-bottom: 5px;
  float: right;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -moz-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.battle-results .sandbox-btn-container .sandbox-btn span {
  position: relative;
  z-index: 5;
}

.battle-results .sandbox-btn-container .sandbox-btn.active {
  color: #111;
  background: #ffcf01;
}

.battle-results .sandbox-btn-container .sandbox {
  width: 19px;
  height: 19px;
  border-radius: 12px;
  background: #ffcf01;
  float: right;
  margin: 0 10px;
  cursor: pointer;
  -moz-transform: scale(0, 0);
  -webkit-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  transform: scale(0, 0);
  -webkit-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -moz-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  -o-transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
  transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
}

.battle-results .sandbox-btn-container .sandbox.active {
  -moz-transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
  -o-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
}

.battle-results .sandbox-btn-container .clear-btn:after {
  content: "↻";
}

.battle-results .playback {
  max-width: 300px;
  margin: 10px auto;
  padding: 2px 10px;
}

.battle-results .playback .flex {
  display: flex;
  justify-content: space-between;
}

.battle-results .playback .playback-btn {
  width: 16px;
  height: 19px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.battle-results .playback .playback-btn.play {
  background-image: url("/img/playback_play.png");
}

.battle-results .playback .playback-btn.play.active {
  background-image: url("/img/playback_pause.png");
}

.battle-results .playback .playback-btn.replay {
  background-image: url("/img/playback_replay.png");
}

.battle-results .playback .playback-speed,
.battle-results .playback .playback-scale {
  padding: 0;
  background: none;
  border-radius: 8px;
  width: 85px;
  border: 1px solid #ccc;
  font-size: 12px;
}

.battle-results .playback .disclaimer {
  display: none;
  font-size: 12px;
  margin-top: 10px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.battle-results .summary {
  display: inline-block;
  margin: 15px auto;
  font-size: 18px;
  line-height: 26px;
}

.battle-results .tip {
  margin: 15px;
  text-align: center;
  font-size: 12px;
}

.battle-results .tip a {
  color: #3e7dd4;
  text-decoration: none;
}

.battle-results .tip a:hover {
  text-decoration: underline;
}

/* Battle section - match original */
.section.battle {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

/* Battle buttons - match original styling exactly */
.button {
  position: relative;
  display: block;
  font-size: 18px;
  margin: 10px auto;
  border-radius: 30px;
  border: 2px solid #000;
  padding: 10px 18px;
  cursor: pointer;
  text-decoration: none;
  color: #000;
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.25s ease-out 0s;
}

.button.update-btn {
  display: none;
}

.button span.btn-content-wrap {
  display: flex;
  align-items: center;
}

.button span.btn-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: 100%;
  margin-right: 8px;
}

.button span.btn-icon.btn-icon-battle {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDJMMTEuMDkgNy4yNkwxNyA4TDExLjA5IDEzLjc0TDEwIDE4TDguOTEgMTMuNzRMMyA4TDguOTEgNy4yNkwxMCAyWiIgZmlsbD0iIzAwMzQ2MiIvPgo8L3N2Zz4K");
}

.button span.btn-label {
  flex: 1;
}

.button:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  pointer-events: none;
  border-radius: 50px;
  transition: background-color 0.25s ease-out 0s;
}

.button:hover {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.4);
  transition: box-shadow 0s ease-out 0s;
}

.button:hover:after {
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0s ease-out 0s;
}

.button:disabled {
  background: #ccc !important;
  color: #666;
  cursor: not-allowed;
  border-color: #999;
}

.button:disabled:hover {
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
}

.button:disabled:after {
  background: none;
}

/* Tooltip */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  display: none;
}

/* Battle results section - match original exactly */
.battle-results {
  display: none;
  text-align: center;
}

.battle-results.single {
  display: block;
}

/* Sandbox controls - match original exactly */
.sandbox-btn-container {
  max-width: 95%;
  margin: 0 auto;
}

.sandbox-btn-container .sandbox-btn {
  transition: background 0.25s 0s ease-out;
  position: relative;
  background: #888;
  color: #f9fdff;
  border-radius: 12px;
  font-size: 17px;
  line-height: 16px;
  text-decoration: none;
  cursor: pointer;
  padding: 1px 8px;
  margin-bottom: 5px;
  float: right;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -moz-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
}

.sandbox-btn-container .sandbox-btn span {
  position: relative;
  z-index: 5;
}

.sandbox-btn-container .sandbox-btn.active {
  color: #111;
  background: #ffcf01;
}

.sandbox-btn-container .sandbox-btn.active .btn-background {
  left: 0;
}

.sandbox-btn-container .sandbox {
  width: 19px;
  height: 19px;
  border-radius: 12px;
  background: #ffcf01;
  float: right;
  margin: 0 10px;
  cursor: pointer;
  transform: scale(0, 0);
  transition: transform 0.3s cubic-bezier(0.64, 0.57, 0.67, 1.53) 0s;
}

.sandbox-btn-container .sandbox.active {
  transform: scale(1, 1);
}

.sandbox-btn-container .clear-btn:after {
  content: "↻";
}

.clear {
  clear: both;
}

/* Timeline container - match original exactly */
.battle-results .timeline-container {
  max-width: 95%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.5);
  height: 80px;
  border-radius: 8px;
  border: 1px solid #000;
  position: relative;
  overflow: hidden;
}

.battle-results .timeline-container .tracker {
  position: absolute;
  width: 1px;
  height: 100%;
  border-left: 1px solid #000;
  top: 0;
  z-index: 10;
}

.battle-results .timeline-container.sandbox-mode {
  background-color: rgba(255, 240, 200, 0.75);
  border: 1px solid #ffcf01;
}

.battle-results .timeline-container.sandbox-mode .item.charged:hover,
.battle-results .timeline-container.sandbox-mode .item.interaction:hover,
.battle-results .timeline-container.sandbox-mode .item.fast:not(.disabled):hover {
  background: #f9a01b; /* Old browsers */
  background: -moz-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #ffcf01 30%, #f9a01b 99%) !important; /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #ffcf01 30%, #f9a01b 99%) !important; /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  border-color: #f9a01b;
}

.battle-results .timeline-container.sandbox-mode .item.disabled {
  opacity: 0.5;
}

.battle-results .timeline-container.sandbox-mode .item.disabled:hover {
  cursor: initial;
}

.battle-results .timeline-container.sandbox-mode .item.interaction {
  display: block !important;
  border: 2px solid #000;
  cursor: pointer;
}

.battle-results .timeline-container.sandbox-mode .item.interaction.disabled {
  cursor: pointer;
  border: 2px solid #666;
}

.battle-results .timeline-container.sandbox-mode .item.interaction.both {
  border: 3px solid #000;
}

.battle-results .timeline-container.sandbox-mode .item.interaction.wait {
  border-style: dotted;
}

.battle-results .timeline-container.zoom {
  height: 90px;
  overflow-x: scroll;
}

/* Timeline scale modes */
.timeline-container.scale {
  /* Scale to fit mode - default */
}

.timeline-container.zoom {
  /* Zoom mode */
  overflow-x: auto;
}

/* Playback controls - match original exactly */
.battle-results .playback {
  max-width: 300px;
  margin: 10px auto;
  padding: 2px 10px;
}

.battle-results .playback .flex {
  display: flex;
  justify-content: space-between;
}

.battle-results .playback .playback-btn {
  width: 16px;
  height: 19px;
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.battle-results .playback .playback-btn.play {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTkiIHZpZXdCb3g9IjAgMCAxNiAxOSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgMkwxNCA5LjVMMiAxN1YyWiIgZmlsbD0iIzMzMyIvPgo8L3N2Zz4K");
}

.battle-results .playback .playback-btn.play.active {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTkiIHZpZXdCb3g9IjAgMCAxNiAxOSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMiIgeT0iMiIgd2lkdGg9IjQiIGhlaWdodD0iMTUiIGZpbGw9IiMzMzMiLz4KPHJlY3QgeD0iMTAiIHk9IjIiIHdpZHRoPSI0IiBoZWlnaHQ9IjE1IiBmaWxsPSIjMzMzIi8+Cjwvc3ZnPgo=");
}

.battle-results .playback .playback-btn.replay {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTkiIHZpZXdCb3g9IjAgMCAxNiAxOSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkM0LjY5IDIgMiA0LjY5IDIgOEMyIDExLjMxIDQuNjkgMTQgOCAxNEMxMS4zMSAxNCAxNCA11LjMxIDE0IDhIMTJDMTIgMTAuMjEgMTAuMjEgMTIgOCAxMkM1Ljc5IDEyIDQgMTAuMjEgNCA4QzQgNS43OSA1Ljc5IDQgOCA0QzkuMDQgNCA5Ljk4IDQuNDIgMTAuNjkgNS4xTDkgN0gxNFYyTDEyLjMxIDMuNjlDMTEuMjQgMi42MyA5LjY5IDIgOCAyWiIgZmlsbD0iIzMzMyIvPgo8L3N2Zz4K");
}

.battle-results .playback .playback-speed,
.battle-results .playback .playback-scale {
  padding: 0;
  background: none;
  border-radius: 8px;
  width: 85px;
  border: 1px solid #ccc;
  font-size: 12px;
}

.battle-results .playback .disclaimer {
  display: none;
  font-size: 12px;
  margin-top: 10px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Summary section - match original exactly */
.battle-results .summary {
  display: inline-block;
  margin: 15px auto;
  font-size: 18px;
  line-height: 26px;
}

.battle-results .summary .battle-summary-line {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.battle-results .summary .battle-summary-line span {
  margin-right: 6px;
}

.battle-results .summary .battle-summary-line span.time {
  margin-left: 6px;
}

.battle-results .summary .battle-summary-line span.rating {
  margin: 0 0 0 6px;
  padding: 2px 8px;
  border-radius: 30px;
}

.battle-results .summary .battle-summary-line span.rating span {
  padding: 0;
}

.battle-results .summary span.time,
.battle-results .summary span.name {
  padding: 0;
}

.battle-results .summary .disclaimer {
  margin: 15px 0;
  font-size: 14px;
  text-align: left;
}

.battle-results .summary p {
  font-size: 14px;
}

/* Tips - match original exactly */
.battle-results .tip {
  margin: 15px;
  text-align: center;
  font-size: 12px;
}

.tip.automated {
  /* Default tip styling */
}

.tip.sandbox {
  display: none; /* Hidden by default, shown in sandbox mode */
}

.tip a {
  color: #3e7dd4;
  text-decoration: none;
}

.tip a:hover {
  text-decoration: underline;
}

/* Share Link Section - match original exactly */
.share-link-container {
  text-align: center;
  margin-top: 20px;
}

.share-link-container p {
  margin: 0;
}

.share-link {
  display: inline-block;
  background: #6296be;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDJMMTEuMDkgNy4yNkwxNyA4TDExLjA5IDEzLjc0TDEwIDE4TDguOTEgMTMuNzRMMyA4TDguOTEgNy4yNkwxMCAyWiIgZmlsbD0iI2Y5ZmRmZiIvPgo8L3N2Zz4K");
  background-repeat: no-repeat;
  background-position: 6px center;
  background-size: 20px;
  padding: 5px 5px 5px 35px;
  border-radius: 8px;
  margin: 5px auto;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);
}

.share-link input {
  width: auto;
  display: inline-block;
  border: none;
  background: #003462;
  padding: 5px;
  color: #f9fdff;
  font-size: 12px;
  min-width: 200px;
}

.share-link .copy {
  display: inline-block;
  font-weight: bold;
  font-size: 13px;
  cursor: pointer;
  color: #f9fdff;
  margin: 5px;
  border-radius: 8px;
  padding: 2px 6px;
  transition: background-color 0.2s ease;
}

.share-link .copy:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Continue Container - match original exactly */
.continue-container {
  text-align: center;
  margin-top: 20px;
}

.continue-container p {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.continue-container .name {
  font-weight: bold;
}

.continue-container .button {
  display: inline-block;
  cursor: pointer;
}

/* Pokemon selection */
.poke-select-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

/* Battle button */
.battle-btn {
  background-color: #3e7dd4;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.battle-btn:hover {
  background-color: #3569b4;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-content-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  margin-right: 8px;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
}

.btn-icon-battle {
  background-image: url('/images/battle-icon.png');
}

/* Battle results */
.battle-results {
  margin-top: 20px;
}

.sandbox-btn-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.sandbox-btn {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.sandbox-btn.active .btn-background {
  background-color: #3e7dd4;
}

.btn-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  transition: background-color 0.2s ease;
  z-index: -1;
}

.clear-btn {
  width: 24px;
  height: 24px;
  margin-left: 10px;
  background-image: url('/images/clear-icon.png');
  background-size: contain;
  cursor: pointer;
}

/* Timeline */
.timeline-container {
  position: relative;
  height: 100px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 15px;
  overflow: hidden;
}

.timeline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
}

.timeline:nth-child(2) {
  top: 50%;
}

.tracker {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background-color: #ff0000;
  z-index: 10;
}

/* Playback controls */
.playback {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.playback-btn {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  background-size: contain;
  cursor: pointer;
}

.play {
  background-image: url('/images/play-icon.png');
}

.replay {
  background-image: url('/images/replay-icon.png');
}

.playback-speed, .playback-scale {
  margin-right: 10px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.disclaimer {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* Share link */
.share-link-container {
  margin-top: 20px;
}

.share-link {
  display: flex;
  margin-top: 5px;
}

.share-link input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-right: none;
  border-radius: 4px 0 0 4px;
}

.share-link .copy {
  padding: 8px 15px;
  background-color: #3e7dd4;
  color: white;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

/* Continue battle */
.continue-container {
  margin-top: 20px;
}

.continue-container .button {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 15px;
  background-color: #3e7dd4;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

/* Toggle sections */
.toggle {
  display: block;
  padding: 10px 0;
  color: #333;
  font-weight: bold;
  text-decoration: none;
  position: relative;
}

.toggle .arrow-down {
  display: inline-block;
}

.toggle .arrow-up {
  display: none;
}

.toggle.active .arrow-down {
  display: none;
}

.toggle.active .arrow-up {
  display: inline-block;
}

.toggle-content {
  display: none;
  padding: 10px 0;
}

.toggle-content.active {
  display: block;
}

/* Center alignment */
.center {
  text-align: center;
}

/* Rating table */
.rating-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
}

.rating-table td {
  padding: 8px;
  text-align: center;
  border: 1px solid #ddd;
}

.rating {
  display: inline-block;
  padding: 3px 6px;
  border-radius: 3px;
  font-weight: bold;
}

.shield {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('/images/shield-icon.png');
  background-size: contain;
  vertical-align: middle;
}

.shield-none {
  opacity: 0.3;
}

/* Matrix mode */
.matrix-controls {
  margin-bottom: 15px;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
}

.matrix-table td, .matrix-table th {
  padding: 5px;
  text-align: center;
  border: 1px solid #ddd;
}

/* Sandbox mode */
.sandbox-move-select {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  width: 90%;
  max-width: 500px;
}

.move-select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.move-stats {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.move-stats > div {
  margin-right: 15px;
  margin-bottom: 5px;
}

.check {
  margin: 10px 0;
  cursor: pointer;
}

.check span {
  display: inline-block;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
}

.check.on span {
  background-color: #3e7dd4;
  background-image: url('/images/check-icon.png');
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.sandbox-clear-confirm {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.flex {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .poke-select-container {
    flex-direction: column;
  }
  
  .ranking-categories a {
    padding: 6px 12px;
    font-size: 14px;
  }
  
  .move-stats {
    flex-direction: column;
  }
  
  .move-stats > div {
    margin-right: 0;
  }
}