import type { BattlePokemon, Move } from './pokemon';

export interface DamageResult {
  damage: number;
  effectiveness: number;
  isCritical: boolean;
}

export interface BattleEvent {
  turn: number;
  actor: 'pokemon1' | 'pokemon2';
  action: 'fast' | 'charged' | 'switch' | 'shield';
  move?: Move;
  damage?: number;
  energy?: number;
  hp?: number;
}

export interface BattlePokemonResult {
  pokemon: BattlePokemon;
  remainingHp: number;
  energyUsed: number;
  damageDealt: number;
  damageTaken: number;
  movesUsed: {
    fast: number;
    charged: number;
  };
}

export interface BattleResult {
  winner: 'pokemon1' | 'pokemon2' | 'tie';
  pokemon1: BattlePokemonResult;
  pokemon2: BattlePokemonResult;
  timeline: BattleEvent[];
  duration: number;
  turns: number;
}

export interface BattleOptions {
  shields: {
    pokemon1: number;
    pokemon2: number;
  };
  strategy: 'neutral' | 'aggressive' | 'defensive';
  allowSwitching: boolean;
  timeLimit?: number;
  battleFormat?: 'single' | 'multi' | 'matrix';
  energyAdvantage?: number;
  moveTimingAdvantage?: number;
  scenario?: 'lead' | 'switch' | 'closer';
  sandboxMode?: boolean;
}

export interface TypeEffectiveness {
  [attackingType: string]: {
    [defendingType: string]: number;
  };
}

// Multi-battle result
export interface MultiBattleResult {
  id: string;
  opponent: BattlePokemon;
  battleRating: number;
  win: boolean;
  result: BattleResult;
}

// Matrix battle result
export interface MatrixBattleResult {
  grid: {
    [team1PokemonId: string]: {
      [team2PokemonId: string]: {
        battleRating: number;
        win: boolean;
      }
    }
  };
  team1Pokemon: BattlePokemon[];
  team2Pokemon: BattlePokemon[];
  results: {
    [matchupId: string]: BattleResult;
  };
}

// Sandbox action
export interface SandboxAction {
  turn: number;
  actor: 'pokemon1' | 'pokemon2';
  actionType: 'fast' | 'charged' | 'shield' | 'wait' | 'switch';
  moveId?: string;
  chargeLevel?: 0 | 1 | 2 | 3 | 4; // 0 = 100%, 1 = 95%, 2 = 75%, 3 = 50%, 4 = 25%
  shielded?: boolean;
  applyBuffs?: boolean;
  switchPokemonIndex?: number;
}