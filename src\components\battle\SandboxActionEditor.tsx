import { useState, useEffect } from 'react';
import { SandboxAction } from '@/types/battle';
import { Move } from '@/types/pokemon';
import clsx from 'clsx';

interface SandboxActionEditorProps {
  action: SandboxAction | null;
  availableMoves: {
    pokemon1: {
      fast: Move[];
      charged: Move[];
    };
    pokemon2: {
      fast: Move[];
      charged: Move[];
    };
  };
  onSave: (action: SandboxAction) => void;
  onCancel: () => void;
  turn: number;
}

export function SandboxActionEditor({
  action,
  availableMoves,
  onSave,
  onCancel,
  turn,
}: SandboxActionEditorProps) {
  const [editedAction, setEditedAction] = useState<SandboxAction>({
    turn,
    actor: 'pokemon1',
    actionType: 'fast',
  });

  // Initialize with provided action or defaults
  useEffect(() => {
    if (action) {
      setEditedAction(action);
    } else {
      setEditedAction({
        turn,
        actor: 'pokemon1',
        actionType: 'fast',
      });
    }
  }, [action, turn]);

  const handleActionTypeChange = (actionType: 'fast' | 'charged' | 'shield' | 'wait' | 'switch') => {
    setEditedAction((prev) => ({
      ...prev,
      actionType,
      // Reset move-specific properties when changing action type
      moveId: actionType === 'fast' || actionType === 'charged' ? prev.moveId : undefined,
      chargeLevel: actionType === 'charged' ? prev.chargeLevel : undefined,
      shielded: actionType === 'charged' ? prev.shielded : undefined,
      applyBuffs: actionType === 'charged' ? prev.applyBuffs : undefined,
      switchPokemonIndex: actionType === 'switch' ? prev.switchPokemonIndex : undefined,
    }));
  };

  const handleSave = () => {
    onSave(editedAction);
  };

  // Get available moves based on actor and action type
  const getAvailableMoves = () => {
    if (editedAction.actionType === 'fast') {
      return editedAction.actor === 'pokemon1'
        ? availableMoves.pokemon1.fast
        : availableMoves.pokemon2.fast;
    } else if (editedAction.actionType === 'charged') {
      return editedAction.actor === 'pokemon1'
        ? availableMoves.pokemon1.charged
        : availableMoves.pokemon2.charged;
    }
    return [];
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Action - Turn {turn}</h3>

      {/* Actor Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Pokemon</label>
        <div className="flex space-x-2">
          <button
            className={clsx(
              'px-4 py-2 rounded-md text-sm font-medium',
              editedAction.actor === 'pokemon1'
                ? 'bg-blue-100 text-blue-800 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => setEditedAction((prev) => ({ ...prev, actor: 'pokemon1' }))}
          >
            Pokemon 1
          </button>
          <button
            className={clsx(
              'px-4 py-2 rounded-md text-sm font-medium',
              editedAction.actor === 'pokemon2'
                ? 'bg-red-100 text-red-800 border border-red-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => setEditedAction((prev) => ({ ...prev, actor: 'pokemon2' }))}
          >
            Pokemon 2
          </button>
        </div>
      </div>

      {/* Action Type Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Action Type</label>
        <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 sm:gap-3">
          <button
            className={clsx(
              'px-3 py-2 rounded-md text-sm font-medium',
              editedAction.actionType === 'fast'
                ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => handleActionTypeChange('fast')}
          >
            Fast Move
          </button>
          <button
            className={clsx(
              'px-3 py-2 rounded-md text-sm font-medium',
              editedAction.actionType === 'charged'
                ? 'bg-red-100 text-red-800 border border-red-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => handleActionTypeChange('charged')}
          >
            Charged Move
          </button>
          <button
            className={clsx(
              'px-3 py-2 rounded-md text-sm font-medium',
              editedAction.actionType === 'shield'
                ? 'bg-blue-100 text-blue-800 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => handleActionTypeChange('shield')}
          >
            Shield
          </button>
          <button
            className={clsx(
              'px-3 py-2 rounded-md text-sm font-medium',
              editedAction.actionType === 'wait'
                ? 'bg-gray-300 text-gray-800 border border-gray-400'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => handleActionTypeChange('wait')}
          >
            Wait
          </button>
          <button
            className={clsx(
              'px-3 py-2 rounded-md text-sm font-medium',
              editedAction.actionType === 'switch'
                ? 'bg-purple-100 text-purple-800 border border-purple-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            )}
            onClick={() => handleActionTypeChange('switch')}
          >
            Switch
          </button>
        </div>
      </div>

      {/* Move Selection (for Fast and Charged) */}
      {(editedAction.actionType === 'fast' || editedAction.actionType === 'charged') && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {editedAction.actionType === 'fast' ? 'Fast Move' : 'Charged Move'}
          </label>
          <select
            value={editedAction.moveId || ''}
            onChange={(e) => setEditedAction((prev) => ({ ...prev, moveId: e.target.value }))}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select a move</option>
            {getAvailableMoves().map((move) => (
              <option key={move.moveId} value={move.moveId}>
                {move.name} ({move.type})
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Charge Level (for Charged only) */}
      {editedAction.actionType === 'charged' && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Charge Level</label>
          <select
            value={editedAction.chargeLevel !== undefined ? editedAction.chargeLevel : 0}
            onChange={(e) =>
              setEditedAction((prev) => ({ ...prev, chargeLevel: Number(e.target.value) as 0 | 1 | 2 | 3 | 4 }))
            }
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={0}>100% Charge</option>
            <option value={1}>95% Charge</option>
            <option value={2}>75% Charge</option>
            <option value={3}>50% Charge</option>
            <option value={4}>25% Charge</option>
          </select>
        </div>
      )}

      {/* Shield Option (for Charged only) */}
      {editedAction.actionType === 'charged' && (
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={editedAction.shielded || false}
              onChange={(e) => setEditedAction((prev) => ({ ...prev, shielded: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">Shield this attack</span>
          </label>
        </div>
      )}

      {/* Apply Buffs Option (for Charged only) */}
      {editedAction.actionType === 'charged' && (
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={editedAction.applyBuffs || false}
              onChange={(e) => setEditedAction((prev) => ({ ...prev, applyBuffs: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">Apply buffs/debuffs</span>
          </label>
        </div>
      )}

      {/* Switch Pokemon Index (for Switch only) */}
      {editedAction.actionType === 'switch' && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Switch to Pokemon</label>
          <select
            value={editedAction.switchPokemonIndex !== undefined ? editedAction.switchPokemonIndex : 0}
            onChange={(e) =>
              setEditedAction((prev) => ({ ...prev, switchPokemonIndex: Number(e.target.value) }))
            }
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={0}>Pokemon 1</option>
            <option value={1}>Pokemon 2</option>
            <option value={2}>Pokemon 3</option>
          </select>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6">
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Save Action
        </button>
      </div>
    </div>
  );
}