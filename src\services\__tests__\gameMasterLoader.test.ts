import { GameMasterLoader } from '../gameMasterLoader';

// Mock fs module for testing
jest.mock('fs', () => ({
  readFileSync: jest.fn(),
  readdirSync: jest.fn(),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  copyFileSync: jest.fn(),
  writeFileSync: jest.fn(),
}));

// Mock path module
jest.mock('path', () => ({
  join: (...args: string[]) => args.join('/'),
}));

describe('GameMasterLoader', () => {
  let loader: GameMasterLoader;
  const mockFs = require('fs');

  beforeEach(() => {
    loader = new GameMasterLoader('test-data');
    jest.clearAllMocks();
  });

  describe('validateGameMasterData', () => {
    it('should validate correct gamemaster data structure', () => {
      const validData = {
        timestamp: '2023-01-01T00:00:00Z',
        settings: {
          partySize: 3,
          maxBuffStages: 4,
          buffDivisor: 4,
        },
        pokemon: [
          {
            speciesId: 'charizard',
            speciesName: 'Charizard',
            dex: 6,
            types: ['fire', 'flying'],
            baseStats: {
              atk: 223,
              def: 173,
              hp: 186,
            },
          },
        ],
        moves: [
          {
            moveId: 'FIRE_FANG',
            name: 'Fire Fang',
            type: 'fire',
            power: 12,
            energy: 8,
          },
        ],
      };

      const isValid = loader.validateGameMasterData(validData);
      expect(isValid).toBe(true);
    });

    it('should reject data missing required fields', () => {
      const invalidData = {
        timestamp: '2023-01-01T00:00:00Z',
        // Missing settings, pokemon, moves
      };

      const isValid = loader.validateGameMasterData(invalidData);
      expect(isValid).toBe(false);
    });

    it('should reject data with invalid pokemon structure', () => {
      const invalidData = {
        timestamp: '2023-01-01T00:00:00Z',
        settings: { partySize: 3, maxBuffStages: 4, buffDivisor: 4 },
        pokemon: 'not an array',
        moves: [],
      };

      const isValid = loader.validateGameMasterData(invalidData);
      expect(isValid).toBe(false);
    });

    it('should reject data with invalid moves structure', () => {
      const invalidData = {
        timestamp: '2023-01-01T00:00:00Z',
        settings: { partySize: 3, maxBuffStages: 4, buffDivisor: 4 },
        pokemon: [],
        moves: 'not an array',
      };

      const isValid = loader.validateGameMasterData(invalidData);
      expect(isValid).toBe(false);
    });

    it('should validate pokemon entries when present', () => {
      const invalidData = {
        timestamp: '2023-01-01T00:00:00Z',
        settings: { partySize: 3, maxBuffStages: 4, buffDivisor: 4 },
        pokemon: [
          {
            // Missing required fields
            name: 'Charizard',
          },
        ],
        moves: [],
      };

      const isValid = loader.validateGameMasterData(invalidData);
      expect(isValid).toBe(false);
    });

    it('should validate move entries when present', () => {
      const invalidData = {
        timestamp: '2023-01-01T00:00:00Z',
        settings: { partySize: 3, maxBuffStages: 4, buffDivisor: 4 },
        pokemon: [],
        moves: [
          {
            // Missing required fields
            name: 'Fire Fang',
          },
        ],
      };

      const isValid = loader.validateGameMasterData(invalidData);
      expect(isValid).toBe(false);
    });
  });

  describe('getDataStats', () => {
    it('should return correct statistics', () => {
      const data = {
        timestamp: '2023-01-01T00:00:00Z',
        pokemon: [1, 2, 3], // Mock array with 3 items
        moves: [1, 2], // Mock array with 2 items
        shadowPokemon: [1], // Mock array with 1 item
        cups: [1, 2, 3, 4], // Mock array with 4 items
      };

      const stats = loader.getDataStats(data);

      expect(stats).toEqual({
        pokemonCount: 3,
        moveCount: 2,
        shadowPokemonCount: 1,
        cupCount: 4,
        timestamp: '2023-01-01T00:00:00Z',
      });
    });

    it('should handle missing data gracefully', () => {
      const data = {
        timestamp: '2023-01-01T00:00:00Z',
        // Missing arrays
      };

      const stats = loader.getDataStats(data);

      expect(stats).toEqual({
        pokemonCount: 0,
        moveCount: 0,
        shadowPokemonCount: 0,
        cupCount: 0,
        timestamp: '2023-01-01T00:00:00Z',
      });
    });

    it('should handle missing timestamp', () => {
      const data = {
        pokemon: [],
        moves: [],
        shadowPokemon: [],
        cups: [],
      };

      const stats = loader.getDataStats(data);

      expect(stats.timestamp).toBe('unknown');
    });
  });

  describe('server-side operations', () => {
    beforeEach(() => {
      // Mock that we're in a Node.js environment
      Object.defineProperty(global, 'window', {
        value: undefined,
        writable: true,
      });
    });

    it('should compile gamemaster from separate files', async () => {
      // Mock file system responses
      mockFs.readFileSync
        .mockReturnValueOnce(JSON.stringify({ // base.json
          timestamp: '',
          settings: { partySize: 3, maxBuffStages: 4, buffDivisor: 4 },
          shadowPokemon: ['charizard'],
        }))
        .mockReturnValueOnce(JSON.stringify([{ // pokemon.json
          speciesId: 'charizard',
          speciesName: 'Charizard',
          dex: 6,
        }]))
        .mockReturnValueOnce(JSON.stringify([{ // moves.json
          moveId: 'FIRE_FANG',
          name: 'Fire Fang',
        }]))
        .mockReturnValueOnce(JSON.stringify([])); // formats.json

      mockFs.readdirSync.mockReturnValue(['test.json']);

      const compiled = await loader.compileGameMaster();

      expect(compiled).toHaveProperty('timestamp');
      expect(compiled).toHaveProperty('pokemon');
      expect(compiled).toHaveProperty('moves');
      expect(compiled).toHaveProperty('settings');
      expect(compiled.shadowPokemon).toEqual(['charizard']);
    });

    it('should handle compilation errors gracefully', async () => {
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File not found');
      });

      await expect(loader.compileGameMaster()).rejects.toThrow();
    });
  });

  describe('client-side operations', () => {
    beforeEach(() => {
      // Mock that we're in a browser environment
      Object.defineProperty(global, 'window', {
        value: {},
        writable: true,
      });
    });

    it('should throw error for compilation on client-side', async () => {
      await expect(loader.compileGameMaster()).rejects.toThrow(
        'GameMaster compilation not supported on client-side'
      );
    });

    it('should throw error for file copying on client-side', async () => {
      await expect(loader.copyToPublic()).rejects.toThrow(
        'File copying not supported on client-side'
      );
    });
  });
});
