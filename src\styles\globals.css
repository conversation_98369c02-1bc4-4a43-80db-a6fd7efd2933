@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-pvpoke-background text-gray-900;
  }
}

@layer components {
  /* Pokemon type badges */
  .type-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white;
  }
  
  .type-normal { @apply bg-normal; }
  .type-fire { @apply bg-fire; }
  .type-water { @apply bg-water; }
  .type-electric { @apply bg-electric; }
  .type-grass { @apply bg-grass; }
  .type-ice { @apply bg-ice; }
  .type-fighting { @apply bg-fighting; }
  .type-poison { @apply bg-poison; }
  .type-ground { @apply bg-ground; }
  .type-flying { @apply bg-flying; }
  .type-psychic { @apply bg-psychic; }
  .type-bug { @apply bg-bug; }
  .type-rock { @apply bg-rock; }
  .type-ghost { @apply bg-ghost; }
  .type-dragon { @apply bg-dragon; }
  .type-dark { @apply bg-dark; }
  .type-steel { @apply bg-steel; }
  .type-fairy { @apply bg-fairy; }
  
  /* Pokemon card styles */
  .pokemon-card {
    @apply bg-white rounded-xl shadow-pokemon border border-gray-200 hover:shadow-pokemon-lg transition-shadow duration-200;
  }
  
  /* Button styles */
  .btn-primary {
    @apply bg-pvpoke-primary hover:bg-pvpoke-secondary text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  /* League badges */
  .league-great { @apply bg-league-great; }
  .league-ultra { @apply bg-league-ultra; }
  .league-master { @apply bg-league-master; }
  .league-little { @apply bg-league-little; }
  .league-premier { @apply bg-league-premier; }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}
