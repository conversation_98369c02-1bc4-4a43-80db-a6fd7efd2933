import { Pokemon } from '@/types/pokemon';
import { TypeIcon } from './TypeIcon';

interface PokemonCardProps {
  pokemon: Pokemon;
  onClick?: () => void;
  className?: string;
}

export function PokemonCard({ pokemon, onClick, className = '' }: PokemonCardProps) {
  return (
    <div
      className={`pokemon-card p-4 cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-xs font-medium text-gray-600">
              #{pokemon.dex}
            </span>
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 truncate">
            {pokemon.speciesName}
          </h3>
          
          <div className="flex space-x-1 mt-1">
            {pokemon.types.map((type) => (
              <TypeIcon key={type} type={type} />
            ))}
          </div>
        </div>
        
        <div className="flex-shrink-0 text-right">
          <div className="text-sm text-gray-500">
            <div>ATK: {pokemon.baseStats.attack}</div>
            <div>DEF: {pokemon.baseStats.defense}</div>
            <div>STA: {pokemon.baseStats.stamina}</div>
          </div>
        </div>
      </div>
      
      {pokemon.tags.length > 0 && (
        <div className="mt-3 flex flex-wrap gap-1">
          {pokemon.tags.map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
    </div>
  );
}
